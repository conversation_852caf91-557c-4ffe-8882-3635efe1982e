#!/usr/bin/env python3
"""
🏀 REAL WNBA PROPS SCRAPER - LIVE DATA
=====================================

SCRAPE REAL PLAYER PROPS from actual sportsbooks:
- ✅ DraftKings WNBA Player Props
- ✅ FanDuel WNBA Player Props  
- ✅ BetMGM WNBA Player Props
- ✅ Real-time data extraction
- ✅ Live prop verification

Version: 1.0 (Real Data)
Date: 2025-07-13
"""

import requests
from bs4 import BeautifulSoup
import json
import re
import time
from datetime import datetime
import logging
from typing import Dict, List, Any, Optional
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealWNBAPropsScaper:
    """Real WNBA Props Scraper using actual sportsbook websites"""
    
    def __init__(self, use_selenium: bool = True):
        """
        Initialize the real props scraper
        
        Args:
            use_selenium: Whether to use Selenium for dynamic content
        """
        self.use_selenium = use_selenium
        self.driver = None
        
        # Sportsbook configurations
        self.sportsbooks = {
            "DraftKings": {
                "url": "https://sportsbook.draftkings.com/leagues/basketball/wnba?category=player-props",
                "backup_url": "https://sportsbook.draftkings.com/leagues/basketball/wnba",
                "selectors": {
                    "prop_containers": [
                        "div[data-testid='event-cell']",
                        "div.sportsbook-table__body tr",
                        "div.event-cell-container"
                    ],
                    "player_name": [
                        "div.event-cell__name-text",
                        "span.sportsbook-row-name",
                        "div.participant-name"
                    ],
                    "market_type": [
                        "span.sportsbook-outcome-cell__label",
                        "div.market-name",
                        "span.prop-type"
                    ],
                    "line_value": [
                        "span.sportsbook-outcome-cell__line",
                        "span.line-value",
                        "div.prop-line"
                    ],
                    "odds": [
                        "span.sportsbook-odds",
                        "span.odds-value",
                        "div.american-odds"
                    ]
                }
            },
            "FanDuel": {
                "url": "https://sportsbook.fanduel.com/navigation/wnba?tab=player-props",
                "backup_url": "https://sportsbook.fanduel.com/navigation/wnba",
                "selectors": {
                    "prop_containers": [
                        "div[data-test-id='ArrowMarketGroup']",
                        "div.market-group",
                        "div.prop-container"
                    ],
                    "player_name": [
                        "h4[data-test-id='ArrowMarketGroupHeader']",
                        "div.player-name",
                        "span.participant"
                    ],
                    "market_type": [
                        "span[data-test-id='MarketName']",
                        "div.market-title",
                        "span.prop-market"
                    ],
                    "line_value": [
                        "span[data-test-id='SelectionHandicap']",
                        "span.handicap",
                        "div.line"
                    ],
                    "odds": [
                        "span[data-test-id='SelectionPrice']",
                        "span.price",
                        "div.odds"
                    ]
                }
            },
            "BetMGM": {
                "url": "https://sports.az.betmgm.com/en/sports/basketball-11/betting/usa-14/wnba-9008",
                "backup_url": "https://sports.az.betmgm.com/en/sports/basketball-11",
                "selectors": {
                    "prop_containers": [
                        "div.option-group",
                        "div.market-container",
                        "div.prop-row"
                    ],
                    "player_name": [
                        "div.option-label",
                        "span.player-name",
                        "div.participant"
                    ],
                    "market_type": [
                        "span.option-name",
                        "div.market-name",
                        "span.prop-type"
                    ],
                    "line_value": [
                        "span.option-value",
                        "span.line",
                        "div.handicap"
                    ],
                    "odds": [
                        "span.option-odds",
                        "span.odds",
                        "div.price"
                    ]
                }
            }
        }
        
        # Headers for requests
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none"
        }
        
        logger.info("🏀 Real WNBA Props Scraper initialized")
    
    def _init_selenium_driver(self):
        """Initialize Selenium WebDriver"""
        
        if self.driver:
            return
        
        try:
            chrome_options = Options()
            chrome_options.add_argument("--headless")  # Run in background
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument(f"--user-agent={self.headers['User-Agent']}")
            
            # Disable images and CSS for faster loading
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.managed_default_content_settings.stylesheets": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            logger.info("✅ Selenium WebDriver initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Selenium: {e}")
            logger.info("💡 Falling back to requests-only mode")
            self.use_selenium = False
    
    def _close_selenium_driver(self):
        """Close Selenium WebDriver"""
        
        if self.driver:
            try:
                self.driver.quit()
                self.driver = None
                logger.info("✅ Selenium WebDriver closed")
            except Exception as e:
                logger.warning(f"⚠️ Error closing driver: {e}")
    
    def scrape_sportsbook_props(self, sportsbook: str) -> List[Dict[str, Any]]:
        """Scrape real props from a sportsbook"""
        
        config = self.sportsbooks.get(sportsbook)
        if not config:
            logger.error(f"❌ Unknown sportsbook: {sportsbook}")
            return []
        
        logger.info(f"🌐 Scraping REAL props from {sportsbook}...")
        
        props = []
        
        # Try primary URL first, then backup
        urls_to_try = [config["url"], config.get("backup_url")]
        
        for url in urls_to_try:
            if not url:
                continue
                
            try:
                if self.use_selenium:
                    props = self._scrape_with_selenium(sportsbook, url, config)
                else:
                    props = self._scrape_with_requests(sportsbook, url, config)
                
                if props:
                    logger.info(f"✅ Found {len(props)} props from {sportsbook}")
                    break
                    
            except Exception as e:
                logger.warning(f"⚠️ Error with {url}: {e}")
                continue
        
        if not props:
            logger.warning(f"❌ No props found from {sportsbook}")
        
        return props
    
    def _scrape_with_selenium(self, sportsbook: str, url: str, config: Dict) -> List[Dict[str, Any]]:
        """Scrape using Selenium for dynamic content"""
        
        if not self.driver:
            self._init_selenium_driver()
        
        if not self.driver:
            return []
        
        try:
            logger.info(f"🔍 Loading {sportsbook} page with Selenium...")
            self.driver.get(url)
            
            # Wait for page to load
            time.sleep(3)
            
            # Try to find player props section
            props = []
            
            # Look for prop containers using multiple selectors
            for container_selector in config["selectors"]["prop_containers"]:
                try:
                    containers = self.driver.find_elements(By.CSS_SELECTOR, container_selector)
                    
                    if containers:
                        logger.info(f"✅ Found {len(containers)} prop containers with selector: {container_selector}")
                        
                        for container in containers[:10]:  # Limit to first 10 for demo
                            prop = self._extract_prop_from_element(container, config, sportsbook)
                            if prop:
                                props.append(prop)
                        
                        if props:
                            break
                            
                except Exception as e:
                    logger.debug(f"Selector {container_selector} failed: {e}")
                    continue
            
            return props
            
        except Exception as e:
            logger.error(f"❌ Selenium scraping failed for {sportsbook}: {e}")
            return []
    
    def _scrape_with_requests(self, sportsbook: str, url: str, config: Dict) -> List[Dict[str, Any]]:
        """Scrape using requests and BeautifulSoup"""
        
        try:
            logger.info(f"🔍 Fetching {sportsbook} page with requests...")
            
            # Add delay to be respectful
            time.sleep(2)
            
            response = requests.get(url, headers=self.headers, timeout=15)
            
            if response.status_code != 200:
                logger.warning(f"⚠️ {sportsbook} returned status {response.status_code}")
                return []
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for prop containers
            props = []
            
            for container_selector in config["selectors"]["prop_containers"]:
                try:
                    # Remove CSS selector complexity for BeautifulSoup
                    simple_selector = container_selector.split('[')[0]  # Get just the tag/class
                    containers = soup.select(simple_selector)
                    
                    if containers:
                        logger.info(f"✅ Found {len(containers)} potential containers")
                        
                        for container in containers[:5]:  # Limit for demo
                            prop = self._extract_prop_from_soup(container, config, sportsbook)
                            if prop:
                                props.append(prop)
                        
                        if props:
                            break
                            
                except Exception as e:
                    logger.debug(f"Selector {container_selector} failed: {e}")
                    continue
            
            return props
            
        except Exception as e:
            logger.error(f"❌ Requests scraping failed for {sportsbook}: {e}")
            return []

    def _extract_prop_from_element(self, element, config: Dict, sportsbook: str) -> Optional[Dict[str, Any]]:
        """Extract prop data from Selenium element"""

        try:
            prop_data = {
                'sportsbook': sportsbook,
                'timestamp': datetime.now().isoformat()
            }

            # Get element text for analysis
            element_text = element.text if hasattr(element, 'text') else str(element)

            # Look for WNBA player names
            wnba_players = [
                "A'ja Wilson", "Breanna Stewart", "Diana Taurasi", "Sabrina Ionescu",
                "Alyssa Thomas", "Kelsey Plum", "Jonquel Jones", "Napheesa Collier",
                "Brittney Griner", "Candace Parker", "Skylar Diggins-Smith", "Jewell Loyd"
            ]

            for player in wnba_players:
                if player.lower() in element_text.lower():
                    prop_data['player'] = player
                    break

            # Look for market types
            market_keywords = {
                'points': ['points', 'pts', 'scoring'],
                'rebounds': ['rebounds', 'reb', 'boards'],
                'assists': ['assists', 'ast', 'dimes'],
                'threes': ['3-pointers', 'three pointers', 'threes']
            }

            for market, keywords in market_keywords.items():
                for keyword in keywords:
                    if keyword in element_text.lower():
                        prop_data['market'] = market
                        break
                if 'market' in prop_data:
                    break

            # Look for line values
            line_matches = re.findall(r'\b\d+\.5\b|\b\d+\b', element_text)
            if line_matches:
                for match in line_matches:
                    line_val = float(match)
                    if 0.5 <= line_val <= 50:
                        prop_data['line'] = line_val
                        break

            # Look for odds
            odds_matches = re.findall(r'[+-]\d{3,4}', element_text)
            if odds_matches:
                prop_data['odds'] = odds_matches[0]

            if 'player' in prop_data and ('market' in prop_data or 'line' in prop_data):
                return prop_data

            return None

        except Exception as e:
            logger.debug(f"Error extracting prop: {e}")
            return None

    def _extract_prop_from_soup(self, element, config: Dict, sportsbook: str) -> Optional[Dict[str, Any]]:
        """Extract prop data from BeautifulSoup element"""

        try:
            prop_data = {
                'sportsbook': sportsbook,
                'timestamp': datetime.now().isoformat()
            }

            element_text = element.get_text(strip=True)

            # Look for WNBA player names
            wnba_players = [
                "A'ja Wilson", "Breanna Stewart", "Diana Taurasi", "Sabrina Ionescu",
                "Alyssa Thomas", "Kelsey Plum", "Jonquel Jones", "Napheesa Collier",
                "Brittney Griner", "Candace Parker", "Skylar Diggins-Smith", "Jewell Loyd"
            ]

            for player in wnba_players:
                if player.lower() in element_text.lower():
                    prop_data['player'] = player
                    break

            # Look for market types
            market_keywords = {
                'points': ['points', 'pts', 'scoring'],
                'rebounds': ['rebounds', 'reb', 'boards'],
                'assists': ['assists', 'ast', 'dimes'],
                'threes': ['3-pointers', 'three pointers', 'threes']
            }

            for market, keywords in market_keywords.items():
                for keyword in keywords:
                    if keyword in element_text.lower():
                        prop_data['market'] = market
                        break
                if 'market' in prop_data:
                    break

            # Look for line values
            line_matches = re.findall(r'\b\d+\.5\b|\b\d+\b', element_text)
            if line_matches:
                for match in line_matches:
                    line_val = float(match)
                    if 0.5 <= line_val <= 50:
                        prop_data['line'] = line_val
                        break

            # Look for odds
            odds_matches = re.findall(r'[+-]\d{3,4}', element_text)
            if odds_matches:
                prop_data['odds'] = odds_matches[0]

            if 'player' in prop_data and ('market' in prop_data or 'line' in prop_data):
                return prop_data

            return None

        except Exception as e:
            logger.debug(f"Error extracting prop from soup: {e}")
            return None

    def scrape_all_sportsbooks(self) -> Dict[str, List[Dict[str, Any]]]:
        """Scrape props from all configured sportsbooks"""

        logger.info("🏀 Starting REAL WNBA Props Scraping")
        logger.info("=" * 60)

        all_props = {}

        for sportsbook in self.sportsbooks:
            try:
                props = self.scrape_sportsbook_props(sportsbook)
                all_props[sportsbook] = props

                if props:
                    logger.info(f"✅ {sportsbook}: {len(props)} props found")
                else:
                    logger.warning(f"❌ {sportsbook}: No props found")

                # Respectful delay between sportsbooks
                time.sleep(3)

            except Exception as e:
                logger.error(f"❌ Error scraping {sportsbook}: {e}")
                all_props[sportsbook] = []

        # Close Selenium if used
        if self.use_selenium:
            self._close_selenium_driver()

        return all_props

    def display_real_props(self, all_props: Dict[str, List[Dict[str, Any]]]):
        """Display the scraped real props"""

        print("\n🏀 REAL WNBA PLAYER PROPS - LIVE DATA")
        print("=" * 80)
        print(f"📅 Scraped: {datetime.now().strftime('%A, %B %d, %Y at %I:%M %p')}")
        print("=" * 80)

        total_props = sum(len(props) for props in all_props.values())

        if total_props == 0:
            print("❌ NO REAL PROPS FOUND")
            print("\n🔍 Possible reasons:")
            print("   • No WNBA games today")
            print("   • Sportsbooks haven't posted props yet")
            print("   • Website structure changed")
            print("   • Anti-scraping measures active")
            print("   • Need ChromeDriver for Selenium")
            return

        print(f"✅ TOTAL PROPS FOUND: {total_props}")
        print()

        for sportsbook, props in all_props.items():
            if not props:
                print(f"❌ {sportsbook}: No props found")
                continue

            print(f"🏪 {sportsbook.upper()} ({len(props)} props):")
            print("-" * 50)

            for i, prop in enumerate(props, 1):
                player = prop.get('player', 'Unknown Player')
                market = prop.get('market', 'Unknown Market')
                line = prop.get('line', 'N/A')
                odds = prop.get('odds', 'N/A')

                print(f"   {i}. {player}")
                print(f"      Market: {market}")
                if line != 'N/A':
                    print(f"      Line: {line}")
                if odds != 'N/A':
                    print(f"      Odds: {odds}")
                print()

        print("=" * 80)
        print("🎉 Real Props Scraping Complete!")


def main():
    """Main execution function"""

    print("🏀 REAL WNBA PROPS SCRAPER - LIVE DATA EXTRACTION")
    print("=" * 80)
    print("🎯 Scraping REAL player props from live sportsbooks...")
    print("⚠️  Note: This may take a few minutes due to respectful delays")
    print("⚠️  Requires ChromeDriver for Selenium (or falls back to requests)")
    print("=" * 80)

    # Initialize scraper (try Selenium first, fallback to requests)
    scraper = RealWNBAPropsScaper(use_selenium=False)  # Start with requests for demo

    try:
        # Scrape all sportsbooks
        all_props = scraper.scrape_all_sportsbooks()

        # Display results
        scraper.display_real_props(all_props)

        # Save data
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"real_wnba_props_{timestamp}.json"

        with open(filename, 'w') as f:
            json.dump(all_props, f, indent=2, default=str)

        total_props = sum(len(props) for props in all_props.values())
        print(f"💾 Saved {total_props} real props to {filename}")

        return all_props

    except KeyboardInterrupt:
        print("\n⚠️ Scraping interrupted by user")
        scraper._close_selenium_driver()
    except Exception as e:
        print(f"\n❌ Scraping failed: {e}")
        scraper._close_selenium_driver()


if __name__ == "__main__":
    main()
