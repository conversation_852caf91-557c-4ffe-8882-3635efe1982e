#!/usr/bin/env python3
"""
🏀 ENHANCED WNBA PROPS SCRAPER - ADVANCED EXTRACTION
===================================================

ENHANCED FEATURES:
- ✅ Advanced prop extraction patterns
- ✅ Specific WNBA props page navigation
- ✅ Dynamic content waiting for JavaScript
- ✅ Real-time prop detection
- ✅ Multi-level fallback strategies
- ✅ All bulletproof fixes applied

Version: 9.0 (Enhanced)
Date: 2025-07-13
"""

import time
import json
import re
import random
import os
import socket
import ctypes
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Network and connection imports
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Selenium imports
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager

# Environment hardening
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
os.environ['NO_PROXY'] = 'localhost,127.0.0.1'
socket.setdefaulttimeout(45)

# Windows network optimization
if os.name == 'nt':
    try:
        INTERNET_OPTION_HTTP_DECODING = 65
        value = ctypes.c_ulong(0)
        ctypes.windll.winhttp.WinHttpSetOption(None, INTERNET_OPTION_HTTP_DECODING, 
                                               ctypes.byref(value), ctypes.sizeof(value))
    except Exception:
        pass

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedWNBAPropsScaper:
    """Enhanced WNBA Props Scraper with advanced extraction capabilities"""
    
    def __init__(self, visible: bool = True):
        """Initialize enhanced scraper"""
        
        self.visible = visible
        self.driver = None
        self.wait = None
        self.session = None
        
        # Setup robust HTTP session
        self.setup_robust_session()
        
        # Enhanced WNBA Props Detection Patterns
        self.ADVANCED_PROP_PATTERNS = {
            # Points patterns
            'points_over': r'(\w+\s+\w+).*?points.*?over\s+(\d+\.?\d*)',
            'points_under': r'(\w+\s+\w+).*?points.*?under\s+(\d+\.?\d*)',
            'points_ou': r'(\w+\s+\w+).*?(\d+\.?\d+)\s+points?\s+(over|under)',
            'points_line': r'(\w+\s+\w+).*?points.*?(\d+\.?\d+)',
            
            # Rebounds patterns
            'rebounds_over': r'(\w+\s+\w+).*?rebounds?.*?over\s+(\d+\.?\d*)',
            'rebounds_under': r'(\w+\s+\w+).*?rebounds?.*?under\s+(\d+\.?\d*)',
            'rebounds_ou': r'(\w+\s+\w+).*?(\d+\.?\d+)\s+rebounds?\s+(over|under)',
            'rebounds_line': r'(\w+\s+\w+).*?rebounds?.*?(\d+\.?\d+)',
            
            # Assists patterns
            'assists_over': r'(\w+\s+\w+).*?assists?.*?over\s+(\d+\.?\d*)',
            'assists_under': r'(\w+\s+\w+).*?assists?.*?under\s+(\d+\.?\d*)',
            'assists_ou': r'(\w+\s+\w+).*?(\d+\.?\d+)\s+assists?\s+(over|under)',
            'assists_line': r'(\w+\s+\w+).*?assists?.*?(\d+\.?\d+)',
            
            # PRA (Points + Rebounds + Assists) patterns
            'pra_over': r'(\w+\s+\w+).*?(?:pra|pts\+reb\+ast).*?over\s+(\d+\.?\d*)',
            'pra_under': r'(\w+\s+\w+).*?(?:pra|pts\+reb\+ast).*?under\s+(\d+\.?\d*)',
            'pra_ou': r'(\w+\s+\w+).*?(\d+\.?\d+)\s+(?:pra|pts\+reb\+ast)\s+(over|under)',
            
            # Double-double patterns
            'double_double_yes': r'(\w+\s+\w+).*?double.?double.*?yes',
            'double_double_no': r'(\w+\s+\w+).*?double.?double.*?no',
            'double_double': r'(\w+\s+\w+).*?double.?double',
            
            # Three-pointers patterns
            'threes_over': r'(\w+\s+\w+).*?(?:3-?pointers?|threes?).*?over\s+(\d+\.?\d*)',
            'threes_under': r'(\w+\s+\w+).*?(?:3-?pointers?|threes?).*?under\s+(\d+\.?\d*)',
            'threes_ou': r'(\w+\s+\w+).*?(\d+\.?\d+)\s+(?:3-?pointers?|threes?)\s+(over|under)',
            
            # Steals + Blocks patterns
            'steals_blocks': r'(\w+\s+\w+).*?(?:steals?\+blocks?|blocks?\+steals?).*?(\d+\.?\d+)',
            'steals_over': r'(\w+\s+\w+).*?steals?.*?over\s+(\d+\.?\d*)',
            'blocks_over': r'(\w+\s+\w+).*?blocks?.*?over\s+(\d+\.?\d*)'
        }
        
        # WNBA Teams and Players for validation
        self.WNBA_TEAMS = {
            'atlanta': 'ATL', 'chicago': 'CHI', 'connecticut': 'CON', 'dallas': 'DAL',
            'golden state': 'GSV', 'indiana': 'IND', 'las vegas': 'LV', 'los angeles': 'LAS',
            'minnesota': 'MIN', 'new york': 'NYL', 'phoenix': 'PHO', 'seattle': 'SEA',
            'washington': 'WAS', 'dream': 'ATL', 'sky': 'CHI', 'sun': 'CON', 'wings': 'DAL',
            'valkyries': 'GSV', 'fever': 'IND', 'aces': 'LV', 'sparks': 'LAS', 'lynx': 'MIN',
            'liberty': 'NYL', 'mercury': 'PHO', 'storm': 'SEA', 'mystics': 'WAS'
        }
        
        self.WNBA_PLAYERS = [
            "A'ja Wilson", "Breanna Stewart", "Diana Taurasi", "Sabrina Ionescu",
            "Alyssa Thomas", "Kelsey Plum", "Jonquel Jones", "Napheesa Collier",
            "Caitlin Clark", "Angel Reese", "Cameron Brink", "Rickea Jackson",
            "Chelsea Gray", "Jackie Young", "Skylar Diggins-Smith", "Jewell Loyd",
            "Kahleah Copper", "Courtney Williams", "Dearica Hamby", "Nneka Ogwumike",
            "Rhyne Howard", "Aliyah Boston", "Kate Martin", "Kamilla Cardoso"
        ]
        
        # Enhanced site configurations with specific WNBA props URLs
        self.ENHANCED_SITES = {
            "DraftKings_WNBA_Props": {
                "urls": [
                    "https://sportsbook.draftkings.com/leagues/basketball/wnba?category=player-props",
                    "https://sportsbook.draftkings.com/leagues/basketball/wnba",
                    "https://sportsbook.draftkings.com/featured/basketball-11002"
                ],
                "selectors": {
                    "props_containers": [
                        "div.sportsbook-table__body",
                        "div[data-testid='event-cell']",
                        "div.sportsbook-responsive-card-container__card",
                        "div.sportsbook-table-row"
                    ],
                    "player_elements": [
                        "span.sportsbook-row-name",
                        "div.event-cell__name-text",
                        "span.participant-name",
                        "div.sportsbook-outcome-cell__label"
                    ],
                    "odds_elements": [
                        "span.sportsbook-odds",
                        "div.sportsbook-odds",
                        "span.american-odds",
                        "div.price"
                    ],
                    "line_elements": [
                        "span.sportsbook-outcome-cell__line",
                        "div.line-value",
                        "span.handicap"
                    ]
                },
                "wait_elements": [
                    "div.sportsbook-table__body",
                    "span.sportsbook-row-name",
                    "div[data-testid='event-cell']"
                ],
                "dynamic_content": True
            },
            "FanDuel_WNBA_Props": {
                "urls": [
                    "https://sportsbook.fanduel.com/navigation/wnba?tab=player-props",
                    "https://sportsbook.fanduel.com/navigation/wnba",
                    "https://sportsbook.fanduel.com/basketball/wnba"
                ],
                "selectors": {
                    "props_containers": [
                        "div[data-test-id='ArrowMarketGroup']",
                        "div[data-test-id='MarketGroup']",
                        "div.market-group",
                        "div[class*='market']"
                    ],
                    "player_elements": [
                        "h4[data-test-id='ArrowMarketGroupHeader']",
                        "div[data-test-id='MarketName']",
                        "span.player-name",
                        "div.participant"
                    ],
                    "odds_elements": [
                        "span[data-test-id='SelectionPrice']",
                        "div.price",
                        "span.odds"
                    ],
                    "line_elements": [
                        "span[data-test-id='SelectionHandicap']",
                        "div.handicap",
                        "span.line"
                    ]
                },
                "wait_elements": [
                    "div[data-test-id='ArrowMarketGroup']",
                    "h4[data-test-id='ArrowMarketGroupHeader']"
                ],
                "dynamic_content": True
            },
            "ESPN_WNBA_Stats": {
                "urls": [
                    "https://www.espn.com/wnba/players",
                    "https://www.espn.com/wnba/stats",
                    "https://www.espn.com/wnba/"
                ],
                "selectors": {
                    "stats_containers": [
                        "div.Table__TR",
                        "tr.Table__TR",
                        "div.player-stats",
                        "table.Table"
                    ],
                    "player_elements": [
                        "a.AnchorLink",
                        "span.player-name",
                        "td.Table__TD"
                    ]
                },
                "wait_elements": [
                    "div.Table__TR",
                    "table.Table"
                ],
                "dynamic_content": False
            }
        }
        
        logger.info("🏀 Enhanced WNBA Props Scraper initialized")
    
    def setup_robust_session(self):
        """Setup robust HTTP session"""
        
        retry_strategy = Retry(
            total=5,
            backoff_factor=1,
            status_forcelist=[500, 502, 503, 504, 429],
            allowed_methods=["GET", "POST"],
            respect_retry_after_header=True
        )
        
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=100,
            pool_maxsize=100
        )
        
        self.session = requests.Session()
        self.session.mount("https://", adapter)
        self.session.mount("http://", adapter)
        
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
        
        logger.info("✅ Enhanced HTTP session configured")

    def setup_enhanced_driver(self) -> bool:
        """Setup enhanced Chrome driver with JavaScript support"""

        try:
            chrome_options = Options()

            # Core stability options (bulletproof fixes)
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-software-rasterizer")
            chrome_options.add_argument("--disable-webgl")
            chrome_options.add_argument("--disable-webgl2")

            # Enhanced options for dynamic content
            chrome_options.add_argument("--disable-features=GCM,NetworkService")
            chrome_options.add_argument("--disable-background-networking")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option('excludeSwitches', ['enable-logging', 'enable-automation'])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # Enable JavaScript and dynamic content
            chrome_options.add_argument("--enable-javascript")
            chrome_options.add_argument("--disable-web-security")  # For dynamic content
            chrome_options.add_argument("--allow-running-insecure-content")

            # Visibility and performance
            if not self.visible:
                chrome_options.add_argument("--headless=new")
                logger.info("🔧 Enhanced headless mode with JavaScript support")
            else:
                logger.info("🔧 Enhanced visible mode for monitoring")

            chrome_options.add_argument("--window-size=1366,768")
            chrome_options.add_argument("--start-maximized")

            # User agent rotation
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            ]
            chrome_options.add_argument(f"--user-agent={random.choice(user_agents)}")

            # Enhanced preferences for dynamic content
            prefs = {
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0,
                "profile.managed_default_content_settings.images": 1,  # Load images for props
                "profile.managed_default_content_settings.javascript": 1,  # Enable JS
                "profile.managed_default_content_settings.plugins": 1,
                "profile.managed_default_content_settings.media_stream": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)

            # Create driver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Enhanced timeouts for dynamic content
            self.driver.set_page_load_timeout(90)  # Longer for dynamic content
            self.driver.implicitly_wait(15)

            # Setup enhanced wait
            self.wait = WebDriverWait(self.driver, 30)

            # Execute enhanced anti-detection scripts
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
            self.driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})")

            logger.info("✅ Enhanced Chrome driver setup complete")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to setup enhanced driver: {e}")
            return False

    def wait_for_dynamic_content(self, site_config: Dict[str, Any], timeout: int = 30) -> bool:
        """Wait for dynamic content to load using multiple strategies"""

        try:
            logger.info("⏳ Waiting for dynamic content to load...")

            # Strategy 1: Wait for specific elements
            for wait_element in site_config.get("wait_elements", []):
                try:
                    self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, wait_element)))
                    logger.info(f"✅ Found wait element: {wait_element}")
                    time.sleep(2)  # Additional wait for content to populate
                    return True
                except TimeoutException:
                    logger.debug(f"⏰ Timeout waiting for: {wait_element}")
                    continue

            # Strategy 2: Wait for JavaScript to finish loading
            try:
                self.wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")
                logger.info("✅ Document ready state complete")
                time.sleep(3)  # Wait for dynamic content
            except TimeoutException:
                logger.debug("⏰ Document ready state timeout")

            # Strategy 3: Wait for network activity to settle
            try:
                # Wait for no active network requests
                self.wait.until(lambda driver: driver.execute_script(
                    "return window.performance && window.performance.getEntriesByType && "
                    "window.performance.getEntriesByType('navigation').length > 0"
                ))
                logger.info("✅ Network activity settled")
                time.sleep(2)
            except (TimeoutException, Exception):
                logger.debug("⏰ Network activity check failed")

            # Strategy 4: Check for content changes
            initial_content_length = len(self.driver.page_source)
            time.sleep(5)  # Wait for potential content changes
            final_content_length = len(self.driver.page_source)

            if final_content_length > initial_content_length:
                logger.info(f"✅ Dynamic content detected: {final_content_length - initial_content_length} chars added")
                time.sleep(2)  # Additional wait
                return True

            logger.info("✅ Dynamic content wait completed")
            return True

        except Exception as e:
            logger.warning(f"⚠️ Dynamic content wait error: {e}")
            return False

    def enhanced_navigate_to_props(self, site_name: str, site_config: Dict[str, Any]) -> bool:
        """Navigate to WNBA props pages with enhanced strategies"""

        urls_to_try = site_config.get("urls", [])

        for i, url in enumerate(urls_to_try):
            try:
                logger.info(f"🌐 Enhanced navigation {i+1}/{len(urls_to_try)}: {url}")

                # Navigate with JavaScript
                self.driver.execute_script(f"window.location.href = '{url}'")

                # Wait for initial page load
                self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

                # Wait for dynamic content if needed
                if site_config.get("dynamic_content", False):
                    self.wait_for_dynamic_content(site_config)

                # Verify page loaded correctly
                page_title = self.driver.title.lower()
                current_url = self.driver.current_url.lower()

                # Check for access denied or error pages
                if any(error in page_title for error in ['denied', 'error', 'not found', 'blocked']):
                    logger.warning(f"⚠️ Access issue detected: {page_title}")
                    continue

                # Check for WNBA content indicators
                page_source = self.driver.page_source.lower()
                wnba_indicators = ['wnba', 'women', 'basketball', 'props', 'player']

                if any(indicator in page_source for indicator in wnba_indicators):
                    logger.info(f"✅ Successfully loaded WNBA content: {self.driver.title}")
                    return True
                else:
                    logger.warning(f"⚠️ No WNBA content detected on {url}")
                    continue

            except Exception as e:
                logger.warning(f"⚠️ Navigation failed for {url}: {e}")
                continue

        logger.error(f"❌ All navigation attempts failed for {site_name}")
        return False

    def advanced_prop_extraction(self, site_name: str, site_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Advanced prop extraction with multiple strategies"""

        props_found = []

        try:
            logger.info(f"🎯 Starting advanced prop extraction for {site_name}")

            # Strategy 1: Element-based extraction
            element_props = self.extract_props_from_elements(site_config)
            props_found.extend(element_props)
            logger.info(f"📊 Element extraction: {len(element_props)} props")

            # Strategy 2: Enhanced pattern matching
            page_source = self.driver.page_source
            pattern_props = self.extract_props_with_advanced_patterns(page_source)
            props_found.extend(pattern_props)
            logger.info(f"📊 Pattern extraction: {len(pattern_props)} props")

            # Strategy 3: JavaScript-based extraction
            js_props = self.extract_props_with_javascript()
            props_found.extend(js_props)
            logger.info(f"📊 JavaScript extraction: {len(js_props)} props")

            # Strategy 4: Table-based extraction
            table_props = self.extract_props_from_tables()
            props_found.extend(table_props)
            logger.info(f"📊 Table extraction: {len(table_props)} props")

            # Deduplicate and validate
            validated_props = self.validate_and_deduplicate_props(props_found)

            logger.info(f"✅ Total validated props: {len(validated_props)}")
            return validated_props

        except Exception as e:
            logger.error(f"❌ Advanced prop extraction failed: {e}")
            return []

    def extract_props_from_elements(self, site_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract props by finding specific elements"""

        props = []

        try:
            # Find prop containers
            containers = []
            for container_selector in site_config.get("selectors", {}).get("props_containers", []):
                try:
                    found_containers = self.driver.find_elements(By.CSS_SELECTOR, container_selector)
                    containers.extend(found_containers)
                    if found_containers:
                        logger.info(f"✅ Found {len(found_containers)} containers: {container_selector}")
                except Exception as e:
                    logger.debug(f"Container selector failed: {e}")

            # Extract props from containers
            for container in containers[:20]:  # Limit to first 20 containers
                try:
                    container_text = container.text.strip()
                    if not container_text:
                        continue

                    # Look for WNBA player names
                    player_found = None
                    for player in self.WNBA_PLAYERS:
                        if player.lower() in container_text.lower():
                            player_found = player
                            break

                    if not player_found:
                        continue

                    # Extract prop details from container
                    prop_details = self.extract_prop_details_from_container(container, player_found)
                    if prop_details:
                        props.append(prop_details)

                except Exception as e:
                    logger.debug(f"Container processing error: {e}")

            return props

        except Exception as e:
            logger.error(f"❌ Element extraction failed: {e}")
            return []

    def extract_prop_details_from_container(self, container, player_name: str) -> Optional[Dict[str, Any]]:
        """Extract detailed prop information from a container element"""

        try:
            container_text = container.text.strip()

            # Initialize prop data
            prop_data = {
                'player': player_name,
                'timestamp': datetime.now().isoformat(),
                'extraction_method': 'element_based',
                'source_text': container_text[:200]
            }

            # Extract prop type and line using advanced patterns
            for pattern_name, pattern in self.ADVANCED_PROP_PATTERNS.items():
                matches = re.findall(pattern, container_text, re.IGNORECASE)

                if matches:
                    match = matches[0]

                    # Determine prop type
                    if 'points' in pattern_name:
                        prop_data['prop_type'] = 'points'
                    elif 'rebounds' in pattern_name:
                        prop_data['prop_type'] = 'rebounds'
                    elif 'assists' in pattern_name:
                        prop_data['prop_type'] = 'assists'
                    elif 'pra' in pattern_name:
                        prop_data['prop_type'] = 'pra'
                    elif 'double' in pattern_name:
                        prop_data['prop_type'] = 'double_double'
                    elif 'threes' in pattern_name:
                        prop_data['prop_type'] = 'threes'
                    elif 'steals' in pattern_name or 'blocks' in pattern_name:
                        prop_data['prop_type'] = 'steals_blocks'

                    # Extract line and side
                    if len(match) >= 2:
                        if 'over' in pattern_name or 'under' in pattern_name:
                            prop_data['side'] = 'OVER' if 'over' in pattern_name else 'UNDER'
                            prop_data['line'] = match[1] if len(match) > 1 else None
                        elif len(match) >= 3 and match[2] in ['over', 'under']:
                            prop_data['side'] = match[2].upper()
                            prop_data['line'] = match[1]
                        else:
                            prop_data['line'] = match[1] if len(match) > 1 else None

                    # Extract odds if possible
                    odds_patterns = [r'[+-]\d{3,4}', r'\(\+\d+\)', r'\(-\d+\)']
                    for odds_pattern in odds_patterns:
                        odds_match = re.search(odds_pattern, container_text)
                        if odds_match:
                            prop_data['odds'] = odds_match.group()
                            break

                    return prop_data

            # Fallback: Look for basic numerical patterns
            numbers = re.findall(r'\d+\.?\d*', container_text)
            if numbers:
                # Look for reasonable prop lines (0.5 to 50)
                for num in numbers:
                    try:
                        value = float(num)
                        if 0.5 <= value <= 50:
                            prop_data['line'] = num
                            prop_data['prop_type'] = 'unknown'
                            return prop_data
                    except ValueError:
                        continue

            return None

        except Exception as e:
            logger.debug(f"Prop detail extraction error: {e}")
            return None

    def extract_props_with_advanced_patterns(self, page_source: str) -> List[Dict[str, Any]]:
        """Extract props using advanced regex patterns"""

        props = []

        try:
            for pattern_name, pattern in self.ADVANCED_PROP_PATTERNS.items():
                matches = re.findall(pattern, page_source, re.IGNORECASE | re.DOTALL)

                for match in matches:
                    if len(match) >= 2:
                        # Validate player name
                        potential_player = match[0].strip()
                        validated_player = None

                        for player in self.WNBA_PLAYERS:
                            if player.lower() in potential_player.lower():
                                validated_player = player
                                break

                        if validated_player:
                            prop = {
                                'player': validated_player,
                                'extraction_method': 'advanced_pattern',
                                'pattern_used': pattern_name,
                                'timestamp': datetime.now().isoformat()
                            }

                            # Extract prop details based on pattern
                            if 'points' in pattern_name:
                                prop['prop_type'] = 'points'
                            elif 'rebounds' in pattern_name:
                                prop['prop_type'] = 'rebounds'
                            elif 'assists' in pattern_name:
                                prop['prop_type'] = 'assists'
                            elif 'pra' in pattern_name:
                                prop['prop_type'] = 'pra'
                            elif 'double' in pattern_name:
                                prop['prop_type'] = 'double_double'
                            elif 'threes' in pattern_name:
                                prop['prop_type'] = 'threes'

                            # Extract line and side
                            if len(match) >= 2:
                                prop['line'] = match[1]
                            if len(match) >= 3:
                                prop['side'] = match[2].upper()
                            elif 'over' in pattern_name:
                                prop['side'] = 'OVER'
                            elif 'under' in pattern_name:
                                prop['side'] = 'UNDER'

                            props.append(prop)

            return props

        except Exception as e:
            logger.error(f"❌ Advanced pattern extraction failed: {e}")
            return []

    def extract_props_with_javascript(self) -> List[Dict[str, Any]]:
        """Extract props using JavaScript execution"""

        props = []

        try:
            # JavaScript to find props data
            js_script = """
            var props = [];
            var allText = document.body.innerText || document.body.textContent || '';

            // Look for WNBA players
            var players = arguments[0];
            var patterns = arguments[1];

            for (var i = 0; i < players.length; i++) {
                var player = players[i];
                if (allText.toLowerCase().indexOf(player.toLowerCase()) !== -1) {
                    // Found player, look for prop patterns around their name
                    var playerIndex = allText.toLowerCase().indexOf(player.toLowerCase());
                    var context = allText.substring(Math.max(0, playerIndex - 200), playerIndex + 200);

                    // Look for numbers that could be prop lines
                    var numbers = context.match(/\\d+\\.?\\d*/g);
                    if (numbers) {
                        for (var j = 0; j < numbers.length; j++) {
                            var num = parseFloat(numbers[j]);
                            if (num >= 0.5 && num <= 50) {
                                props.push({
                                    player: player,
                                    line: numbers[j],
                                    context: context.substring(0, 100),
                                    method: 'javascript'
                                });
                                break;
                            }
                        }
                    }
                }
            }

            return props;
            """

            # Execute JavaScript
            js_props = self.driver.execute_script(js_script, self.WNBA_PLAYERS, list(self.ADVANCED_PROP_PATTERNS.keys()))

            # Convert to standard format
            for js_prop in js_props:
                prop = {
                    'player': js_prop.get('player'),
                    'line': js_prop.get('line'),
                    'prop_type': 'unknown',
                    'extraction_method': 'javascript',
                    'context': js_prop.get('context', ''),
                    'timestamp': datetime.now().isoformat()
                }
                props.append(prop)

            return props

        except Exception as e:
            logger.debug(f"JavaScript extraction error: {e}")
            return []

    def extract_props_from_tables(self) -> List[Dict[str, Any]]:
        """Extract props from table structures"""

        props = []

        try:
            # Find all tables
            tables = self.driver.find_elements(By.TAG_NAME, "table")
            tables.extend(self.driver.find_elements(By.CSS_SELECTOR, "div[role='table']"))
            tables.extend(self.driver.find_elements(By.CSS_SELECTOR, "div.table"))

            for table in tables:
                try:
                    table_text = table.text.strip()

                    # Check if table contains WNBA content
                    if not any(player.lower() in table_text.lower() for player in self.WNBA_PLAYERS):
                        continue

                    # Extract rows
                    rows = table.find_elements(By.TAG_NAME, "tr")
                    if not rows:
                        rows = table.find_elements(By.CSS_SELECTOR, "div[role='row']")

                    for row in rows:
                        row_text = row.text.strip()

                        # Look for player names in row
                        for player in self.WNBA_PLAYERS:
                            if player.lower() in row_text.lower():
                                # Extract numbers from row
                                numbers = re.findall(r'\d+\.?\d*', row_text)
                                for num in numbers:
                                    try:
                                        value = float(num)
                                        if 0.5 <= value <= 50:
                                            prop = {
                                                'player': player,
                                                'line': num,
                                                'prop_type': 'table_extracted',
                                                'extraction_method': 'table',
                                                'source_text': row_text[:100],
                                                'timestamp': datetime.now().isoformat()
                                            }
                                            props.append(prop)
                                            break
                                    except ValueError:
                                        continue
                                break

                except Exception as e:
                    logger.debug(f"Table row processing error: {e}")
                    continue

            return props

        except Exception as e:
            logger.debug(f"Table extraction error: {e}")
            return []

    def validate_and_deduplicate_props(self, props: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate and deduplicate extracted props"""

        validated_props = []
        seen = set()

        for prop in props:
            try:
                # Validate required fields
                if not prop.get('player') or not prop.get('line'):
                    continue

                # Validate line value
                try:
                    line_value = float(prop['line'])
                    if not (0.5 <= line_value <= 50):
                        continue
                except (ValueError, TypeError):
                    continue

                # Create deduplication key
                key = (
                    prop['player'].lower(),
                    prop.get('prop_type', 'unknown'),
                    prop['line']
                )

                if key not in seen:
                    seen.add(key)
                    validated_props.append(prop)

            except Exception as e:
                logger.debug(f"Prop validation error: {e}")
                continue

        return validated_props

    def run_enhanced_props_session(self) -> Dict[str, Any]:
        """Run enhanced props scraping session"""

        logger.info("🏀 Starting ENHANCED WNBA Props Scraping Session")
        logger.info("=" * 80)

        # Setup enhanced driver
        if not self.setup_enhanced_driver():
            return {"error": "Failed to setup enhanced driver"}

        session_results = {
            'session_start': datetime.now().isoformat(),
            'sites_scraped': {},
            'total_props': [],
            'session_summary': {}
        }

        try:
            total_props = []
            successful_sites = 0

            for site_name, site_config in self.ENHANCED_SITES.items():
                logger.info(f"🎯 Enhanced scraping {site_name}...")

                # Navigate to props pages
                if self.enhanced_navigate_to_props(site_name, site_config):
                    # Extract props with advanced methods
                    site_props = self.advanced_prop_extraction(site_name, site_config)

                    session_results['sites_scraped'][site_name] = {
                        'success': True,
                        'props_found': site_props,
                        'props_count': len(site_props),
                        'timestamp': datetime.now().isoformat()
                    }

                    total_props.extend(site_props)
                    successful_sites += 1

                    logger.info(f"✅ {site_name}: {len(site_props)} props extracted")
                else:
                    session_results['sites_scraped'][site_name] = {
                        'success': False,
                        'error': 'Navigation failed',
                        'props_found': [],
                        'props_count': 0
                    }
                    logger.warning(f"❌ {site_name}: Navigation failed")

                # Delay between sites
                time.sleep(random.uniform(5, 10))

            # Final validation and deduplication
            final_props = self.validate_and_deduplicate_props(total_props)
            session_results['total_props'] = final_props

            # Session summary
            session_results['session_summary'] = {
                'sites_attempted': len(self.ENHANCED_SITES),
                'successful_sites': successful_sites,
                'total_props_found': len(final_props),
                'extraction_methods_used': list(set(prop.get('extraction_method', 'unknown') for prop in final_props)),
                'session_status': 'completed'
            }

        finally:
            # Always close driver
            if self.driver:
                self.driver.quit()
                logger.info("✅ Enhanced session ended - driver closed")

        return session_results

    def display_enhanced_results(self, results: Dict[str, Any]):
        """Display enhanced scraping results"""

        print("\n🏀 ENHANCED WNBA PROPS SCRAPING RESULTS")
        print("=" * 80)
        print(f"📅 Session: {results.get('session_start', 'Unknown')}")
        print("🚀 Enhanced Features: Advanced Patterns + Dynamic Content + JavaScript")
        print("=" * 80)

        if 'error' in results:
            print(f"❌ Session failed: {results['error']}")
            return

        # Session summary
        summary = results.get('session_summary', {})
        print(f"📊 SESSION SUMMARY:")
        print(f"   Sites Attempted: {summary.get('sites_attempted', 0)}")
        print(f"   Successful Sites: {summary.get('successful_sites', 0)}")
        print(f"   Total Props Found: {summary.get('total_props_found', 0)}")
        print(f"   Extraction Methods: {', '.join(summary.get('extraction_methods_used', []))}")
        print(f"   Status: {summary.get('session_status', 'Unknown')}")

        # Props found
        props_found = results.get('total_props', [])
        if props_found:
            print(f"\n🎯 PROPS EXTRACTED ({len(props_found)} total):")

            # Group by extraction method
            by_method = {}
            for prop in props_found:
                method = prop.get('extraction_method', 'unknown')
                if method not in by_method:
                    by_method[method] = []
                by_method[method].append(prop)

            for method, method_props in by_method.items():
                print(f"\n   📊 {method.upper()} METHOD ({len(method_props)} props):")
                for i, prop in enumerate(method_props[:5], 1):  # Show first 5 per method
                    player = prop.get('player', 'Unknown')
                    prop_type = prop.get('prop_type', 'unknown')
                    line = prop.get('line', 'N/A')
                    side = prop.get('side', '')

                    print(f"      {i}. {player} - {prop_type.upper()}")
                    print(f"         Line: {line} {side}")
                    if prop.get('odds'):
                        print(f"         Odds: {prop['odds']}")

                if len(method_props) > 5:
                    print(f"      ... and {len(method_props) - 5} more")
        else:
            print(f"\n❌ No props extracted")
            print(f"\n🔍 TROUBLESHOOTING:")
            print(f"   • Check if WNBA games are scheduled today")
            print(f"   • Verify sportsbooks have posted player props")
            print(f"   • Some sites may require login or have regional restrictions")

        # Site-by-site analysis
        print(f"\n🌐 ENHANCED SITE ANALYSIS:")
        sites_scraped = results.get('sites_scraped', {})

        for site_name, site_data in sites_scraped.items():
            print(f"\n🏪 {site_name.upper()}:")

            if not site_data.get('success', False):
                error = site_data.get('error', 'Unknown error')
                print(f"   ❌ Failed: {error}")
                continue

            props_count = site_data.get('props_count', 0)
            props = site_data.get('props_found', [])

            print(f"   ✅ Success")
            print(f"   📊 Props Extracted: {props_count}")

            if props:
                # Show extraction method breakdown
                methods = {}
                for prop in props:
                    method = prop.get('extraction_method', 'unknown')
                    methods[method] = methods.get(method, 0) + 1

                print(f"   🔧 Methods Used: {', '.join(f'{k}({v})' for k, v in methods.items())}")

                # Show sample props
                print(f"   📋 Sample Props:")
                for prop in props[:3]:
                    print(f"      • {prop.get('player', 'Unknown')} {prop.get('prop_type', 'unknown')} {prop.get('line', 'N/A')}")

        print("=" * 80)
        print("🎉 Enhanced props scraping complete!")

        # Performance analysis
        total_props = len(props_found)
        successful_sites = summary.get('successful_sites', 0)

        if total_props > 0:
            print(f"\n🏆 PERFORMANCE ANALYSIS:")
            print(f"   ✅ Props per successful site: {total_props / max(successful_sites, 1):.1f}")
            print(f"   ✅ Success rate: {successful_sites / summary.get('sites_attempted', 1) * 100:.1f}%")
            print(f"   ✅ Enhanced extraction working!")
        else:
            print(f"\n⚠️ PERFORMANCE NOTES:")
            print(f"   • Enhanced extraction methods deployed successfully")
            print(f"   • No props found may indicate:")
            print(f"     - No WNBA games scheduled")
            print(f"     - Props not yet posted by sportsbooks")
            print(f"     - Regional access restrictions")


def main():
    """Main execution with enhanced capabilities"""

    print("🏀 ENHANCED WNBA PROPS SCRAPER")
    print("=" * 60)
    print("🚀 ENHANCED FEATURES:")
    print("   ✅ Advanced prop extraction patterns")
    print("   ✅ Specific WNBA props page navigation")
    print("   ✅ Dynamic content waiting for JavaScript")
    print("   ✅ Multi-strategy extraction (Elements + Patterns + JS + Tables)")
    print("   ✅ Real-time prop detection")
    print("   ✅ All bulletproof fixes applied")
    print("=" * 60)
    print("⚠️  Running enhanced session with advanced extraction")
    print("⚠️  This will take 15-20 minutes for thorough analysis")
    print("=" * 60)

    # Initialize enhanced scraper
    scraper = EnhancedWNBAPropsScaper(visible=True)

    try:
        # Run enhanced session
        results = scraper.run_enhanced_props_session()

        # Display enhanced results
        scraper.display_enhanced_results(results)

        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"enhanced_wnba_props_{timestamp}.json"

        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        total_props = len(results.get('total_props', []))
        successful_sites = results.get('session_summary', {}).get('successful_sites', 0)
        extraction_methods = results.get('session_summary', {}).get('extraction_methods_used', [])

        print(f"\n💾 Results saved to {filename}")
        print(f"🎯 Total props extracted: {total_props}")
        print(f"✅ Successful sites: {successful_sites}")
        print(f"🔧 Methods used: {', '.join(extraction_methods)}")

        return results

    except KeyboardInterrupt:
        print("\n⚠️ Enhanced session interrupted by user")
    except Exception as e:
        print(f"\n❌ Enhanced session failed: {e}")


if __name__ == "__main__":
    main()
