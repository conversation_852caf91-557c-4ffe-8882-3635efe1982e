#!/usr/bin/env python3
"""
🎯 TEST WEB DASHBOARD EXPERT MAPPING INTEGRATION
===============================================

Quick test to verify expert mapping integration in web dashboard
"""

import sys
import os

# Add paths for imports
sys.path.append(os.path.dirname(__file__))
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'monitoring'))

def test_web_dashboard_expert_integration():
    """Test web dashboard expert mapping integration"""
    
    print("🎯 TESTING WEB DASHBOARD EXPERT MAPPING INTEGRATION")
    print("=" * 60)
    
    try:
        # Import the web dashboard module
        import web_dashboard

        # Test expert insights method directly
        print("🔍 Testing expert insights data method...")

        # Create a mock handler instance to test the method
        class MockHandler:
            def _get_expert_insights_data(self):
                # Copy the method from web_dashboard
                if not web_dashboard.EXPERT_MAPPING_AVAILABLE:
                    return {
                        "expert_status": "Not Available",
                        "player_mappings": 0,
                        "team_mappings": 0,
                        "top_players": [],
                        "team_insights": []
                    }

                try:
                    from expert_multiverse_integration import ExpertMappingLoader
                    expert_loader = ExpertMappingLoader()

                    player_count = len(expert_loader.player_mappings)
                    team_count = len(expert_loader.team_mappings)

                    # Get top players
                    top_players = []
                    elite_players = []

                    for _, player_data in expert_loader.player_mappings.items():
                        player_name = player_data.get('name', 'Unknown')
                        team = player_data.get('team_abbrev', 'UNK')

                        try:
                            role = expert_loader.get_expert_role_assignment(player_name, team)
                            if role == 0:  # Elite player
                                elite_players.append({
                                    "name": player_name,
                                    "team": team,
                                    "position": player_data.get('position', 'Unknown'),
                                    "experience": len(player_data.get('years', [])),
                                    "clutch_rating": player_data.get('clutch_rating', 0.5),
                                    "consistency": player_data.get('consistency', 0.5),
                                    "role": "Elite"
                                })
                        except:
                            pass

                    elite_players.sort(key=lambda x: x['clutch_rating'], reverse=True)
                    top_players = elite_players[:5]

                    # Get team insights
                    team_insights = []
                    for team_abbrev, team_data in expert_loader.team_mappings.items():
                        team_insights.append({
                            "team": team_abbrev,
                            "tier": team_data.get('tier', 'Unknown'),
                            "style": team_data.get('style', 'Unknown'),
                            "pace": team_data.get('pace', 'Unknown')
                        })

                    tier_order = {'elite': 0, 'contender': 1, 'developing': 2}
                    team_insights.sort(key=lambda x: tier_order.get(x['tier'], 3))

                    return {
                        "expert_status": "Active",
                        "player_mappings": player_count,
                        "team_mappings": team_count,
                        "top_players": top_players,
                        "team_insights": team_insights[:8],
                        "last_updated": "2025-07-13"
                    }

                except Exception as e:
                    return {
                        "expert_status": "Error",
                        "player_mappings": 0,
                        "team_mappings": 0,
                        "top_players": [],
                        "team_insights": [],
                        "error": str(e)
                    }

        handler = MockHandler()
        expert_data = handler._get_expert_insights_data()
        
        print(f"✅ Expert insights method working!")
        print(f"📊 Expert Status: {expert_data.get('expert_status', 'Unknown')}")
        print(f"👥 Player Mappings: {expert_data.get('player_mappings', 0)}")
        print(f"🏀 Team Mappings: {expert_data.get('team_mappings', 0)}")
        print(f"⭐ Top Players: {len(expert_data.get('top_players', []))}")
        print(f"🏆 Team Insights: {len(expert_data.get('team_insights', []))}")
        
        # Show some top players if available
        top_players = expert_data.get('top_players', [])
        if top_players:
            print(f"\n🌟 Top Elite Players:")
            for player in top_players[:3]:
                print(f"   {player.get('name', 'Unknown')} ({player.get('team', 'UNK')}) - {player.get('position', 'Unknown')}")
                print(f"      Clutch: {(player.get('clutch_rating', 0) * 100):.0f}% | Experience: {player.get('experience', 0)} years")
        
        # Show team insights if available
        team_insights = expert_data.get('team_insights', [])
        if team_insights:
            print(f"\n🏀 Team Analysis:")
            for team in team_insights[:3]:
                print(f"   {team.get('team', 'UNK')}: {team.get('tier', 'Unknown')} tier | {team.get('style', 'Unknown')} style")
        
        return True
        
    except Exception as e:
        print(f"❌ Web dashboard expert integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_expert_mapping_availability():
    """Test if expert mapping system is available"""
    
    print("\n🧠 TESTING EXPERT MAPPING SYSTEM AVAILABILITY")
    print("=" * 60)
    
    try:
        from expert_multiverse_integration import ExpertMappingLoader, ExpertWeightedMultiverseSystem
        
        # Test expert mapping loader
        loader = ExpertMappingLoader()
        
        print(f"✅ Expert Mapping System available!")
        print(f"👥 Player Mappings: {len(loader.player_mappings)}")
        print(f"🏀 Team Mappings: {len(loader.team_mappings)}")
        
        # Test a few player lookups
        test_players = ["A'ja Wilson", "Breanna Stewart", "Diana Taurasi"]
        
        print(f"\n🔍 Testing player role assignments:")
        for player in test_players:
            try:
                role = loader.get_expert_role_assignment(player, "LV")  # Use LV as default team
                role_name = {0: "Elite", 1: "Rotation", 2: "Bench"}.get(role, "Unknown")
                print(f"   {player}: {role_name} (role {role})")
            except Exception as e:
                print(f"   {player}: Error - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Expert mapping system not available: {e}")
        return False

def main():
    """Run web dashboard expert integration tests"""
    
    print("🎯 WEB DASHBOARD EXPERT MAPPING INTEGRATION TEST")
    print("=" * 80)
    print("Testing expert mapping integration in web dashboard")
    print("=" * 80)
    
    # Test expert mapping availability
    expert_available = test_expert_mapping_availability()
    
    # Test web dashboard integration
    dashboard_integration = test_web_dashboard_expert_integration()
    
    # Summary
    print(f"\n🏆 TEST RESULTS")
    print("=" * 40)
    print(f"Expert Mapping System: {'✅ AVAILABLE' if expert_available else '❌ NOT AVAILABLE'}")
    print(f"Web Dashboard Integration: {'✅ WORKING' if dashboard_integration else '❌ FAILED'}")
    
    if expert_available and dashboard_integration:
        print(f"\n🎉 EXPERT MAPPING FULLY INTEGRATED IN WEB DASHBOARD!")
        print(f"✅ Expert insights will be displayed in the dashboard")
        print(f"✅ Player and team analysis available")
        print(f"✅ Expert system status monitoring active")
    else:
        print(f"\n⚠️ Some integration issues detected")
        if not expert_available:
            print(f"   - Expert mapping system needs to be available")
        if not dashboard_integration:
            print(f"   - Web dashboard integration needs fixing")
    
    return expert_available and dashboard_integration

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
