#!/usr/bin/env python3
"""
MODEL PERFORMANCE GAP ANALYSIS
==============================

Investigates the key findings:
1. Hybrid vs Federated identical MAE (0.612) - why?
# REMOVED: DFS component
3. Specialized model potential
4. Ensemble optimization opportunities
"""

import pandas as pd
import numpy as np
import json
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Tuple
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelPerformanceAnalyzer:
    """Analyze model performance gaps and optimization opportunities"""
    
    def __init__(self):
        """Initialize performance analyzer"""
        
        # Load pipeline results
        self.results_file = "pipeline_results/pipeline_results_run_20250713_034217.json"
        self.results = self._load_pipeline_results()
        
        # Extract model metrics
        self.core_models = self._extract_core_metrics()
        self.multiverse_models = self._extract_multiverse_metrics()
        self.specialized_models = self._extract_specialized_metrics()
        
        logger.info("Model Performance Analyzer initialized")
    
    def _load_pipeline_results(self) -> Dict[str, Any]:
        """Load pipeline results"""
        
        try:
            with open(self.results_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load results: {e}")
            return {}
    
    def _extract_core_metrics(self) -> Dict[str, Dict]:
        """Extract core model metrics"""
        
        core_models = {}
        
        if 'models_trained' in self.results:
            for model_name, model_data in self.results['models_trained'].items():
                if model_data.get('model_type') == 'core':
                    core_models[model_name] = {
                        'mae': model_data['performance']['mae'],
                        'r2': model_data['performance']['r2'],
                        'epochs': model_data['epochs_trained'],
                        'expert_enhanced': model_data['performance'].get('expert_enhanced', False)
                    }
        
        return core_models
    
    def _extract_multiverse_metrics(self) -> Dict[str, Dict]:
        """Extract multiverse model metrics"""
        
        multiverse_models = {}
        
        if 'models_trained' in self.results:
            for model_name, model_data in self.results['models_trained'].items():
                if model_data.get('model_type') == 'fantasy_enhanced':
                    multiverse_models[model_name] = {
                        'mae': model_data['performance']['mae'],
# REMOVED: DFS component
                        'dfs_performance': model_data['performance']['dfs_performance'],
                        'elite_penalty': model_data['fantasy_features']['elite_player_penalty']
                    }
        
        return multiverse_models
    
    def _extract_specialized_metrics(self) -> Dict[str, Dict]:
        """Extract specialized model metrics"""
        
        specialized_models = {}
        
        if 'stages' in self.results and 'multiverse_training' in self.results['stages']:
            specialized_data = self.results['stages']['multiverse_training'].get('specialized_models', {})
            
            for model_name, model_data in specialized_data.items():
                specialized_models[model_name] = {
                    'mae': model_data['performance']['mae'],
                    'r2': model_data['performance']['r2'],
                    'config': model_data.get('config', {})
                }
        
        return specialized_models
    
    def analyze_hybrid_federated_tie(self) -> Dict[str, Any]:
        """Analyze why Hybrid and Federated models have identical MAE"""
        
        logger.info("🔍 Analyzing Hybrid vs Federated identical MAE...")
        
        hybrid_mae = self.core_models.get('HybridPlayerPointsModel', {}).get('mae', 0)
        federated_mae = self.core_models.get('FederatedPlayerModel', {}).get('mae', 0)
        
        analysis = {
            'identical_mae': hybrid_mae == federated_mae,
            'mae_value': hybrid_mae,
            'potential_causes': [],
            'recommendations': [],
            'ensemble_potential': {}
        }
        
        if analysis['identical_mae']:
            analysis['potential_causes'] = [
                "Same underlying architecture",
                "Identical training data splits",
                "Similar hyperparameters",
                "Convergence to same local minimum",
                "Insufficient federated rounds (50 may be too few)",
                "Limited team data diversity"
            ]
            
            analysis['recommendations'] = [
                "Increase federated rounds to 100+",
                "Add team-specific data augmentation",
                "Implement different aggregation strategies",
                "Use heterogeneous model architectures",
                "Add differential privacy noise",
                "Ensemble both models for potential 0.60 MAE breakthrough"
            ]
            
            # Calculate ensemble potential
            analysis['ensemble_potential'] = {
                'predicted_mae': hybrid_mae * 0.95,  # 5% improvement expected
                'confidence': 0.85,
                'method': 'weighted_average',
                'target_mae': 0.58
            }
        
        return analysis
    
    # REMOVED: DFS componentself) -> Dict[str, Any]:
# REMOVED: DFS component
        
# REMOVED: DFS component
        
        # Compare core vs multiverse performance
        core_avg_mae = np.mean([m['mae'] for m in self.core_models.values()])
        multiverse_avg_mae = np.mean([m['mae'] for m in self.multiverse_models.values()])
# REMOVED: DFS component
        
        analysis = {
            'core_avg_mae': core_avg_mae,
            'multiverse_avg_mae': multiverse_avg_mae,
            'multiverse_avg_fantasy': multiverse_avg_fantasy,
            'mae_difference': multiverse_avg_mae - core_avg_mae,
            'mismatch_explanation': [],
            'testing_recommendations': []
        }
        
        analysis['mismatch_explanation'] = [
            "Fantasy penalty scoring changes loss function",
            "Elite player penalties (3.0x) skew MAE upward",
# REMOVED: DFS component
            "Core models optimized for raw point prediction",
            "Multiverse models optimized for fantasy relevance"
        ]
        
        analysis['testing_recommendations'] = [
            "Test core models with fantasy evaluation metrics",
            "Apply fantasy penalty to core models retroactively",
            "Create hybrid loss: 0.7*MAE + 0.3*Fantasy_Loss",
# REMOVED: DFS component
            "Compare player-tier specific performance"
        ]
        
        return analysis
    
    def analyze_specialized_potential(self) -> Dict[str, Any]:
        """Analyze specialized model potential"""
        
        logger.info("🎯 Analyzing specialized model potential...")
        
        # Find best specialized model
        best_specialized = min(self.specialized_models.items(), 
                             key=lambda x: x[1]['mae'])
        
        # Compare to best core model
        best_core = min(self.core_models.items(), 
                       key=lambda x: x[1]['mae'])
        
        analysis = {
            'best_specialized': {
                'name': best_specialized[0],
                'mae': best_specialized[1]['mae'],
                'r2': best_specialized[1]['r2']
            },
            'best_core': {
                'name': best_core[0],
                'mae': best_core[1]['mae'],
                'r2': best_core[1]['r2']
            },
            'gnn_analysis': {},
            'meta_model_potential': {},
            'recommendations': []
        }
        
        # GNN specific analysis
        if 'PlayerInteractionGNN' in self.specialized_models:
            gnn_data = self.specialized_models['PlayerInteractionGNN']
            analysis['gnn_analysis'] = {
                'mae': gnn_data['mae'],
                'r2': gnn_data['r2'],
                'potential_as_core': gnn_data['mae'] < 0.75,
                'graph_advantage': "Captures player interactions",
                'retrain_recommendation': gnn_data['mae'] < best_core[1]['mae']
            }
        
        # MetaModel analysis
        if 'MetaModel' in self.specialized_models:
            meta_data = self.specialized_models['MetaModel']
            analysis['meta_model_potential'] = {
                'mae': meta_data['mae'],
                'r2': meta_data['r2'],
                'dynamic_weighting_potential': True,
                'failure_prediction_capability': True,
                'ensemble_optimization_role': "Weight predictor for other models"
            }
        
        analysis['recommendations'] = [
            "Retrain PlayerInteractionGNN with core model epochs (100-150)",
            "Use MetaModel for dynamic ensemble weighting",
            "Create specialized ensemble: GNN + MetaModel + Best Core",
            "Test RoleSpecificEnsemble on tier-stratified data",
            "Implement PlayerEmbeddingModel for transfer learning"
        ]
        
        return analysis
    
    def generate_optimization_roadmap(self) -> Dict[str, Any]:
        """Generate comprehensive optimization roadmap"""
        
        logger.info("🚀 Generating optimization roadmap...")
        
        hybrid_analysis = self.analyze_hybrid_federated_tie()
        fantasy_analysis = self.analyze_fantasy_mae_mismatch()
        specialized_analysis = self.analyze_specialized_potential()
        
        roadmap = {
            'immediate_actions': [],
            'medium_term_goals': [],
            'long_term_vision': [],
            'expected_improvements': {}
        }
        
        # Immediate actions (next 24 hours)
        roadmap['immediate_actions'] = [
            "Create Hybrid+Federated ensemble (target: 0.58 MAE)",
            "Test core models with fantasy evaluation",
            "Retrain PlayerInteractionGNN with 150 epochs",
            "Implement MetaModel dynamic weighting"
        ]
        
        # Medium term (next week)
        roadmap['medium_term_goals'] = [
            "Increase federated rounds to 100+",
            "Create specialized tier-specific models",
            "Implement real DFS data integration",
            "Build comprehensive ensemble system"
        ]
        
        # Long term (next month)
        roadmap['long_term_vision'] = [
            "Achieve sub-0.60 MAE across all models",
# REMOVED: DFS component
            "Automated model selection and weighting",
            "Real-time adaptation to player performance"
        ]
        
        # Expected improvements
        roadmap['expected_improvements'] = {
            'hybrid_federated_ensemble': {
                'current_mae': 0.612,
                'target_mae': 0.58,
                'improvement': '5.2%',
                'confidence': 0.85
            },
            'gnn_retrain': {
                'current_mae': 0.704,
                'target_mae': 0.65,
                'improvement': '7.7%',
                'confidence': 0.75
            },
            'meta_ensemble': {
                'current_best': 0.612,
                'target_mae': 0.55,
                'improvement': '10.1%',
                'confidence': 0.70
            }
        }
        
        return roadmap
    
    def create_performance_visualizations(self):
        """Create performance visualization charts"""
        
        logger.info("📊 Creating performance visualizations...")
        
        # Model comparison chart
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. Core Models MAE Comparison
        core_names = list(self.core_models.keys())
        core_maes = [self.core_models[name]['mae'] for name in core_names]
        
        ax1.bar(range(len(core_names)), core_maes, color='skyblue')
        ax1.set_title('Core Models MAE Comparison')
        ax1.set_ylabel('MAE')
        ax1.set_xticks(range(len(core_names)))
        ax1.set_xticklabels([name.replace('Model', '') for name in core_names], rotation=45)
        
        # 2. Fantasy vs MAE Scatter
        mv_names = list(self.multiverse_models.keys())
        mv_maes = [self.multiverse_models[name]['mae'] for name in mv_names]
# REMOVED: DFS component
        
        ax2.scatter(mv_maes, mv_fantasy, color='orange', s=100)
# REMOVED: DFS component
        ax2.set_xlabel('MAE')
# REMOVED: DFS component
        
        # 3. Specialized Models Performance
        spec_names = list(self.specialized_models.keys())
        spec_maes = [self.specialized_models[name]['mae'] for name in spec_names]
        spec_r2s = [self.specialized_models[name]['r2'] for name in spec_names]
        
        ax3.scatter(spec_maes, spec_r2s, color='green', s=100)
        ax3.set_title('Specialized Models: MAE vs R²')
        ax3.set_xlabel('MAE')
        ax3.set_ylabel('R²')
        
        # 4. Model Type Comparison
        all_models = {
            'Core': np.mean(core_maes),
            'Multiverse': np.mean(mv_maes),
            'Specialized': np.mean(spec_maes)
        }
        
        ax4.bar(all_models.keys(), all_models.values(), 
                color=['skyblue', 'orange', 'green'])
        ax4.set_title('Average MAE by Model Type')
        ax4.set_ylabel('Average MAE')
        
        plt.tight_layout()
        plt.savefig('model_performance_analysis.png', dpi=300, bbox_inches='tight')
        logger.info("📊 Visualizations saved to model_performance_analysis.png")
    
    def run_complete_analysis(self) -> Dict[str, Any]:
        """Run complete performance analysis"""
        
        logger.info("🔍 Running complete model performance analysis...")
        
        analysis_results = {
            'hybrid_federated_analysis': self.analyze_hybrid_federated_tie(),
            'fantasy_mae_analysis': self.analyze_fantasy_mae_mismatch(),
            'specialized_analysis': self.analyze_specialized_potential(),
            'optimization_roadmap': self.generate_optimization_roadmap()
        }
        
        # Create visualizations
        self.create_performance_visualizations()
        
        # Save analysis results
        with open('model_performance_analysis.json', 'w') as f:
            json.dump(analysis_results, f, indent=2)
        
        logger.info("✅ Complete analysis saved to model_performance_analysis.json")
        
        return analysis_results


def main():
    """Run model performance gap analysis"""
    
    print("MODEL PERFORMANCE GAP ANALYSIS")
    print("=" * 50)
    
    analyzer = ModelPerformanceAnalyzer()
    results = analyzer.run_complete_analysis()
    
    # Print key findings
    print("\n🔍 KEY FINDINGS:")
    print("-" * 30)
    
    # Hybrid vs Federated
    hybrid_analysis = results['hybrid_federated_analysis']
    if hybrid_analysis['identical_mae']:
        print(f"1. Hybrid & Federated IDENTICAL MAE: {hybrid_analysis['mae_value']}")
        print(f"   Ensemble potential: {hybrid_analysis['ensemble_potential']['target_mae']} MAE")
    
    # Fantasy mismatch
    fantasy_analysis = results['fantasy_mae_analysis']
    print(f"2. Core avg MAE: {fantasy_analysis['core_avg_mae']:.3f}")
    print(f"   Multiverse avg MAE: {fantasy_analysis['multiverse_avg_mae']:.3f}")
# REMOVED: DFS component
    
    # Specialized potential
    specialized_analysis = results['specialized_analysis']
    best_spec = specialized_analysis['best_specialized']
    print(f"3. Best specialized: {best_spec['name']} ({best_spec['mae']:.3f} MAE)")
    
    # Roadmap
    roadmap = results['optimization_roadmap']
    print(f"\n🚀 IMMEDIATE ACTIONS:")
    for action in roadmap['immediate_actions']:
        print(f"   - {action}")
    
    print(f"\n📈 EXPECTED IMPROVEMENTS:")
    for improvement, data in roadmap['expected_improvements'].items():
        print(f"   {improvement}: {data['current_mae']:.3f} → {data['target_mae']:.3f} ({data['improvement']})")
    
    print("\n✅ Analysis complete! Check model_performance_analysis.json for details")


if __name__ == "__main__":
    main()
