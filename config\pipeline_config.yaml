# 🚀 AUTOMATED WNBA TRAINING PIPELINE CONFIGURATION
# ================================================

# Scheduling Configuration
schedule:
  daily_training: "02:00"              # Daily training at 2 AM
  weekly_full_retrain: "sunday 01:00"  # Full retrain Sunday 1 AM
  data_collection_interval: 6          # Hours between data collection
  model_validation_interval: 12        # Hours between model validation

# Data Collection Configuration
data:
  # TRAINING PHASE - Use historical data only
  training_sources:
    - "real_wnba_data_collector"
    - "historical_injury_data"  # Use batch historical data
    - "expert_odds_api"

  # PREDICTION PHASE - Use real-time data
  prediction_sources:
    - "real_wnba_data_collector"
    - "real_injury_system"      # Live injury monitoring
    - "expert_odds_api"

  # Current mode
  current_mode: "training"  # Switch to "prediction" for live predictions
  validation_threshold: 0.95           # Minimum data quality threshold
  min_samples_per_player: 10           # Minimum samples required per player
  feature_selection_method: "expert_guided"
  data_retention_days: 365             # Days to retain historical data

# Model Training Configuration
models:
  # Core Models
  core_models:
    - "PlayerPointsModel"
    - "HybridPlayerPointsModel"
    - "MultiTaskPlayerModel"
    - "BayesianPlayerModel"
    - "FederatedPlayerModel"
  
  # Complete Multiverse Ensemble Models (9 MODELS - WeatherImpactModel REMOVED)
  multiverse_models:
    - "PossessionBasedModel"
    - "LineupChemistryModel"
    - "CumulativeFatigueModel"
    - "HighLeverageModel"
    - "TeamDynamicsModel"
    - "ContextualPerformanceModel"
    - "InjuryImpactModel"
    - "CoachingStyleModel"
    - "ArenaEffectModel"
    # REMOVED: "WeatherImpactModel" - WNBA is indoor sport

  # Specialized Advanced Models
  specialized_models:
    - "MetaModel"                # Predicts when primary model will fail
    - "PlayerEmbeddingModel"     # Latent player embeddings
    - "RoleSpecificEnsemble"     # Tier-based ensemble (Elite/Rotation/Bench)
    - "RoleClassifierModel"      # Multi-task role prediction
    - "PlayerInteractionGNN"     # Graph Neural Networks for interactions
  
  # Repurposed Models (Need Retraining)
  repurposed_models:
    - "GameTotalsModel"
    - "TeamScoringModel"
    - "ReboundPredictionModel"
    - "AssistPredictionModel"
    - "MinutesPredictionModel"
    - "EfficiencyRatingModel"
    - "ThreePointModel"
    - "FreeThrowModel"
    - "TurnoverModel"
    - "StealBlockModel"
    - "PlusMinusModel"
    - "UsageRateModel"
  
  # EXPERT-LEVEL Training Parameters
  target_mae: 1.0                      # Target Mean Absolute Error
  max_training_time: 7200              # Maximum training time per model (2 hours)
  early_stopping_patience: 15         # Expert patience for full convergence

  # FEDERATED LEARNING MATCHING DATA SPLITS - CORRECTED SPLITS
  temporal_split: true                 # Use temporal splits (matching fed learning)
  train_years: [2015, 2016, 2017, 2018, 2019, 2020, 2021]  # 2015-2021 training (7 years)
  val_years: [2022, 2023]              # 2022-2023 validation (2 years)
  test_years: [2024, 2025]             # 2024-2025 test (current seasons)

  # EXPERT-LEVEL TRAINING EPOCHS
  core_model_epochs: 100               # Core models: 100 epochs
  enhanced_model_epochs: 120           # Enhanced models: 120 epochs
  bayesian_model_epochs: 150           # Bayesian models: 150 epochs (uncertainty calibration)
  multiverse_model_epochs: 100         # Multiverse models: 100 epochs
  specialized_model_epochs: 120        # Specialized models: 120 epochs

  # Training Hyperparameters
  batch_size: 64                       # Batch size (matching fed learning)
  learning_rate: 0.001                 # Initial learning rate
  weight_decay: 1e-5                   # Weight decay (matching fed learning)
  gradient_clipping: 1.0               # Gradient clipping (matching fed learning)

  # Fantasy Integration Settings
  fantasy_penalty_enabled: true       # Enable fantasy penalty scoring
  advanced_sample_weighting: true     # Use advanced sample weighting
  drift_detection_enabled: true       # Enable temporal drift monitoring
  hierarchical_validation: true       # Enable team-level validation
  stratified_evaluation: true         # Enable role-based evaluation

  # EXPERT MAPPING INTEGRATION
  expert_mapping_enabled: true        # Enable expert mapping system
  expert_mapping_path: "config/expert_mappings.json"
  player_tier_weighting: true         # Use player tier weighting
  team_context_adjustment: true       # Use team context adjustments
  role_specific_training: true        # Use role-specific training

  # Training Callbacks
  callbacks:
    - "DriftDetectorCallback"          # Temporal drift monitoring
    - "FantasyPenaltyCallback"         # Fantasy-aware training
    - "HierarchicalValidationCallback" # Team-level validation
    - "StratifiedEvaluationCallback"   # Role-based evaluation
    - "ExpertMappingCallback"          # Expert mapping integration

# Federated Learning Configuration
federated:
  teams:
    - "ATL"  # Atlanta Dream
    - "CHI"  # Chicago Sky
    - "CON"  # Connecticut Sun
    - "DAL"  # Dallas Wings
    - "GSV"  # Golden State Valkyries
    - "IND"  # Indiana Fever
    - "LAS"  # Los Angeles Sparks
    - "LV"   # Las Vegas Aces
    - "MIN"  # Minnesota Lynx
    - "NYL"  # New York Liberty
    - "PHO"  # Phoenix Mercury
    - "SEA"  # Seattle Storm
    - "WAS"  # Washington Mystics
  
  # EXPERT-LEVEL FEDERATED LEARNING
  rounds: 50                          # Expert-level federated rounds (matching fed config)
  local_epochs: 5                     # Local epochs per round (matching fed config)
  privacy_budget: 1.0                 # Differential privacy budget
  aggregation_method: "fedavg"        # Federated averaging method
  min_clients_per_round: 8            # Minimum teams per round
  client_fraction: 1.0                # Use ALL teams (matching fed config)

  # MATCHING FEDERATED CONFIG EXACTLY
  batch_size_fed: 64                  # Federated batch size
  learning_rate_fed: 0.001            # Federated learning rate
  weight_decay_fed: 1e-5              # Federated weight decay
  gradient_clipping_fed: 1.0          # Federated gradient clipping
  server_timeout: 300                 # 5 minutes server timeout
  client_timeout: 180                 # 3 minutes client timeout

  # Federated Multiverse Integration
  multiverse_federated: true          # Enable federated multiverse training
  specialized_federated: true         # Include specialized models in federation
  fantasy_federated: true             # Include fantasy systems in federation
  cross_team_validation: true         # Validate models across teams

  # Privacy and Security
  differential_privacy: true          # Enable differential privacy
  secure_aggregation: true            # Use secure aggregation
  model_encryption: true              # Encrypt model updates

# Fantasy System Configuration
fantasy:
  contest_types:
    - "dfs"                           # Daily Fantasy Sports
    - "season_long"                   # Season-long fantasy
    - "best_ball"                     # Best ball format
  
  salary_sources: []
    # REMOVED: Fake DFS salary sources
  
  optimization_objectives:
    - "points"                        # Maximize fantasy points
    - "value"                         # Maximize points per dollar
    - "ceiling"                       # Maximize upside potential
    - "floor"                         # Maximize consistency
  
  lineup_constraints:
    total_players: 8                  # WNBA DFS lineup size
    salary_cap: 50000                # DFS salary cap
    position_requirements:
      G: 2                           # Guards
      F: 2                           # Forwards
      UTIL: 4                        # Utility spots

# Quality Assurance Configuration
quality:
  min_accuracy_threshold: 0.85        # Minimum model accuracy
  max_bias_threshold: 0.1             # Maximum prediction bias
  performance_regression_threshold: 0.05  # Max performance drop
  stress_test_scenarios: 100          # Number of stress test scenarios
  
  test_suites:
    - "unit_tests"
    - "integration_tests"
    - "performance_tests"
    - "bias_tests"
    - "drift_tests"
  
  validation_metrics:
    - "mae"                           # Mean Absolute Error
    - "rmse"                          # Root Mean Square Error
    - "mape"                          # Mean Absolute Percentage Error
    - "r2"                            # R-squared
    - "bias"                          # Prediction bias
    - "calibration"                   # Probability calibration

# Deployment Configuration
deployment:
  staging_validation: true            # Validate in staging first
  canary_deployment: true             # Use canary deployment
  rollback_threshold: 0.1             # Performance drop threshold for rollback
  
  monitoring_metrics:
    - "mae"                           # Mean Absolute Error
    - "bias"                          # Prediction bias
    - "drift"                         # Model drift
    - "latency"                       # Prediction latency
    - "throughput"                    # Predictions per second
  
  alert_thresholds:
    mae_threshold: 2.0                # MAE alert threshold
    bias_threshold: 0.15              # Bias alert threshold
    drift_threshold: 0.1              # Drift alert threshold
    latency_threshold: 1000           # Latency alert threshold (ms)
  
  deployment_environments:
    - "staging"
    - "production"
    - "canary"

# Logging and Monitoring Configuration
logging:
  level: "INFO"                       # Logging level
  file_retention_days: 30             # Log file retention
  max_file_size_mb: 100              # Maximum log file size
  
  log_destinations:
    - "file"                          # Log to file
    - "console"                       # Log to console
    - "monitoring_system"             # Send to monitoring

# Resource Management Configuration
resources:
  max_cpu_cores: 8                    # Maximum CPU cores to use
  max_memory_gb: 16                   # Maximum memory to use (GB)
  gpu_enabled: false                  # Enable GPU training
  parallel_training: true            # Enable parallel model training
  max_parallel_models: 4              # Maximum models to train in parallel

# Backup and Recovery Configuration
backup:
  enabled: true                       # Enable automated backups
  frequency: "daily"                  # Backup frequency
  retention_days: 30                  # Backup retention period
  backup_locations:
    - "local"                         # Local backup
    - "cloud"                         # Cloud backup (if configured)
  
  backup_components:
    - "models"                        # Trained models
    - "data"                          # Training data
    - "configs"                       # Configuration files
    - "results"                       # Training results

# Notification Configuration
notifications:
  enabled: true                       # Enable notifications
  channels:
    - "email"                         # Email notifications
    - "slack"                         # Slack notifications (if configured)
    - "dashboard"                     # Dashboard alerts
  
  notification_events:
    - "training_complete"             # Training completion
    - "training_failed"               # Training failure
    - "quality_issues"                # Quality assurance issues
    - "deployment_complete"           # Deployment completion
    - "performance_degradation"       # Performance issues
