# 🔍 **COMPREHENSIVE API ACCESS AUDIT REPORT**
## **Every Location in Codebase Requiring API Access**

---

## 🚨 **CRITICAL SECURITY FINDING: EXPOSED API KEY**

### **⚠️ IMMEDIATE ACTION REQUIRED:**
**File:** `expert_odds_api_system.py` (Line 488)
```python
API_KEY = "6b834a837e6c14b85f25949449bb2296"  # Your API key
```
**🚨 SECURITY RISK:** Hardcoded API key exposed in source code!

---

## 📊 **API ACCESS REQUIREMENTS BY CATEGORY**

### **1. 🏀 SPORTS DATA APIs**

#### **🏀 NBA/WNBA Official API**
- **Files:** `src/data_collection/extract_additional_nba_api_data.py`, `src/data_collection/unified_wnba_automated_collector.py`
- **Base URL:** `https://stats.nba.com/stats/`
- **Endpoints Used:**
  - `leaguedashplayerstats` (Player statistics)
  - `leaguedashteamstats` (Team statistics)
  - `leaguedashplayerbiostats` (Player biographical data)
  - `commonteamroster` (Team rosters)
  - `synergyplaytypes` (Play type analytics)
  - `leaguedashlineups` (Lineup performance)
- **Authentication:** Headers-based (User-Agent spoofing)
- **Rate Limits:** 1.5 requests/second, 1000 daily, 15000 monthly
- **Tracking File:** `api_credit_tracking.json`

#### **🎯 The Odds API**
- **Files:** `expert_odds_api_system.py`, `src/data_collection/odds_api_roster_manager.py`
- **Base URL:** `https://api.the-odds-api.com/v4`
- **Endpoints Used:**
  - `/sports/basketball_wnba/odds` (WNBA odds)
- **Authentication:** API Key required
- **🚨 EXPOSED KEY:** `6b834a837e6c14b85f25949449bb2296`
- **Rate Limits:** 500 requests/day (configurable)
- **Markets:** h2h, spreads, totals
- **Cache Duration:** 30 minutes
- **Database:** `expert_odds_cache.db`

### **2. 🌐 WEB SCRAPING TARGETS**

#### **🏀 WNBA Official Website**
- **Files:** `real_wnba_data_collector.py`
- **Base URL:** `https://www.wnba.com/`
- **Endpoints:**
  - `/stats/` (Statistics)
  - `/players/` (Player information)
  - `/teams/` (Team information)
  - `/schedule/` (Game schedules)
- **Authentication:** None (public scraping)
- **Rate Limiting:** Respectful delays (2-5 seconds)

#### **📊 Sportsbook Props Scraping**
- **Files:** `real_wnba_props_scraper.py`, `wnba_props_scraper_integrated.py`, `bulletproof_wnba_scraper.py`
- **Targets:** Multiple sportsbooks (URLs removed for compliance)
- **Methods:** 
  - Selenium WebDriver (Chrome/Firefox)
  - Requests + BeautifulSoup
- **Authentication:** None (public data)
- **Rate Limiting:** 2-5 second delays
- **Cache:** `wnba_props_cache.db`
- **Selenium Config:** Headless mode, GPU disabled

#### **🏥 Injury Data RSS Feeds**
- **Files:** `expert_injury_integration_system.py`
- **RSS URL:** `http://localhost:8080/wnba-injuries.rss` (local server)
- **Sources:** ESPN, WNBA Official, Basketball Reference
- **Authentication:** None
- **Database:** `wnba_injuries.db`, `real_wnba_injuries.db`

### **3. 🖥️ INTERNAL API SERVICES**

#### **🔮 Hybrid Prediction Server**
- **Files:** `hybrid_prediction_server.py`, `src/monitoring/web_dashboard.py`
- **Base URL:** `http://localhost:5000` or `http://127.0.0.1:5000`
- **Endpoints:**
  - `/predict` (Predictions)
  - `/api/odds` (Odds data)
  - `/accuracy` (Accuracy metrics)
  - `/health` (Health check)
- **Authentication:** None (internal service)
- **Port:** 5000

#### **📊 Web Dashboard**
- **Files:** `src/monitoring/web_dashboard.py`
- **Internal Requests:** Prediction server communication
- **Database:** `prediction_accuracy.db`

### **4. 🗄️ DATABASE CONNECTIONS**

#### **📊 SQLite Databases (Local)**
- `expert_odds_cache.db` (Odds caching)
- `wnba_props_cache.db` (Props caching)
- `wnba_injuries.db` (Injury tracking)
- `real_wnba_injuries.db` (Real injury data)
- `prediction_accuracy.db` (Accuracy tracking)
- `unified_collection_tracking.db` (Collection tracking)

#### **🔒 Production Database (Kubernetes)**
- **File:** `k8s/deployment.yaml`
- **Connection:** Environment variable `DATABASE_URL`
- **Secret:** `wnba-secrets/database-url`

### **5. 🐳 CONTAINER REGISTRY ACCESS**

#### **🐙 GitHub Container Registry**
- **Files:** `.github/workflows/cicd.yml`, `expert_production_deployment_system.py`
- **Registry:** `ghcr.io`
- **Authentication:** `${{ secrets.GITHUB_TOKEN }}`
- **Images:** `wnba-prediction:latest`, `wnba-prediction:${{ github.sha }}`

#### **☸️ Kubernetes Deployment**
- **Files:** `k8s/deployment.yaml`, `k8s/hpa.yaml`
- **Authentication:** `${{ secrets.KUBECONFIG }}`
- **Namespace:** `wnba-prediction`

---

## 🔧 **CONFIGURATION FILES REQUIRING API CREDENTIALS**

### **📋 Configuration Files:**
1. `config/pipeline_config.yaml` - Data source configurations
2. `config/unified_collector_config.json` - Collection settings
3. `config/federated_config.json` - Federated learning config
4. `api_credit_tracking.json` - API usage tracking

### **🔐 Environment Variables Needed:**
```bash
# The Odds API
ODDS_API_KEY=your_odds_api_key_here

# Database (Production)
DATABASE_URL=postgresql://user:pass@host:port/db

# Kubernetes (CI/CD)
KUBECONFIG=base64_encoded_kubeconfig

# GitHub (CI/CD)
GITHUB_TOKEN=github_personal_access_token
```

---

## 🚨 **SECURITY RECOMMENDATIONS**

### **🔒 IMMEDIATE FIXES REQUIRED:**

1. **🚨 CRITICAL: Remove Hardcoded API Key**
   ```python
   # REMOVE THIS FROM expert_odds_api_system.py:
   API_KEY = "6b834a837e6c14b85f25949449bb2296"
   
   # REPLACE WITH:
   API_KEY = os.getenv('ODDS_API_KEY')
   if not API_KEY:
       raise ValueError("ODDS_API_KEY environment variable required")
   ```

2. **🔐 Environment Variable Migration**
   - Move all credentials to environment variables
   - Use `.env` files for development
   - Use Kubernetes secrets for production

3. **🛡️ API Key Rotation**
   - Rotate the exposed Odds API key immediately
   - Implement key rotation schedule

4. **📊 Rate Limiting Enhancement**
   - Implement exponential backoff
   - Add circuit breaker patterns
   - Monitor API usage more closely

---

## 📈 **API USAGE MONITORING**

### **📊 Current Tracking:**
- **NBA API:** `api_credit_tracking.json` (500/1000 daily limit)
- **Odds API:** `expert_odds_cache.db` (500/day limit)
- **Props Scraping:** Respectful delays implemented

### **🔍 Monitoring Locations:**
- `src/monitoring/web_dashboard.py` - Dashboard monitoring
- `automated_training_pipeline.py` - Pipeline monitoring
- `hybrid_prediction_server.py` - Prediction monitoring

---

## 🎯 **DEPLOYMENT REQUIREMENTS**

### **🐳 Docker Requirements:**
- Network access for API calls
- Volume mounts for databases
- Environment variable injection

### **☸️ Kubernetes Requirements:**
- Secrets for API keys
- ConfigMaps for configuration
- Network policies for external access
- Ingress for external traffic

### **🔧 Local Development:**
- `.env` file with API credentials
- Local database setup
- Network access for external APIs

---

## ✅ **COMPLIANCE & BEST PRACTICES**

### **📋 Current Status:**
- ✅ Rate limiting implemented
- ✅ Caching systems in place
- ✅ Error handling for API failures
- ❌ Hardcoded credentials (CRITICAL)
- ✅ Respectful scraping practices
- ✅ User-Agent rotation

### **🎯 Recommendations:**
1. Implement proper secrets management
2. Add API key validation
3. Enhance monitoring and alerting
4. Implement API circuit breakers
5. Add comprehensive logging
6. Regular security audits

---

**🏆 SUMMARY: 15+ API integrations identified requiring proper credential management and security hardening.**
