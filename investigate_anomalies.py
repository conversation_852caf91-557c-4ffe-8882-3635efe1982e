#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INVESTIGATE MODEL ANOMALIES
===========================

Investigates the three key anomalies identified:
1. Specialized models underperformance
2. Arena Effect Model overfitting
3. Best model selection discrepancy
"""

import json
import os

def investigate_anomalies():
    """Comprehensive anomaly investigation"""
    
    print("🔍 COMPREHENSIVE MODEL ANOMALY INVESTIGATION")
    print("=" * 60)
    
    # Load results
    with open('pipeline_results/latest_pipeline_results.json', 'r') as f:
        results = json.load(f)
    
    core_models = results['stages']['core_training']['models']
    multiverse_models = results['stages']['multiverse_training']['models']
    specialized_models = results['stages']['multiverse_training']['specialized_models']
    
    print("\n🚨 ANOMALY 1: SPECIALIZED MODELS UNDERPERFORMANCE")
    print("-" * 55)
    
    # Calculate performance gaps
    core_mae_avg = sum(m['performance']['mae'] for m in core_models.values()) / len(core_models)
    spec_mae_avg = sum(m['performance']['mae'] for m in specialized_models.values()) / len(specialized_models)
    
    mae_gap = spec_mae_avg - core_mae_avg
    
    print(f"Core Models Average MAE: {core_mae_avg:.3f}")
    print(f"Specialized Models Average MAE: {spec_mae_avg:.3f}")
    print(f"Performance Gap: {mae_gap:.3f} (specialized worse)")
    
    print("\nWORST SPECIALIZED MODEL:")
    worst_spec = max(specialized_models.items(), key=lambda x: x[1]['performance']['mae'])
    print(f"  {worst_spec[0]}: MAE={worst_spec[1]['performance']['mae']:.3f}")
    
    print("\nBEST CORE MODEL:")
    best_core = min(core_models.items(), key=lambda x: x[1]['performance']['mae'])
    print(f"  {best_core[0]}: MAE={best_core[1]['performance']['mae']:.3f}")
    
    performance_ratio = worst_spec[1]['performance']['mae'] / best_core[1]['performance']['mae']
    print(f"Performance Ratio: {performance_ratio:.2f}x worse")
    
    print("\n🚨 ANOMALY 2: ARENA EFFECT MODEL OVERFITTING")
    print("-" * 50)
    
    # Analyze MAE gaps for overfitting
    mae_gaps = {}
    for name, model in multiverse_models.items():
        perf = model['performance']
        mae_gap = perf['val_mae'] - perf['train_mae']
        mae_gaps[name] = mae_gap
    
    # Sort by MAE gap (worst first)
    sorted_gaps = sorted(mae_gaps.items(), key=lambda x: x[1], reverse=True)
    
    print("OVERFITTING RANKING (MAE Gap):")
    for i, (model_name, gap) in enumerate(sorted_gaps[:5], 1):
        risk_level = 'HIGH' if gap > 0.035 else 'MEDIUM' if gap > 0.025 else 'LOW'
        print(f"  {i}. {model_name}: {gap:.3f} ({risk_level} RISK)")
    
    # Arena Effect Model details
    arena_model = multiverse_models['ArenaEffectModel']['performance']
    print(f"\nArenaEffectModel Details:")
    print(f"  Train MAE: {arena_model['train_mae']:.3f}")
    print(f"  Val MAE:   {arena_model['val_mae']:.3f}")
    print(f"  MAE Gap:   {arena_model['val_mae'] - arena_model['train_mae']:.3f}")
    print(f"  Val R²:    {arena_model['val_r2']:.3f}")
    
    print("\n🚨 ANOMALY 3: BEST MODEL SELECTION DISCREPANCY")
    print("-" * 50)
    
    # Find true best performers across all categories
    all_models = {}
    
    # Add core models
    for name, model in core_models.items():
        all_models[name] = {
            'mae': model['performance']['mae'],
            'r2': model['performance']['r2'],
            'category': 'CORE'
        }
    
    # Add multiverse models (use validation MAE)
    for name, model in multiverse_models.items():
        all_models[name] = {
            'mae': model['performance']['val_mae'],
            'r2': model['performance']['val_r2'],
            'category': 'MULTIVERSE'
        }
    
    # Add specialized models
    for name, model in specialized_models.items():
        all_models[name] = {
            'mae': model['performance']['mae'],
            'r2': model['performance']['r2'],
            'category': 'SPECIALIZED'
        }
    
    # Find true best performers
    best_mae_model = min(all_models.items(), key=lambda x: x[1]['mae'])
    best_r2_model = max(all_models.items(), key=lambda x: x[1]['r2'])
    
    print("TRUE BEST PERFORMERS:")
    print(f"  Best MAE: {best_mae_model[0]} ({best_mae_model[1]['category']})")
    print(f"    MAE: {best_mae_model[1]['mae']:.3f}")
    print(f"    R²:  {best_mae_model[1]['r2']:.3f}")
    
    print(f"  Best R²: {best_r2_model[0]} ({best_r2_model[1]['category']})")
    print(f"    MAE: {best_r2_model[1]['mae']:.3f}")
    print(f"    R²:  {best_r2_model[1]['r2']:.3f}")
    
    # Check current "best" flagged models
    current_best_mae = min(multiverse_models.items(), key=lambda x: x[1]['performance']['val_mae'])
    current_best_r2 = max(multiverse_models.items(), key=lambda x: x[1]['performance']['val_r2'])
    
    print(f"\nCURRENT FLAGGED 'BEST' (Multiverse only):")
    print(f"  Flagged Best MAE: {current_best_mae[0]} ({current_best_mae[1]['performance']['val_mae']:.3f})")
    print(f"  Flagged Best R²: {current_best_r2[0]} ({current_best_r2[1]['performance']['val_r2']:.3f})")
    
    print("\n📊 CRITICAL FINDINGS:")
    print("-" * 20)
    
    print("1. EVALUATION INCONSISTENCY:")
    print(f"   - Core models use different metrics than multiverse models")
    print(f"   - True best MAE is {best_mae_model[0]} (CORE), not flagged")
    
    print("2. SPECIALIZED MODEL FAILURE:")
    print(f"   - {mae_gap:.3f} MAE degradation vs core models")
    print(f"   - Suggests task misalignment or overfitting")
    
    print("3. OVERFITTING DETECTED:")
    print(f"   - ArenaEffectModel has {arena_model['val_mae'] - arena_model['train_mae']:.3f} MAE gap")
    print(f"   - Indicates poor generalization")
    
    print("\n🔧 RECOMMENDED ACTIONS:")
    print("-" * 25)
    print("1. STANDARDIZE EVALUATION: Use same metrics across all model types")
    print("2. RETRAIN SPECIALIZED: Fix task alignment and reduce complexity")
    print("3. ARENA MODEL REGULARIZATION: Add dropout, reduce overfitting")
    print("4. CROSS-VALIDATION: Implement proper model comparison")
    print("5. METRIC AUDIT: Verify evaluation pipeline consistency")

if __name__ == "__main__":
    investigate_anomalies()
