#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FIX EVALUATION PIPELINE
=======================

Fixes the critical evaluation inconsistencies identified:
1. Standardizes metrics across all model types
2. Implements proper cross-model comparison
3. Fixes best model selection logic
"""

import json
import os

def fix_evaluation_pipeline():
    """Fix evaluation pipeline inconsistencies"""
    
    print("🔧 FIXING EVALUATION PIPELINE INCONSISTENCIES")
    print("=" * 55)
    
    # Load results
    with open('pipeline_results/latest_pipeline_results.json', 'r') as f:
        results = json.load(f)
    
    core_models = results['stages']['core_training']['models']
    multiverse_models = results['stages']['multiverse_training']['models']
    specialized_models = results['stages']['multiverse_training']['specialized_models']
    
    print("🚨 ISSUE 1: INCONSISTENT METRICS")
    print("-" * 35)
    print("Core models use: 'mae', 'r2'")
    print("Multiverse models use: 'train_mae', 'val_mae', 'train_r2', 'val_r2'")
    print("Specialized models use: 'mae', 'r2'")
    print()
    
    print("🔧 FIX 1: STANDARDIZED EVALUATION")
    print("-" * 35)
    
    # Create standardized evaluation
    standardized_models = {}
    
    # Process core models
    for name, model in core_models.items():
        standardized_models[name] = {
            'model_type': 'CORE',
            'mae': model['performance']['mae'],
            'r2': model['performance']['r2'],
            'epochs': model['epochs_trained'],
            'overfitting_risk': 'UNKNOWN',  # No train/val split data
            'bias_risk': 'UNKNOWN'
        }
    
    # Process multiverse models (use validation metrics)
    for name, model in multiverse_models.items():
        perf = model['performance']
        mae_gap = perf['val_mae'] - perf['train_mae']
        overfitting_risk = 'HIGH' if mae_gap > 0.035 else 'MEDIUM' if mae_gap > 0.025 else 'LOW'
        bias_risk = 'HIGH' if abs(perf['bench_bias']) > 0.02 else 'LOW'
        
        standardized_models[name] = {
            'model_type': 'MULTIVERSE',
            'mae': perf['val_mae'],  # Use validation MAE for fair comparison
            'r2': perf['val_r2'],
            'epochs': 100,
            'overfitting_risk': overfitting_risk,
            'bias_risk': bias_risk,
            'mae_gap': mae_gap,
            'bench_bias': perf['bench_bias']
        }
    
    # Process specialized models
    for name, model in specialized_models.items():
        standardized_models[name] = {
            'model_type': 'SPECIALIZED',
            'mae': model['performance']['mae'],
            'r2': model['performance']['r2'],
            'epochs': 'UNKNOWN',
            'overfitting_risk': 'HIGH',  # Assume high due to complexity
            'bias_risk': 'UNKNOWN'
        }
    
    print("✅ STANDARDIZED MODEL RANKINGS:")
    print("-" * 35)
    
    # Sort by MAE (best first)
    mae_ranking = sorted(standardized_models.items(), key=lambda x: x[1]['mae'])
    
    print("BEST MAE PERFORMERS:")
    for i, (name, model) in enumerate(mae_ranking[:5], 1):
        risk_flags = []
        if model['overfitting_risk'] == 'HIGH':
            risk_flags.append('OVERFITTING')
        if model['bias_risk'] == 'HIGH':
            risk_flags.append('BIAS')
        
        risk_str = f" ⚠️ {', '.join(risk_flags)}" if risk_flags else " ✅"
        print(f"  {i}. {name} ({model['model_type']})")
        print(f"     MAE: {model['mae']:.3f}, R²: {model['r2']:.3f}{risk_str}")
    
    print()
    
    # Sort by R² (best first)
    r2_ranking = sorted(standardized_models.items(), key=lambda x: x[1]['r2'], reverse=True)
    
    print("BEST R² PERFORMERS:")
    for i, (name, model) in enumerate(r2_ranking[:5], 1):
        risk_flags = []
        if model['overfitting_risk'] == 'HIGH':
            risk_flags.append('OVERFITTING')
        if model['bias_risk'] == 'HIGH':
            risk_flags.append('BIAS')
        
        risk_str = f" ⚠️ {', '.join(risk_flags)}" if risk_flags else " ✅"
        print(f"  {i}. {name} ({model['model_type']})")
        print(f"     MAE: {model['mae']:.3f}, R²: {model['r2']:.3f}{risk_str}")
    
    print()
    print("🎯 PRODUCTION RECOMMENDATIONS:")
    print("-" * 35)
    
    # Find best production-ready models (low risk)
    production_ready = {
        name: model for name, model in standardized_models.items()
        if model['overfitting_risk'] in ['LOW', 'UNKNOWN'] and 
           model['bias_risk'] in ['LOW', 'UNKNOWN']
    }
    
    if production_ready:
        best_production_mae = min(production_ready.items(), key=lambda x: x[1]['mae'])
        best_production_r2 = max(production_ready.items(), key=lambda x: x[1]['r2'])
        
        print("RECOMMENDED FOR PRODUCTION:")
        print(f"  Primary (Best MAE): {best_production_mae[0]}")
        print(f"    MAE: {best_production_mae[1]['mae']:.3f}, R²: {best_production_mae[1]['r2']:.3f}")
        print(f"    Type: {best_production_mae[1]['model_type']}")
        
        if best_production_r2[0] != best_production_mae[0]:
            print(f"  Secondary (Best R²): {best_production_r2[0]}")
            print(f"    MAE: {best_production_r2[1]['mae']:.3f}, R²: {best_production_r2[1]['r2']:.3f}")
            print(f"    Type: {best_production_r2[1]['model_type']}")
    
    print()
    print("🚨 MODELS REQUIRING ATTENTION:")
    print("-" * 35)
    
    # Find problematic models
    problematic = {
        name: model for name, model in standardized_models.items()
        if model['overfitting_risk'] == 'HIGH' or model['bias_risk'] == 'HIGH'
    }
    
    for name, model in problematic.items():
        issues = []
        if model['overfitting_risk'] == 'HIGH':
            issues.append(f"Overfitting (gap: {model.get('mae_gap', 'N/A')})")
        if model['bias_risk'] == 'HIGH':
            issues.append(f"Bias (bench: {model.get('bench_bias', 'N/A')})")
        
        print(f"  ⚠️ {name}: {', '.join(issues)}")
    
    # Save standardized results
    standardized_results = {
        'evaluation_timestamp': '2025-07-13T11:45:00',
        'evaluation_method': 'standardized_cross_model',
        'models': standardized_models,
        'production_recommendations': {
            'primary': best_production_mae[0] if production_ready else None,
            'secondary': best_production_r2[0] if production_ready else None
        },
        'problematic_models': list(problematic.keys())
    }
    
    with open('pipeline_results/standardized_evaluation.json', 'w') as f:
        json.dump(standardized_results, f, indent=2)
    
    print()
    print("✅ EVALUATION PIPELINE FIXED!")
    print("📊 Standardized results saved to: pipeline_results/standardized_evaluation.json")
    print("🎯 Production recommendations generated")
    print("⚠️ Problematic models identified for retraining")

if __name__ == "__main__":
    fix_evaluation_pipeline()
