FROM python:3.11-slim

WORKDIR /app

RUN apt-get update && apt-get install -y gcc && rm -rf /var/lib/apt/lists/*
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY src/monitoring/ ./src/monitoring/
COPY *.py .

RUN useradd -m -u 1000 monitor && chown -R monitor:monitor /app
USER monitor

EXPOSE 8083

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8083/health || exit 1

CMD ["python", "src/monitoring/unified_monitoring_dashboard.py"]