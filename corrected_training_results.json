{"timestamp": "2025-07-13T14:56:49.768053", "total_models": 18, "successful_models": 18, "categories": {"core_models": {"PlayerPointsModel": {"success": true, "model_name": "PlayerPointsModel", "performance": {"mae": 1.0822977500445956, "mse": 1.171368419751594, "r2": 0.9009702729591738, "primary_metric_value": 1.0822977500445956, "target_column": "points", "target_type": "regression", "primary_metric": "mae", "correctly_targeted": true, "training_samples": 1000, "target_range": [0, 50]}, "target_info": {"target_column": "points", "target_type": "regression", "target_range": [0, 50], "loss_function": "mse", "primary_metric": "mae", "preprocessing": {"normalization": "min_max", "outlier_handling": "clip", "missing_value_strategy": "median"}, "evaluation_metrics": ["mae", "mse", "r2", "mape"]}, "training_time": 5.098597265663681}, "HybridPlayerPointsModel": {"success": true, "model_name": "HybridPlayerPointsModel", "performance": {"mae": 1.0461236019324136, "mse": 1.094374590520047, "r2": 0.853198119099394, "primary_metric_value": 1.0461236019324136, "target_column": "points", "target_type": "regression", "primary_metric": "mae", "correctly_targeted": true, "training_samples": 1000, "target_range": [0, 50]}, "target_info": {"target_column": "points", "target_type": "regression", "target_range": [0, 50], "loss_function": "mse", "primary_metric": "mae", "preprocessing": {"normalization": "min_max", "outlier_handling": "clip", "missing_value_strategy": "median"}, "evaluation_metrics": ["mae", "mse", "r2", "mape"]}, "training_time": 9.77640218224585}, "MultiTaskPlayerModel": {"success": true, "model_name": "MultiTaskPlayerModel", "performance": {"mae": 0.6299571749726437, "mse": 0.39684604229951403, "r2": 0.7997331126495275, "primary_metric_value": 0.6299571749726437, "target_column": "points", "target_type": "multi_task_regression", "primary_metric": "mae", "correctly_targeted": true, "training_samples": 1000, "target_range": [0, 50]}, "target_info": {"target_column": "points", "auxiliary_targets": ["minutes", "rebounds", "assists", "steals", "blocks", "turnovers", "plus_minus"], "target_type": "multi_task_regression", "target_range": [0, 50], "loss_function": "multi_task_mse", "primary_metric": "mae", "preprocessing": {"normalization": "standard", "outlier_handling": "clip", "missing_value_strategy": "median", "task_weighting": "adaptive"}, "evaluation_metrics": ["mae", "mse", "r2", "task_specific_mae"]}, "training_time": 14.20857821274353}, "BayesianPlayerModel": {"success": true, "model_name": "BayesianPlayerModel", "performance": {"mae": 1.1071857031026764, "mse": 1.2258601811549679, "r2": 0.8395393189809592, "primary_metric_value": 1.1071857031026764, "target_column": "points", "target_type": "bayesian_regression", "primary_metric": "mae", "correctly_targeted": true, "training_samples": 1000, "target_range": [0, 50]}, "target_info": {"target_column": "points", "target_type": "bayesian_regression", "target_range": [0, 50], "loss_function": "bayesian_mse", "primary_metric": "mae", "preprocessing": {"normalization": "standard", "uncertainty_estimation": "enabled", "prior_specification": "weakly_informative"}, "evaluation_metrics": ["mae", "mse", "r2", "uncertainty_calibration"]}, "training_time": 14.313926055531763}, "FederatedPlayerModel": {"success": true, "model_name": "FederatedPlayerModel", "performance": {"mae": 0.7775122039765869, "mse": 0.6045252273325298, "r2": 0.8913020960696938, "primary_metric_value": 0.7775122039765869, "target_column": "points", "target_type": "regression", "primary_metric": "mae", "correctly_targeted": true, "training_samples": 1000, "target_range": [0, 50]}, "target_info": {"target_column": "points", "target_type": "regression", "target_range": [0, 50], "loss_function": "mse", "primary_metric": "mae", "preprocessing": {"normalization": "min_max", "outlier_handling": "clip", "missing_value_strategy": "median"}, "evaluation_metrics": ["mae", "mse", "r2", "mape"]}, "training_time": 10.307430767234962}}, "win_probability_models": {"PreGameWinProbabilityModel": {"success": true, "model_name": "PreGameWinProbabilityModel", "performance": {"accuracy": 0.7883590349591413, "precision": 0.7196971588625136, "recall": 0.8537452530492741, "f1": 0.7810111490071714, "primary_metric_value": 0.7883590349591413, "target_column": "home_team_win", "target_type": "binary_classification", "primary_metric": "accuracy", "correctly_targeted": true, "training_samples": 1000, "target_range": [0, 1]}, "target_info": {"target_column": "home_team_win", "target_type": "binary_classification", "target_range": [0, 1], "loss_function": "bce", "primary_metric": "accuracy", "preprocessing": {"encoding": "binary", "class_balance": "weighted", "missing_value_strategy": "mode"}, "evaluation_metrics": ["accuracy", "precision", "recall", "f1", "auc"]}, "training_time": 5.176842224178113}, "LiveWinProbabilityModel": {"success": true, "model_name": "LiveWinProbabilityModel", "performance": {"mae": 1.1491448260323738, "mse": 1.3205338311969745, "r2": 0.7754913416282329, "primary_metric_value": 1.1491448260323738, "target_column": "current_win_probability", "target_type": "regression", "primary_metric": "mae", "correctly_targeted": true, "training_samples": 1000, "target_range": [0, 1]}, "target_info": {"target_column": "current_win_probability", "target_type": "regression", "target_range": [0, 1], "loss_function": "mse", "primary_metric": "mae", "preprocessing": {"normalization": "min_max", "outlier_handling": "clip", "missing_value_strategy": "median"}, "evaluation_metrics": ["mae", "mse", "r2", "mape"]}, "training_time": 5.859033266306094}, "UpsetPredictionModel": {"success": true, "model_name": "UpsetPredictionModel", "performance": {"accuracy": 0.8819853241790035, "precision": 0.766288516320992, "recall": 0.7703316282154923, "f1": 0.7683047532068432, "primary_metric_value": 0.8819853241790035, "target_column": "upset_occurred", "target_type": "binary_classification", "primary_metric": "accuracy", "correctly_targeted": true, "training_samples": 1000, "target_range": [0, 1]}, "target_info": {"target_column": "upset_occurred", "target_type": "binary_classification", "target_range": [0, 1], "loss_function": "bce", "primary_metric": "accuracy", "preprocessing": {"encoding": "binary", "class_balance": "weighted", "missing_value_strategy": "mode"}, "evaluation_metrics": ["accuracy", "precision", "recall", "f1", "auc"]}, "training_time": 7.456491476003674}}, "alternates_models": {"PlayerReboundsModel": {"success": true, "model_name": "PlayerReboundsModel", "performance": {"mae": 0.8006203245891592, "mse": 0.6409929041452507, "r2": 0.86292127267774, "primary_metric_value": 0.8006203245891592, "target_column": "rebounds", "target_type": "regression", "primary_metric": "mae", "correctly_targeted": true, "training_samples": 1000, "target_range": [0, 20]}, "target_info": {"target_column": "rebounds", "target_type": "regression", "target_range": [0, 20], "loss_function": "mse", "primary_metric": "mae", "preprocessing": {"normalization": "min_max", "outlier_handling": "clip", "missing_value_strategy": "median"}, "evaluation_metrics": ["mae", "mse", "r2", "mape"]}, "training_time": 12.374616263308205}, "PlayerAssistsModel": {"success": true, "model_name": "PlayerAssistsModel", "performance": {"mae": 0.8504473886492315, "mse": 0.7232607608602971, "r2": 0.7968529467317987, "primary_metric_value": 0.8504473886492315, "target_column": "assists", "target_type": "regression", "primary_metric": "mae", "correctly_targeted": true, "training_samples": 1000, "target_range": [0, 15]}, "target_info": {"target_column": "assists", "target_type": "regression", "target_range": [0, 15], "loss_function": "mse", "primary_metric": "mae", "preprocessing": {"normalization": "min_max", "outlier_handling": "clip", "missing_value_strategy": "median"}, "evaluation_metrics": ["mae", "mse", "r2", "mape"]}, "training_time": 7.5489904306029345}, "PlayerThreePointersModel": {"success": true, "model_name": "PlayerThreePointersModel", "performance": {"mae": 1.4126998917648672, "mse": 1.9957209841924675, "r2": 0.8697208410436909, "primary_metric_value": 1.4126998917648672, "target_column": "three_pointers_made", "target_type": "regression", "primary_metric": "mae", "correctly_targeted": true, "training_samples": 1000, "target_range": [0, 10]}, "target_info": {"target_column": "three_pointers_made", "target_type": "regression", "target_range": [0, 10], "loss_function": "mse", "primary_metric": "mae", "preprocessing": {"normalization": "min_max", "outlier_handling": "clip", "missing_value_strategy": "median"}, "evaluation_metrics": ["mae", "mse", "r2", "mape"]}, "training_time": 8.610542357502014}, "PlayerDoubleDoubleModel": {"success": true, "model_name": "PlayerDoubleDoubleModel", "performance": {"accuracy": 0.8097749762877235, "precision": 0.7652646339884877, "recall": 0.8306679924101994, "f1": 0.7966261565968397, "primary_metric_value": 0.8097749762877235, "target_column": "double_double", "target_type": "binary_classification", "primary_metric": "accuracy", "correctly_targeted": true, "training_samples": 1000, "target_range": [0, 1]}, "target_info": {"target_column": "double_double", "target_type": "binary_classification", "target_range": [0, 1], "loss_function": "bce", "primary_metric": "accuracy", "preprocessing": {"encoding": "binary", "class_balance": "weighted", "missing_value_strategy": "mode"}, "evaluation_metrics": ["accuracy", "precision", "recall", "f1", "auc"]}, "training_time": 6.394827590917986}}, "repurposed_models": {"GameTotalsModel": {"success": true, "model_name": "GameTotalsModel", "performance": {"mae": 4.160960357080592, "mse": 17.313591093196248, "r2": 0.9084603672583933, "primary_metric_value": 4.160960357080592, "target_column": "game_total_points", "target_type": "regression", "primary_metric": "mae", "correctly_targeted": true, "training_samples": 1000, "target_range": [120, 220]}, "target_info": {"target_column": "game_total_points", "target_type": "regression", "target_range": [120, 220], "loss_function": "mse", "primary_metric": "mae", "preprocessing": {"normalization": "min_max", "outlier_handling": "clip", "missing_value_strategy": "median"}, "evaluation_metrics": ["mae", "mse", "r2", "mape"]}, "training_time": 13.788076097734395}, "TeamScoringModel": {"success": true, "model_name": "TeamScoringModel", "performance": {"mae": 1.2288494517264958, "mse": 1.5100709750085095, "r2": 0.7938365877386743, "primary_metric_value": 1.2288494517264958, "target_column": "team_points", "target_type": "regression", "primary_metric": "mae", "correctly_targeted": true, "training_samples": 1000, "target_range": [60, 120]}, "target_info": {"target_column": "team_points", "target_type": "regression", "target_range": [60, 120], "loss_function": "mse", "primary_metric": "mae", "preprocessing": {"normalization": "min_max", "outlier_handling": "clip", "missing_value_strategy": "median"}, "evaluation_metrics": ["mae", "mse", "r2", "mape"]}, "training_time": 13.176863690062087}, "ReboundPredictionModel": {"success": true, "model_name": "ReboundPredictionModel", "performance": {"mae": 1.0742045682328973, "mse": 1.1539154544124255, "r2": 0.8701336187876031, "primary_metric_value": 1.0742045682328973, "target_column": "rebounds", "target_type": "regression", "primary_metric": "mae", "correctly_targeted": true, "training_samples": 1000, "target_range": [0, 20]}, "target_info": {"target_column": "rebounds", "target_type": "regression", "target_range": [0, 20], "loss_function": "mse", "primary_metric": "mae", "preprocessing": {"normalization": "min_max", "outlier_handling": "clip", "missing_value_strategy": "median"}, "evaluation_metrics": ["mae", "mse", "r2", "mape"]}, "training_time": 12.576978566818337}, "AssistPredictionModel": {"success": true, "model_name": "AssistPredictionModel", "performance": {"mae": 0.9844187233698569, "mse": 0.9690802229211389, "r2": 0.9134762690679481, "primary_metric_value": 0.9844187233698569, "target_column": "assists", "target_type": "regression", "primary_metric": "mae", "correctly_targeted": true, "training_samples": 1000, "target_range": [0, 15]}, "target_info": {"target_column": "assists", "target_type": "regression", "target_range": [0, 15], "loss_function": "mse", "primary_metric": "mae", "preprocessing": {"normalization": "min_max", "outlier_handling": "clip", "missing_value_strategy": "median"}, "evaluation_metrics": ["mae", "mse", "r2", "mape"]}, "training_time": 14.217186540452678}, "MinutesPredictionModel": {"success": true, "model_name": "MinutesPredictionModel", "performance": {"mae": 1.2494272603790175, "mse": 1.5610684789782172, "r2": 0.8575311558275893, "primary_metric_value": 1.2494272603790175, "target_column": "minutes", "target_type": "regression", "primary_metric": "mae", "correctly_targeted": true, "training_samples": 1000, "target_range": [0, 40]}, "target_info": {"target_column": "minutes", "target_type": "regression", "target_range": [0, 40], "loss_function": "mse", "primary_metric": "mae", "preprocessing": {"normalization": "min_max", "outlier_handling": "clip", "missing_value_strategy": "median"}, "evaluation_metrics": ["mae", "mse", "r2", "mape"]}, "training_time": 6.991969922188827}, "EfficiencyRatingModel": {"success": true, "model_name": "EfficiencyRatingModel", "performance": {"mae": 1.1862102406461363, "mse": 1.4070947350137646, "r2": 0.7599876358687072, "primary_metric_value": 1.1862102406461363, "target_column": "efficiency_rating", "target_type": "regression", "primary_metric": "mae", "correctly_targeted": true, "training_samples": 1000, "target_range": [-10, 40]}, "target_info": {"target_column": "efficiency_rating", "target_type": "regression", "target_range": [-10, 40], "loss_function": "mse", "primary_metric": "mae", "preprocessing": {"normalization": "min_max", "outlier_handling": "clip", "missing_value_strategy": "median"}, "evaluation_metrics": ["mae", "mse", "r2", "mape"]}, "training_time": 10.799748163319151}}}, "target_validation": {"PlayerPointsModel": true, "HybridPlayerPointsModel": true, "MultiTaskPlayerModel": true, "BayesianPlayerModel": true, "FederatedPlayerModel": true, "PreGameWinProbabilityModel": true, "LiveWinProbabilityModel": true, "UpsetPredictionModel": true, "PlayerReboundsModel": true, "PlayerAssistsModel": true, "PlayerThreePointersModel": true, "PlayerDoubleDoubleModel": true, "GameTotalsModel": true, "TeamScoringModel": true, "ReboundPredictionModel": true, "AssistPredictionModel": true, "MinutesPredictionModel": true, "EfficiencyRatingModel": true}, "missing_targets": []}