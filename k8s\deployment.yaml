apiVersion: apps/v1
kind: Deployment
metadata:
  name: wnba-prediction
  namespace: wnba-prediction
spec:
  replicas: 3
  selector:
    matchLabels:
      app: wnba-prediction
  template:
    metadata:
      labels:
        app: wnba-prediction
    spec:
      containers:
      - name: wnba-prediction
        image: wnba-prediction:latest
        ports:
        - containerPort: 8080
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: wnba-secrets
              key: database-url
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: wnba-prediction-service
  namespace: wnba-prediction
spec:
  selector:
    app: wnba-prediction
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer