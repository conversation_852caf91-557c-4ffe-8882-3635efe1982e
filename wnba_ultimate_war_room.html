<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏀 WNBA ULTIMATE WAR ROOM</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --vampire-black: #050707;
            --quartz: #4C4C4D;
            --pearl: #EFE3C6;
            --princeton-orange: #F57B20;
            --alert-red: #FF1744;
            --success-green: #00E676;
            --warning-amber: #FFD600;
            --info-blue: #00B0FF;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: var(--vampire-black);
            color: var(--pearl);
            font-family: 'Courier New', monospace;
            overflow: hidden;
            height: 100vh;
        }
        
        .ultimate-war-room {
            display: grid;
            grid-template-columns: 280px 1fr 320px;
            grid-template-rows: 50px 1fr 180px;
            height: 100vh;
            gap: 1px;
            background: var(--vampire-black);
            grid-template-areas:
                "header header header"
                "games-panel live-quad momentum-radar"
                "props-tracker props-tracker value-scanner";
        }
        
        .panel {
            background: rgba(76, 76, 77, 0.15);
            border: 1px solid var(--quartz);
            padding: 8px;
            overflow: hidden;
        }
        
        .header-panel {
            grid-area: header;
            background: linear-gradient(90deg, var(--vampire-black), var(--princeton-orange));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;
            border-bottom: 2px solid var(--princeton-orange);
        }
        
        .games-panel {
            grid-area: games-panel;
            overflow-y: auto;
        }
        
        .live-quad {
            grid-area: live-quad;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 8px;
            padding: 10px;
        }
        
        .momentum-radar {
            grid-area: momentum-radar;
            overflow-y: auto;
        }
        
        .props-tracker {
            grid-area: props-tracker;
            overflow-y: auto;
        }
        
        .value-scanner {
            grid-area: value-scanner;
            overflow-y: auto;
        }
        
        .game-card {
            background: rgba(5, 7, 7, 0.9);
            border: 1px solid var(--quartz);
            margin: 4px 0;
            padding: 8px;
            border-left: 3px solid var(--princeton-orange);
            cursor: pointer;
            transition: all 0.2s;
            font-size: 11px;
        }
        
        .game-card.selected {
            border-left-color: var(--pearl);
            background: rgba(245, 123, 32, 0.1);
        }
        
        .game-card.live {
            border-left-color: var(--alert-red);
            animation: pulse 2s infinite;
        }
        
        .game-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
            font-weight: bold;
        }
        
        .teams {
            color: var(--pearl);
            font-size: 12px;
        }
        
        .quarter-time {
            color: var(--princeton-orange);
            font-size: 10px;
        }
        
        .win-prob {
            display: flex;
            justify-content: space-between;
            font-size: 10px;
            margin: 2px 0;
        }
        
        .wp-up { color: var(--success-green); }
        .wp-down { color: var(--alert-red); }
        .wp-neutral { color: var(--warning-amber); }
        
        .prediction {
            font-size: 9px;
            color: var(--quartz);
        }
        
        .live-game-panel {
            background: rgba(5, 7, 7, 0.95);
            border: 1px solid var(--alert-red);
            padding: 8px;
            position: relative;
            overflow: hidden;
        }
        
        .live-game-panel::before {
            content: "LIVE";
            position: absolute;
            top: 2px;
            right: 2px;
            background: var(--alert-red);
            color: white;
            padding: 1px 4px;
            font-size: 8px;
            border-radius: 2px;
        }
        
        .game-score {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 5px 0;
        }
        
        .team-score {
            font-size: 18px;
            font-weight: bold;
        }
        
        .score-arrow {
            color: var(--success-green);
            font-size: 14px;
        }
        
        .win-prob-bar {
            height: 4px;
            background: var(--quartz);
            border-radius: 2px;
            overflow: hidden;
            margin: 3px 0;
        }
        
        .win-prob-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--alert-red), var(--success-green));
            transition: width 0.5s ease;
        }
        
        .shot-chart {
            height: 60px;
            background: rgba(76, 76, 77, 0.2);
            border-radius: 4px;
            margin: 5px 0;
            position: relative;
            overflow: hidden;
        }
        
        .shot-dot {
            position: absolute;
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: var(--success-green);
        }
        
        .shot-dot.miss {
            background: var(--alert-red);
        }
        
        .momentum-section {
            margin: 8px 0;
            padding: 6px;
            background: rgba(5, 7, 7, 0.8);
            border-radius: 4px;
        }
        
        .hot-run {
            color: var(--princeton-orange);
            font-size: 10px;
            margin: 2px 0;
        }
        
        .risk-flag {
            color: var(--alert-red);
            font-size: 10px;
            margin: 2px 0;
        }
        
        .prediction-battle {
            background: rgba(76, 76, 77, 0.3);
            padding: 6px;
            border-radius: 4px;
            margin: 5px 0;
        }
        
        .delta-positive { color: var(--success-green); }
        .delta-negative { color: var(--alert-red); }
        
        .prop-item {
            display: flex;
            justify-content: space-between;
            padding: 4px 6px;
            margin: 2px 0;
            border-radius: 3px;
            font-size: 11px;
        }
        
        .prop-hit {
            background: rgba(0, 230, 118, 0.2);
            border-left: 2px solid var(--success-green);
        }
        
        .prop-live {
            background: rgba(255, 214, 0, 0.2);
            border-left: 2px solid var(--warning-amber);
        }
        
        .prop-miss {
            background: rgba(255, 23, 68, 0.2);
            border-left: 2px solid var(--alert-red);
        }
        
        .value-bet {
            background: rgba(0, 230, 118, 0.15);
            border: 1px solid var(--success-green);
            padding: 6px;
            margin: 3px 0;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .value-bet:hover {
            background: rgba(0, 230, 118, 0.3);
            transform: translateX(2px);
        }
        
        .edge-percentage {
            color: var(--success-green);
            font-weight: bold;
            font-size: 12px;
        }
        
        .quick-actions {
            display: flex;
            gap: 4px;
            margin-top: 4px;
        }
        
        .action-btn {
            background: var(--princeton-orange);
            color: var(--vampire-black);
            border: none;
            padding: 2px 6px;
            border-radius: 2px;
            font-size: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .action-btn:hover {
            background: var(--pearl);
        }
        
        .status-bar {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 11px;
        }
        
        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        .live-dot { background: var(--alert-red); }
        .props-dot { background: var(--success-green); }
        .edges-dot { background: var(--warning-amber); }
        
        h1 {
            font-size: 16px;
            color: var(--pearl);
        }
        
        h2 {
            font-size: 12px;
            color: var(--princeton-orange);
            margin-bottom: 6px;
            border-bottom: 1px solid var(--quartz);
            padding-bottom: 2px;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .scrollable {
            max-height: 100%;
            overflow-y: auto;
        }
        
        .scrollable::-webkit-scrollbar {
            width: 3px;
        }
        
        .scrollable::-webkit-scrollbar-track {
            background: var(--vampire-black);
        }
        
        .scrollable::-webkit-scrollbar-thumb {
            background: var(--quartz);
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="ultimate-war-room">
        <!-- HEADER COMMAND BAR -->
        <div class="panel header-panel">
            <div>
                <h1>🏀 WNBA WAR ROOM » 4 LIVE GAMES » 127 PROPS » 23 EDGES</h1>
            </div>
            <div class="status-bar">
                <div class="status-indicator">
                    <div class="status-dot live-dot"></div>
                    <span>4 LIVE</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot props-dot"></div>
                    <span>127 PROPS</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot edges-dot"></div>
                    <span>23 EDGES</span>
                </div>
            </div>
        </div>

        <!-- GAMES PANEL -->
        <div class="panel games-panel">
            <h2>GAMES</h2>
            <div class="scrollable">
                <div class="game-card live selected" data-game="lv-nyl">
                    <div class="game-header">
                        <div class="teams">[LV@NYL]</div>
                        <div class="quarter-time">Q3 6:24</div>
                    </div>
                    <div class="win-prob">
                        <span class="wp-up">WP: 65% ▲12%</span>
                    </div>
                    <div class="prediction">PRED: 72-80</div>
                </div>

                <div class="game-card live" data-game="con-atl">
                    <div class="game-header">
                        <div class="teams">[CON@ATL]</div>
                        <div class="quarter-time">Q2 3:15</div>
                    </div>
                    <div class="win-prob">
                        <span class="wp-neutral">WP: 52% →</span>
                    </div>
                    <div class="prediction">PRED: 45-42✗</div>
                </div>

                <div class="game-card" data-game="sea-pho">
                    <div class="game-header">
                        <div class="teams">[SEA@PHO]</div>
                        <div class="quarter-time">PREGAME</div>
                    </div>
                    <div class="win-prob">
                        <span class="wp-up">WP: 68%</span>
                    </div>
                    <div class="prediction">PRED: SEA+4</div>
                </div>

                <div class="game-card" data-game="chi-min">
                    <div class="game-header">
                        <div class="teams">[CHI@MIN]</div>
                        <div class="quarter-time">PREGAME</div>
                    </div>
                    <div class="win-prob">
                        <span class="wp-neutral">WP: 55%</span>
                    </div>
                    <div class="prediction">PRED: MIN+2</div>
                </div>
            </div>
        </div>

        <!-- LIVE QUAD-FEED -->
        <div class="panel live-quad">
            <div class="live-game-panel">
                <div style="font-weight: bold; font-size: 10px; margin-bottom: 4px;">LV ACES vs NY LIB</div>
                <div class="game-score">
                    <span class="team-score">76</span>
                    <span class="score-arrow">──►</span>
                    <span class="team-score" style="color: var(--success-green);">78 ✓</span>
                </div>
                <div style="display: flex; justify-content: space-between; font-size: 9px;">
                    <span class="wp-down">WP:35%▼</span>
                    <span class="wp-up">WP:65%▲</span>
                </div>
                <div class="win-prob-bar">
                    <div class="win-prob-fill" style="width: 65%;"></div>
                </div>
                <div style="text-align: center; font-size: 8px; color: var(--quartz);">LIVE WIN PROB</div>
            </div>

            <div class="live-game-panel">
                <div style="font-weight: bold; font-size: 10px; margin-bottom: 4px;">CON SUN vs ATL DREAM</div>
                <div class="game-score">
                    <span class="team-score">42</span>
                    <span style="color: var(--quartz);">◄─►</span>
                    <span class="team-score">40</span>
                </div>
                <div style="display: flex; justify-content: space-between; font-size: 9px;">
                    <span class="wp-neutral">WP:52%</span>
                    <span class="wp-down">WP:48%▼</span>
                </div>
                <div class="shot-chart">
                    <div style="text-align: center; font-size: 8px; padding: 2px; color: var(--quartz);">SHOT CHART</div>
                    <div class="shot-dot" style="left: 20%; top: 30%;"></div>
                    <div class="shot-dot" style="left: 30%; top: 50%;"></div>
                    <div class="shot-dot miss" style="left: 60%; top: 40%;"></div>
                    <div class="shot-dot" style="left: 70%; top: 25%;"></div>
                    <div class="shot-dot" style="left: 80%; top: 45%;"></div>
                </div>
            </div>

            <div class="live-game-panel" style="border-color: var(--quartz);">
                <div style="font-weight: bold; font-size: 10px; margin-bottom: 4px;">SEA STM vs PHX MER</div>
                <div class="game-score">
                    <span class="team-score">-</span>
                    <span style="color: var(--quartz);">vs</span>
                    <span class="team-score">-</span>
                </div>
                <div style="display: flex; justify-content: space-between; font-size: 9px;">
                    <span class="wp-up">WP:68%</span>
                    <span class="wp-down">WP:32%</span>
                </div>
                <div style="text-align: center; font-size: 8px; color: var(--princeton-orange); margin-top: 8px;">PRED: SEA by 4</div>
            </div>

            <div class="live-game-panel" style="border-color: var(--quartz);">
                <div style="font-weight: bold; font-size: 10px; margin-bottom: 4px;">CHI SKY vs MIN LYN</div>
                <div class="game-score">
                    <span class="team-score">-</span>
                    <span style="color: var(--quartz);">vs</span>
                    <span class="team-score">-</span>
                </div>
                <div style="display: flex; justify-content: space-between; font-size: 9px;">
                    <span class="wp-down">WP:45%</span>
                    <span class="wp-up">WP:55%</span>
                </div>
                <div style="text-align: center; font-size: 8px; color: var(--princeton-orange); margin-top: 8px;">PRED: MIN by 2</div>
            </div>
        </div>

        <!-- MOMENTUM RADAR -->
        <div class="panel momentum-radar">
            <h2>MOMENTUM RADAR</h2>
            <div class="scrollable">
                <div class="momentum-section">
                    <div style="color: var(--princeton-orange); font-weight: bold; font-size: 11px;">🔥 HOT RUNS</div>
                    <div class="hot-run">• NYL 8-0 run</div>
                    <div class="hot-run" style="margin-left: 8px; font-size: 9px;">(Stewie 3 AST)</div>
                    <div class="hot-run">• Aces +15% WP</div>
                    <div class="hot-run" style="margin-left: 8px; font-size: 9px;">(A'ja block)</div>
                </div>

                <div class="momentum-section">
                    <div style="color: var(--alert-red); font-weight: bold; font-size: 11px;">⚠️ RISK FLAGS</div>
                    <div class="risk-flag">• Jones injury</div>
                    <div class="risk-flag" style="margin-left: 8px; font-size: 9px;">(CON ▼18% WP)</div>
                    <div class="risk-flag">• Foul trouble</div>
                    <div class="risk-flag" style="margin-left: 8px; font-size: 9px;">(Taurasi 4 PF)</div>
                </div>

                <div class="prediction-battle">
                    <div style="color: var(--pearl); font-weight: bold; font-size: 11px; margin-bottom: 4px;">PREDICTION BATTLE</div>
                    <div style="font-size: 10px;">
                        <div>LV@NYL:</div>
                        <div>ACT: NYL +2 ✓</div>
                        <div>PRED: NYL +4</div>
                        <div class="delta-positive">DELTA: +2 PTS</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- PROPS TRACKER -->
        <div class="panel props-tracker">
            <h2>PROPS TRACKER (LIVE)</h2>
            <div class="scrollable">
                <div class="prop-item prop-hit">
                    <div>
                        <div>✅ HIT: A'ja O15.5 PTS (LV)</div>
                        <div style="font-size: 9px; color: var(--quartz);">» 18 PTS @Q3</div>
                    </div>
                </div>
                <div class="prop-item prop-live">
                    <div>
                        <div>⏳ LIVE: Clark 8+ AST (IND)</div>
                        <div style="font-size: 9px; color: var(--quartz);">» 5 AST @Q2</div>
                    </div>
                </div>
                <div class="prop-item prop-miss">
                    <div>
                        <div>❌ MISS: Parker <10 REB (CHI)</div>
                        <div style="font-size: 9px; color: var(--quartz);">» 12 REB @FINAL</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- VALUE SCANNER -->
        <div class="panel value-scanner">
            <h2>VALUE SCANNER</h2>
            <div class="scrollable">
                <div class="value-bet">
                    <div>
                        <div style="font-weight: bold;">🔥 <span class="edge-percentage">+32% EDGE:</span></div>
                        <div>A'ja O24.5 PTS</div>
                        <div style="font-size: 9px; color: var(--quartz);">Implied: 60% | Model: 92%</div>
                    </div>
                </div>

                <div style="margin: 8px 0;">
                    <div style="color: var(--princeton-orange); font-weight: bold; font-size: 11px;">⚡ QUICK ACTIONS:</div>
                    <div class="quick-actions">
                        <button class="action-btn">BET</button>
                        <button class="action-btn">ALERT</button>
                        <button class="action-btn">SIM</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class UltimateWNBAWarRoom {
            constructor() {
                this.isActive = true;
                this.updateInterval = null;
                this.selectedGame = 'lv-nyl';
                this.gameStates = new Map();
                this.propsData = new Map();
                this.momentumData = new Map();

                this.init();
            }

            init() {
                this.setupEventListeners();
                this.initializeGameStates();
                this.startUltimateUpdates();

                console.log('🎯 ULTIMATE WNBA WAR ROOM ONLINE');
            }

            setupEventListeners() {
                // Game selection
                document.querySelectorAll('.game-card').forEach(card => {
                    card.addEventListener('click', (e) => {
                        this.selectGame(card);
                    });
                });

                // Quick action buttons
                document.querySelectorAll('.action-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        this.handleQuickAction(e.target.textContent);
                    });
                });

                // Value bet interactions
                document.querySelectorAll('.value-bet').forEach(bet => {
                    bet.addEventListener('click', (e) => {
                        this.highlightValueBet(bet);
                    });
                });
            }

            selectGame(gameCard) {
                // Remove previous selection
                document.querySelectorAll('.game-card').forEach(card => {
                    card.classList.remove('selected');
                });

                // Select new game
                gameCard.classList.add('selected');
                this.selectedGame = gameCard.dataset.game;

                // Update live quad focus
                this.updateQuadFocus();

                console.log(`🎯 Selected game: ${this.selectedGame}`);
            }

            updateQuadFocus() {
                // Highlight selected game in quad view
                const quadPanels = document.querySelectorAll('.live-game-panel');
                quadPanels.forEach(panel => {
                    panel.style.borderWidth = '1px';
                });

                // Highlight selected game panel
                const gameIndex = ['lv-nyl', 'con-atl', 'sea-pho', 'chi-min'].indexOf(this.selectedGame);
                if (gameIndex >= 0 && quadPanels[gameIndex]) {
                    quadPanels[gameIndex].style.borderWidth = '2px';
                    quadPanels[gameIndex].style.borderColor = 'var(--pearl)';
                }
            }

            startUltimateUpdates() {
                if (this.updateInterval) {
                    clearInterval(this.updateInterval);
                }

                this.updateInterval = setInterval(() => {
                    if (this.isActive) {
                        this.updateLiveScores();
                        this.updateWinProbabilities();
                        this.updateMomentumRadar();
                        this.updatePropsTracker();
                        this.updateValueScanner();
                        this.updateShotChart();
                    }
                }, 1500); // Ultra-fast 1.5 second updates
            }

            updateLiveScores() {
                // Update live game scores
                const liveGames = document.querySelectorAll('.live-game-panel');

                liveGames.forEach((panel, index) => {
                    if (index < 2) { // Only first two games are live
                        const scores = panel.querySelectorAll('.team-score');

                        if (Math.random() > 0.8) { // 20% chance of score change
                            const homeScore = parseInt(scores[0].textContent) || 0;
                            const awayScore = parseInt(scores[2].textContent) || 0;

                            if (Math.random() > 0.5) {
                                scores[0].textContent = homeScore + (Math.random() > 0.7 ? 3 : 2);
                                this.addMomentumEvent(`Score update: ${scores[0].textContent}`);
                            } else {
                                scores[2].textContent = awayScore + (Math.random() > 0.7 ? 3 : 2);
                                this.addMomentumEvent(`Score update: ${scores[2].textContent}`);
                            }
                        }
                    }
                });
            }

            updateWinProbabilities() {
                // Update win probability bars and percentages
                document.querySelectorAll('.win-prob-fill').forEach(bar => {
                    const currentWidth = parseInt(bar.style.width) || 50;
                    const change = (Math.random() - 0.5) * 6; // ±3% change
                    const newWidth = Math.max(20, Math.min(80, currentWidth + change));

                    bar.style.width = newWidth + '%';
                });

                // Update WP text in game cards
                document.querySelectorAll('.win-prob span').forEach(wp => {
                    if (wp.textContent.includes('WP:')) {
                        const currentWP = parseInt(wp.textContent.match(/\d+/)[0]);
                        const change = Math.floor((Math.random() - 0.5) * 4); // ±2% change
                        const newWP = Math.max(20, Math.min(80, currentWP + change));

                        const direction = change > 0 ? '▲' : change < 0 ? '▼' : '→';
                        const className = change > 0 ? 'wp-up' : change < 0 ? 'wp-down' : 'wp-neutral';

                        wp.textContent = `WP: ${newWP}% ${direction}${Math.abs(change)}%`;
                        wp.className = className;
                    }
                });
            }

            updateMomentumRadar() {
                if (Math.random() > 0.9) { // 10% chance of new momentum event
                    const events = [
                        '• SEA 6-0 run (Loyd hot)',
                        '• PHX timeout called',
                        '• MIN foul trouble',
                        '• CHI momentum shift',
                        '• Pace increase +12%',
                        '• Defensive stop streak'
                    ];

                    const randomEvent = events[Math.floor(Math.random() * events.length)];
                    this.addMomentumEvent(randomEvent);
                }
            }

            addMomentumEvent(event) {
                const hotRuns = document.querySelector('.momentum-section .hot-run').parentElement;
                const newEvent = document.createElement('div');
                newEvent.className = 'hot-run';
                newEvent.textContent = event;

                hotRuns.insertBefore(newEvent, hotRuns.children[1]);

                // Keep only 4 most recent events
                while (hotRuns.children.length > 5) {
                    hotRuns.removeChild(hotRuns.lastChild);
                }
            }

            updatePropsTracker() {
                if (Math.random() > 0.85) { // 15% chance of new prop result
                    const propsContainer = document.querySelector('.props-tracker .scrollable');
                    const isHit = Math.random() > 0.35; // 65% hit rate

                    const props = [
                        'Loyd O18.5 PTS',
                        'Collier O9.5 REB',
                        'Taurasi O2.5 3PM',
                        'Copper O4.5 AST',
                        'Howard O15.5 PTS'
                    ];

                    const randomProp = props[Math.floor(Math.random() * props.length)];
                    const status = isHit ? 'HIT' : 'MISS';
                    const icon = isHit ? '✅' : '❌';
                    const className = isHit ? 'prop-hit' : 'prop-miss';

                    const newProp = document.createElement('div');
                    newProp.className = `prop-item ${className}`;
                    newProp.innerHTML = `
                        <div>
                            <div>${icon} ${status}: ${randomProp}</div>
                            <div style="font-size: 9px; color: var(--quartz);">» Live update</div>
                        </div>
                    `;

                    propsContainer.insertBefore(newProp, propsContainer.children[1]);

                    // Keep only 6 most recent props
                    while (propsContainer.children.length > 6) {
                        propsContainer.removeChild(propsContainer.lastChild);
                    }
                }
            }

            updateValueScanner() {
                // Update edge percentages
                document.querySelectorAll('.edge-percentage').forEach(edge => {
                    const currentEdge = parseInt(edge.textContent.replace('%', '').replace('+', ''));
                    const change = (Math.random() - 0.5) * 6; // ±3% change
                    const newEdge = Math.max(5, Math.min(50, currentEdge + change));
                    edge.textContent = `+${Math.round(newEdge)}%`;

                    // Color coding based on edge
                    if (newEdge > 25) {
                        edge.style.color = 'var(--success-green)';
                    } else if (newEdge > 15) {
                        edge.style.color = 'var(--warning-amber)';
                    } else {
                        edge.style.color = 'var(--princeton-orange)';
                    }
                });
            }

            updateShotChart() {
                // Add new shot dots occasionally
                if (Math.random() > 0.9) { // 10% chance
                    const shotChart = document.querySelector('.shot-chart');
                    const newShot = document.createElement('div');
                    newShot.className = Math.random() > 0.6 ? 'shot-dot' : 'shot-dot miss';
                    newShot.style.left = Math.random() * 80 + 10 + '%';
                    newShot.style.top = Math.random() * 40 + 20 + '%';

                    shotChart.appendChild(newShot);

                    // Remove old shots
                    if (shotChart.children.length > 8) {
                        shotChart.removeChild(shotChart.children[1]); // Keep title
                    }
                }
            }

            handleQuickAction(action) {
                switch(action) {
                    case 'BET':
                        this.placeBet();
                        break;
                    case 'ALERT':
                        this.setAlert();
                        break;
                    case 'SIM':
                        this.runSimulation();
                        break;
                }
            }

            placeBet() {
                // Flash bet button
                const betBtn = document.querySelector('.action-btn');
                betBtn.style.background = 'var(--success-green)';
                setTimeout(() => {
                    betBtn.style.background = 'var(--princeton-orange)';
                }, 500);

                console.log('💰 Bet placed on high-value opportunity');
            }

            setAlert() {
                // Add alert to momentum radar
                this.addMomentumEvent('🚨 ALERT SET: Value threshold reached');
                console.log('🚨 Alert configured for value opportunity');
            }

            runSimulation() {
                // Flash simulation
                document.body.style.filter = 'brightness(1.2)';
                setTimeout(() => {
                    document.body.style.filter = 'brightness(1)';
                }, 300);

                console.log('⚡ Running game simulation...');
            }

            highlightValueBet(betElement) {
                betElement.style.background = 'rgba(0, 230, 118, 0.5)';
                betElement.style.transform = 'translateX(4px)';

                setTimeout(() => {
                    betElement.style.background = 'rgba(0, 230, 118, 0.15)';
                    betElement.style.transform = 'translateX(0)';
                }, 1000);
            }

            initializeGameStates() {
                this.gameStates.set('lv-nyl', { live: true, quarter: 'Q3', time: '6:24' });
                this.gameStates.set('con-atl', { live: true, quarter: 'Q2', time: '3:15' });
                this.gameStates.set('sea-pho', { live: false, status: 'PREGAME' });
                this.gameStates.set('chi-min', { live: false, status: 'PREGAME' });

                console.log('🏀 Game states initialized');
                console.log('📊 127 props loaded for monitoring');
                console.log('💰 23 value opportunities detected');
            }
        }

        // Initialize Ultimate War Room
        document.addEventListener('DOMContentLoaded', () => {
            window.ultimateWarRoom = new UltimateWNBAWarRoom();
        });

        // Performance monitoring
        window.addEventListener('load', () => {
            console.log('🎯 ULTIMATE WNBA WAR ROOM OPERATIONAL');
            console.log('⚡ 1.5-second update cycle ACTIVE');
            console.log('🔴 Multi-game quad-feed ONLINE');
            console.log('📡 Momentum radar SCANNING');
        });
    </script>
</body>
</html>
