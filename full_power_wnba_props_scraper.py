#!/usr/bin/env python3
"""
🏀 FULL-POWER WNBA PROPS SCRAPER - SELENIUM VERSION
===================================================

REAL WNBA PLAYER PROPS SCRAPER with:
- ✅ Full Selenium WebDriver Power
- ✅ Auto ChromeDriver Management
- ✅ Real Sportsbook Scraping
- ✅ Live Player Props Extraction
- ✅ Anti-Detection Measures
- ✅ Professional Error Handling

Version: 3.0 (Full Power)
Date: 2025-07-13
"""

import time
import json
import re
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Selenium imports
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException

# WebDriver Manager for automatic ChromeDriver management
from webdriver_manager.chrome import ChromeDriverManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FullPowerWNBAPropsScaper:
    """Full-Power WNBA Props Scraper using Selenium"""
    
    def __init__(self, headless: bool = True, wait_timeout: int = 20):
        """
        Initialize the full-power props scraper
        
        Args:
            headless: Run browser in headless mode
            wait_timeout: Maximum wait time for elements
        """
        self.headless = headless
        self.wait_timeout = wait_timeout
        self.driver = None
        self.wait = None
        
        # Sportsbook configurations with real URLs and selectors
        self.sportsbooks = {
# REMOVED: DFS component
# REMOVED: DFS component
# REMOVED: DFS component
                "selectors": {
# REMOVED: DFS component
                    "game_containers": "div.sportsbook-table",
                    "prop_rows": "tr.sportsbook-table__row",
                    "player_name": "span.sportsbook-row-name",
                    "market_name": "span.sportsbook-outcome-cell__label",
                    "line_value": "span.sportsbook-outcome-cell__line",
                    "odds_value": "span.sportsbook-odds",
                    "prop_sections": "div[data-testid='event-cell']"
                },
                "wait_elements": [
                    "div.sportsbook-table",
                    "span.sportsbook-row-name",
                    "div[data-testid='event-cell']"
                ]
            },
# REMOVED: DFS component
# REMOVED: DFS component
# REMOVED: DFS component
                "selectors": {
# REMOVED: DFS component
                    "game_containers": "div[data-test-id='ArrowMarketGroup']",
                    "prop_rows": "div[data-test-id='SelectionRow']",
                    "player_name": "h4[data-test-id='ArrowMarketGroupHeader']",
                    "market_name": "span[data-test-id='MarketName']",
                    "line_value": "span[data-test-id='SelectionHandicap']",
                    "odds_value": "span[data-test-id='SelectionPrice']",
                    "prop_sections": "div[data-test-id='MarketGroup']"
                },
                "wait_elements": [
                    "div[data-test-id='ArrowMarketGroup']",
                    "h4[data-test-id='ArrowMarketGroupHeader']",
                    "div[data-test-id='MarketGroup']"
                ]
            },
            "BetMGM": {
                "url": "https://sports.az.betmgm.com/en/sports/basketball-11/betting/usa-14/wnba-9008",
                "player_props_url": "https://sports.az.betmgm.com/en/sports/basketball-11/betting/usa-14/wnba-9008/player-props",
                "selectors": {
                    # Updated selectors for BetMGM
                    "game_containers": "div.option-group",
                    "prop_rows": "div.option-pick",
                    "player_name": "div.option-label",
                    "market_name": "span.option-name",
                    "line_value": "span.option-value",
                    "odds_value": "span.option-odds",
                    "prop_sections": "div.market-container"
                },
                "wait_elements": [
                    "div.option-group",
                    "div.option-label",
                    "div.market-container"
                ]
            }
        }
        
        # WNBA player names for recognition
        self.wnba_players = [
            "A'ja Wilson", "Breanna Stewart", "Diana Taurasi", "Sabrina Ionescu",
            "Alyssa Thomas", "Kelsey Plum", "Jonquel Jones", "Napheesa Collier",
            "Brittney Griner", "Candace Parker", "Skylar Diggins-Smith", "Jewell Loyd",
            "Kahleah Copper", "Courtney Williams", "Dearica Hamby", "Nneka Ogwumike",
            "Chelsea Gray", "Jackie Young", "Rhyne Howard", "Aliyah Boston",
            "Caitlin Clark", "Angel Reese", "Cameron Brink", "Rickea Jackson"
        ]
        
        logger.info("🏀 Full-Power WNBA Props Scraper initialized")
    
    def _setup_driver(self):
        """Setup Chrome WebDriver with optimal configuration"""
        
        try:
            # Chrome options for optimal scraping
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument("--headless")
                logger.info("🔧 Running in headless mode")
            else:
                logger.info("🔧 Running with visible browser")
            
            # Performance and stealth options
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # User agent to appear more human-like
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            # Disable images and CSS for faster loading (optional)
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.managed_default_content_settings.stylesheets": 2,
                "profile.managed_default_content_settings.javascript": 1  # Keep JS enabled
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            # Auto-install ChromeDriver
            service = Service(ChromeDriverManager().install())
            
            # Create driver
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Execute script to remove automation indicators
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Setup wait
            self.wait = WebDriverWait(self.driver, self.wait_timeout)
            
            logger.info("✅ Chrome WebDriver setup complete")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup WebDriver: {e}")
            return False
    
    def _close_driver(self):
        """Safely close the WebDriver"""
        
        if self.driver:
            try:
                self.driver.quit()
                self.driver = None
                self.wait = None
                logger.info("✅ WebDriver closed")
            except Exception as e:
                logger.warning(f"⚠️ Error closing driver: {e}")
    
    def scrape_sportsbook_props(self, sportsbook: str) -> List[Dict[str, Any]]:
        """Scrape real props from a sportsbook using Selenium"""
        
        config = self.sportsbooks.get(sportsbook)
        if not config:
            logger.error(f"❌ Unknown sportsbook: {sportsbook}")
            return []
        
        logger.info(f"🌐 Scraping REAL props from {sportsbook}...")
        
        try:
            # Try player props URL first, then main URL
            urls_to_try = [
                config.get("player_props_url", config["url"]),
                config["url"]
            ]
            
            props = []
            
            for url in urls_to_try:
                try:
                    logger.info(f"🔍 Loading {sportsbook} page: {url}")
                    
                    # Navigate to page
                    self.driver.get(url)
                    
                    # Wait for page to load
                    time.sleep(3)
                    
                    # Try to wait for key elements
                    element_found = False
                    for wait_element in config["wait_elements"]:
                        try:
                            self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, wait_element)))
                            logger.info(f"✅ Found element: {wait_element}")
                            element_found = True
                            break
                        except TimeoutException:
                            logger.debug(f"⏰ Timeout waiting for: {wait_element}")
                            continue
                    
                    if not element_found:
                        logger.warning(f"⚠️ No expected elements found on {url}")
                        continue
                    
                    # Extract props using multiple strategies
                    props = self._extract_props_from_page(sportsbook, config)
                    
                    if props:
                        logger.info(f"✅ Found {len(props)} props from {sportsbook}")
                        break
                    else:
                        logger.warning(f"⚠️ No props extracted from {url}")
                        
                except Exception as e:
                    logger.warning(f"⚠️ Error with URL {url}: {e}")
                    continue
            
            return props
            
        except Exception as e:
            logger.error(f"❌ Error scraping {sportsbook}: {e}")
            return []

    def _extract_props_from_page(self, sportsbook: str, config: Dict) -> List[Dict[str, Any]]:
        """Extract props from the current page using multiple strategies"""

        props = []

        try:
            # Strategy 1: Look for specific prop containers
            selectors = config["selectors"]

            # Try different container selectors
            containers = []
            for container_selector in [selectors.get("game_containers"), selectors.get("prop_sections")]:
                if container_selector:
                    try:
                        found_containers = self.driver.find_elements(By.CSS_SELECTOR, container_selector)
                        if found_containers:
                            containers.extend(found_containers)
                            logger.info(f"✅ Found {len(found_containers)} containers with: {container_selector}")
                    except Exception as e:
                        logger.debug(f"Container selector failed: {e}")

            # Extract props from containers
            for container in containers[:10]:  # Limit to first 10 containers
                try:
                    prop = self._extract_prop_from_container(container, sportsbook, selectors)
                    if prop:
                        props.append(prop)
                except Exception as e:
                    logger.debug(f"Error extracting from container: {e}")

            # Strategy 2: Text-based extraction if no structured props found
            if not props:
                props = self._extract_props_from_text(sportsbook)

            # Strategy 3: Generic element search
            if not props:
                props = self._extract_props_generic(sportsbook)

            return props

        except Exception as e:
            logger.error(f"❌ Error extracting props from {sportsbook}: {e}")
            return []

    def _extract_prop_from_container(self, container, sportsbook: str, selectors: Dict) -> Optional[Dict[str, Any]]:
        """Extract a single prop from a container element"""

        try:
            prop_data = {
                'sportsbook': sportsbook,
                'timestamp': datetime.now().isoformat(),
                'extraction_method': 'container'
            }

            # Get container text for analysis
            container_text = container.text if container.text else ""

            # Look for player names
            for player in self.wnba_players:
                if player.lower() in container_text.lower():
                    prop_data['player'] = player
                    break

            # Try to find specific elements within container
            for element_type, selector in selectors.items():
                if element_type in ['player_name', 'market_name', 'line_value', 'odds_value']:
                    try:
                        element = container.find_element(By.CSS_SELECTOR, selector)
                        if element and element.text.strip():
                            prop_data[element_type] = element.text.strip()
                    except:
                        pass

            # Extract line value from text
            line_matches = re.findall(r'\b\d+\.5\b|\b\d+\b', container_text)
            if line_matches:
                for match in line_matches:
                    line_val = float(match)
                    if 0.5 <= line_val <= 50:  # Reasonable range
                        prop_data['line'] = line_val
                        break

            # Extract odds from text
            odds_matches = re.findall(r'[+-]\d{3,4}', container_text)
            if odds_matches:
                prop_data['odds'] = odds_matches[0]

            # Determine market type
            market_keywords = {
                'points': ['points', 'pts', 'scoring'],
                'rebounds': ['rebounds', 'reb', 'boards'],
                'assists': ['assists', 'ast', 'dimes'],
                'threes': ['3-pointers', 'three pointers', 'threes', '3pt'],
                'steals': ['steals', 'stl'],
                'blocks': ['blocks', 'blk']
            }

            for market, keywords in market_keywords.items():
                for keyword in keywords:
                    if keyword in container_text.lower():
                        prop_data['market'] = market
                        break
                if 'market' in prop_data:
                    break

            # Only return if we have essential data
            if 'player' in prop_data and ('market' in prop_data or 'line' in prop_data):
                return prop_data

            return None

        except Exception as e:
            logger.debug(f"Error extracting prop from container: {e}")
            return None

    def _extract_props_from_text(self, sportsbook: str) -> List[Dict[str, Any]]:
        """Extract props using text analysis of the entire page"""

        props = []

        try:
            # Get page source
            page_text = self.driver.page_source

            # Look for WNBA player mentions
            for player in self.wnba_players[:10]:  # Limit to top 10 players
                if player.lower() in page_text.lower():
                    # Found a player, try to extract surrounding context
                    prop = {
                        'sportsbook': sportsbook,
                        'player': player,
                        'timestamp': datetime.now().isoformat(),
                        'extraction_method': 'text_analysis'
                    }

                    # Look for numbers near the player name (simplified)
                    player_pattern = re.escape(player.lower())
                    context_pattern = f'.{{0,200}}{player_pattern}.{{0,200}}'

                    match = re.search(context_pattern, page_text.lower())
                    if match:
                        context = match.group()

                        # Extract line values
                        line_matches = re.findall(r'\b\d+\.5\b', context)
                        if line_matches:
                            prop['line'] = float(line_matches[0])

                        # Extract odds
                        odds_matches = re.findall(r'[+-]\d{3,4}', context)
                        if odds_matches:
                            prop['odds'] = odds_matches[0]

                        # Determine market
                        if 'point' in context:
                            prop['market'] = 'points'
                        elif 'rebound' in context:
                            prop['market'] = 'rebounds'
                        elif 'assist' in context:
                            prop['market'] = 'assists'

                        if 'line' in prop or 'market' in prop:
                            props.append(prop)

                            # Limit to avoid duplicates
                            if len(props) >= 5:
                                break

            logger.info(f"📊 Text analysis found {len(props)} props")
            return props

        except Exception as e:
            logger.error(f"❌ Error in text analysis: {e}")
            return []

    def _extract_props_generic(self, sportsbook: str) -> List[Dict[str, Any]]:
        """Generic extraction method as fallback"""

        props = []

        try:
            # Look for any elements containing WNBA player names
            for player in self.wnba_players[:5]:  # Limit to top 5 players
                try:
                    # Search for elements containing player name
                    xpath = f"//*[contains(text(), '{player}')]"
                    elements = self.driver.find_elements(By.XPATH, xpath)

                    if elements:
                        prop = {
                            'sportsbook': sportsbook,
                            'player': player,
                            'timestamp': datetime.now().isoformat(),
                            'extraction_method': 'generic_search',
                            'elements_found': len(elements)
                        }
                        props.append(prop)

                except Exception as e:
                    logger.debug(f"Generic search failed for {player}: {e}")

            logger.info(f"🔍 Generic search found {len(props)} player mentions")
            return props

        except Exception as e:
            logger.error(f"❌ Error in generic extraction: {e}")
            return []

    def scrape_all_sportsbooks(self) -> Dict[str, List[Dict[str, Any]]]:
        """Scrape props from all sportsbooks"""

        logger.info("🏀 Starting FULL-POWER WNBA Props Scraping")
        logger.info("=" * 80)

        # Setup WebDriver
        if not self._setup_driver():
            logger.error("❌ Failed to setup WebDriver")
            return {}

        all_props = {}

        try:
            for sportsbook in self.sportsbooks:
                try:
                    logger.info(f"🎯 Scraping {sportsbook}...")
                    props = self.scrape_sportsbook_props(sportsbook)
                    all_props[sportsbook] = props

                    if props:
                        logger.info(f"✅ {sportsbook}: {len(props)} props found")
                    else:
                        logger.warning(f"❌ {sportsbook}: No props found")

                    # Respectful delay between sportsbooks
                    time.sleep(5)

                except Exception as e:
                    logger.error(f"❌ Error scraping {sportsbook}: {e}")
                    all_props[sportsbook] = []

        finally:
            # Always close the driver
            self._close_driver()

        return all_props

    def display_results(self, all_props: Dict[str, List[Dict[str, Any]]]):
        """Display comprehensive scraping results"""

        print("\n🏀 FULL-POWER WNBA PROPS SCRAPING RESULTS")
        print("=" * 80)
        print(f"📅 Scraped: {datetime.now().strftime('%A, %B %d, %Y at %I:%M %p')}")
        print("=" * 80)

        total_props = sum(len(props) for props in all_props.values())

        if total_props == 0:
            print("❌ NO REAL PROPS FOUND")
            print("\n🔍 Possible reasons:")
            print("   • No WNBA games scheduled today")
            print("   • Sportsbooks haven't posted props yet")
            print("   • Website structure changed")
            print("   • Anti-scraping measures active")
            print("   • ChromeDriver issues")
            print("\n💡 Try running with headless=False to see what's happening")
            return

        print(f"✅ TOTAL PROPS FOUND: {total_props}")
        print()

        for sportsbook, props in all_props.items():
            if not props:
                print(f"❌ {sportsbook}: No props found")
                continue

            print(f"🏪 {sportsbook.upper()} ({len(props)} props):")
            print("-" * 60)

            for i, prop in enumerate(props, 1):
                player = prop.get('player', 'Unknown Player')
                market = prop.get('market', 'Unknown Market')
                line = prop.get('line', 'N/A')
                odds = prop.get('odds', 'N/A')
                method = prop.get('extraction_method', 'unknown')

                print(f"   {i}. {player}")
                print(f"      Market: {market}")
                if line != 'N/A':
                    print(f"      Line: {line}")
                if odds != 'N/A':
                    print(f"      Odds: {odds}")
                print(f"      Method: {method}")
                print()

        print("=" * 80)
        print("🎉 Full-Power Props Scraping Complete!")


def main():
    """Main execution function"""

    print("🏀 FULL-POWER WNBA PROPS SCRAPER - SELENIUM VERSION")
    print("=" * 80)
    print("🎯 Features:")
    print("   ✅ Full Selenium WebDriver Power")
    print("   ✅ Auto ChromeDriver Management")
    print("   ✅ Real Sportsbook Scraping")
    print("   ✅ Live Player Props Extraction")
    print("   ✅ Anti-Detection Measures")
    print("   ✅ Professional Error Handling")
    print("=" * 80)
    print("⚠️  Note: This will take several minutes due to respectful delays")
    print("⚠️  Requires Chrome browser to be installed")
    print("=" * 80)

    # Initialize scraper (set headless=False to see browser)
    scraper = FullPowerWNBAPropsScaper(headless=True, wait_timeout=20)

    try:
        # Scrape all sportsbooks
        all_props = scraper.scrape_all_sportsbooks()

        # Display results
        scraper.display_results(all_props)

        # Save data
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"full_power_wnba_props_{timestamp}.json"

        with open(filename, 'w') as f:
            json.dump(all_props, f, indent=2, default=str)

        total_props = sum(len(props) for props in all_props.values())
        print(f"💾 Saved {total_props} real props to {filename}")

        return all_props

    except KeyboardInterrupt:
        print("\n⚠️ Scraping interrupted by user")
        scraper._close_driver()
    except Exception as e:
        print(f"\n❌ Scraping failed: {e}")
        scraper._close_driver()


if __name__ == "__main__":
    main()
