#!/usr/bin/env python3
"""
Fix Unicode Emojis in Python Files
==================================

This script removes Unicode emojis from Python files to fix Windows encoding issues.
"""

import os
import re
import sys
from pathlib import Path

def fix_unicode_in_file(file_path):
    """Fix Unicode emojis in a single file"""
    
    try:
        # Read file with UTF-8 encoding
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            content = f.read()
        
        # Remove common Unicode emojis
        emoji_patterns = [
            r'🎯', r'📊', r'🚀', r'✅', r'❌', r'⚠️', r'🔍', r'🎮', r'🌌', r'🌐',
            r'🏀', r'📈', r'📉', r'💡', r'🔧', r'🎨', r'📝', r'🔥', r'⭐', r'🎪',
            r'🤖', r'🧠', r'📚', r'🎲', r'🎯', r'🔮', r'⚡', r'🌟', r'🎊', r'🎉',
            r'📋', r'📌', r'📍', r'🗂️', r'📂', r'📁', r'🗃️', r'🗄️', r'📇', r'🗳️',
            r'🔒', r'🔓', r'🔐', r'🔑', r'🗝️', r'🔨', r'⚒️', r'🛠️', r'⚙️', r'🔩',
            r'⚖️', r'🔗', r'⛓️', r'📎', r'🖇️', r'📐', r'📏', r'📊', r'📈', r'📉',
            r'🎯', r'🎪', r'🎨', r'🎭', r'🎪', r'🎨', r'🎯', r'🎲', r'🎰', r'🎳'
        ]
        
        # Replace emojis with empty string
        for pattern in emoji_patterns:
            content = re.sub(pattern, '', content)
        
        # Remove any remaining Unicode characters that might cause issues
        content = re.sub(r'[\u2600-\u26FF\u2700-\u27BF\U0001F300-\U0001F5FF\U0001F600-\U0001F64F\U0001F680-\U0001F6FF\U0001F700-\U0001F77F\U0001F780-\U0001F7FF\U0001F800-\U0001F8FF\U0001F900-\U0001F9FF\U0001FA00-\U0001FA6F\U0001FA70-\U0001FAFF\U00002600-\U000026FF\U00002700-\U000027BF]', '', content)
        
        # Write file back with UTF-8 encoding
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Fixed: {file_path}")
        return True
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False

def main():
    """Main function to fix Unicode in all Python files"""
    
    print("FIXING UNICODE EMOJIS IN PYTHON FILES")
    print("=" * 50)
    
    # Files to fix
    files_to_fix = [
        'automated_training_pipeline.py',
        'real_wnba_data_collector.py',
        'automated_injury_monitor.py',
        'expert_odds_api_system.py',
        'create_expert_dataset.py',
        'train_federated_multiverse_system.py',
        'src/models/modern_player_points_model.py',
        'test_complete_federated_multiverse_integration.py',
        'test_enhanced_model_last_7_days.py',
        'test_expert_autopilot_implementation.py'
    ]
    
    fixed_count = 0
    total_count = 0
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            total_count += 1
            if fix_unicode_in_file(file_path):
                fixed_count += 1
        else:
            print(f"File not found: {file_path}")
    
    print(f"\nFixed {fixed_count}/{total_count} files")
    print("Unicode emoji fixing completed!")

if __name__ == "__main__":
    main()
