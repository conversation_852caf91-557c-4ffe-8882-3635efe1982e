#!/usr/bin/env python3
"""
PIPELINE CONFIGURATION FIXER
============================

Fixes ALL configuration issues and removes ALL fake data generation:
1. Fix elite penalty configuration (remove identical 3.0x)
2. Fix WNBA team count (13 → 12)
3. Remove WeatherImpactModel entirely
4. Remove ALL DFS fake data generation
5. Fix future test data issues
6. Add strict data validation
7. Audit entire pipeline for fake data
"""

import json
import os
import re
from pathlib import Path
from typing import Dict, List, Any
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PipelineConfigurationFixer:
    """Fix all pipeline configuration issues and remove fake data"""
    
    def __init__(self):
        """Initialize configuration fixer"""
        
        self.fixes_applied = []
        self.files_modified = []
        self.fake_data_removed = []
        
        # Correct WNBA configuration
        self.correct_config = {
            'wnba_teams_2025': [
                'ATL', 'CHI', 'CON', 'DAL', 'GSV', 'IND', 'LAS',
                'LV', 'MIN', 'NYL', 'PHO', 'SEA', 'WAS'
            ],
            'team_count': 13,  # CORRECTED: 13 teams in 2025 with GSV expansion
            'elite_penalties': {
                'PossessionBasedModel': 2.5,
                'LineupChemistryModel': 3.0,
                'CumulativeFatigueModel': 2.0,
                'HighLeverageModel': 3.5,
                'TeamDynamicsModel': 2.8,
                'ContextualPerformanceModel': 3.2,
                'InjuryImpactModel': 2.2,
                'CoachingStyleModel': 2.7,
                'ArenaEffectModel': 1.8,
                # Remove WeatherImpactModel entirely
            },
            'models_to_remove': ['WeatherImpactModel'],
            'test_years': [2024],  # Only 2024, not 2025
            'validation_years': [2023]
        }
        
        logger.info("Pipeline Configuration Fixer initialized")
    
    def fix_automated_training_pipeline(self):
        """Fix automated_training_pipeline.py"""
        
        logger.info("🔧 Fixing automated_training_pipeline.py...")
        
        file_path = "automated_training_pipeline.py"
        
        if not os.path.exists(file_path):
            logger.warning(f"File not found: {file_path}")
            return
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix 1: Remove hardcoded DFS fake data
# REMOVED: DFS component
        if re.search(fake_dfs_pattern, content):
            content = re.sub(
                fake_dfs_pattern,
# REMOVED: DFS component
                content
            )
# REMOVED: DFS component
        
        # Fix 2: Remove WeatherImpactModel from multiverse models
        weather_model_pattern = r"'WeatherImpactModel'[,\s]*"
        content = re.sub(weather_model_pattern, "", content)
        
        # Fix 3: CORRECTED - Keep 13 teams (GSV expansion in 2025)
        # content = re.sub(r"13 teams", "12 teams", content)  # REVERTED
        # content = re.sub(r"13 WNBA teams", "12 WNBA teams", content)  # REVERTED
        
        # Fix 4: Add proper elite penalty configuration
        elite_penalty_section = '''
        # FIXED: Proper elite penalty configuration (no more identical 3.0x)
        self.elite_penalties = {
            'PossessionBasedModel': 2.5,
            'LineupChemistryModel': 3.0,
            'CumulativeFatigueModel': 2.0,
            'HighLeverageModel': 3.5,
            'TeamDynamicsModel': 2.8,
            'ContextualPerformanceModel': 3.2,
            'InjuryImpactModel': 2.2,
            'CoachingStyleModel': 2.7,
            'ArenaEffectModel': 1.8
            # WeatherImpactModel REMOVED - WNBA is indoor sport
        }
        '''
        
        # Insert elite penalty configuration
        if "self.elite_penalties" not in content:
            # Find a good place to insert (after __init__ method start)
            init_pattern = r"(def __init__\(self[^:]*:\s*)"
            content = re.sub(init_pattern, r"\1" + elite_penalty_section, content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            self.files_modified.append(file_path)
            self.fixes_applied.append("Fixed automated_training_pipeline.py")
    
    def fix_pipeline_config_yaml(self):
        """Fix config/pipeline_config.yaml"""
        
        logger.info("🔧 Fixing config/pipeline_config.yaml...")
        
        file_path = "config/pipeline_config.yaml"
        
        if not os.path.exists(file_path):
            logger.warning(f"File not found: {file_path}")
            return
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix 1: Remove DFS salary sources (fake data)
# REMOVED: DFS component
                        'salary_sources: []\n    # REMOVED: Fake DFS salary sources', content)
        
        # Fix 2: Fix team count
        content = re.sub(r'teams: 13', 'teams: 12', content)
        
        # Fix 3: Remove weather-related configurations
        content = re.sub(r'weather_impact: true', 'weather_impact: false  # WNBA is indoor sport', content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            self.files_modified.append(file_path)
            self.fixes_applied.append("Fixed pipeline_config.yaml")
    
    def remove_weather_impact_model(self):
        """Remove WeatherImpactModel from modern_player_points_model.py"""
        
        logger.info("🔧 Removing WeatherImpactModel...")
        
        file_path = "src/models/modern_player_points_model.py"
        
        if not os.path.exists(file_path):
            logger.warning(f"File not found: {file_path}")
            return
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Remove WeatherImpactModel class definition
        weather_class_pattern = r'class WeatherImpactModel\([^}]*?\n\n(?=class|\Z)'
        content = re.sub(weather_class_pattern, '', content, flags=re.DOTALL)
        
        # Remove weather model references
        content = re.sub(r"'WeatherImpactModel'[,\s]*", "", content)
        content = re.sub(r'"WeatherImpactModel"[,\s]*', "", content)
        
        # Add comment explaining removal
        weather_removal_comment = '''
# WeatherImpactModel REMOVED
# Reason: WNBA games are played indoors - weather has minimal impact
# This was generating spurious correlations and inflated performance metrics
'''
        
        if "WeatherImpactModel REMOVED" not in content:
            content = weather_removal_comment + content
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            self.files_modified.append(file_path)
            self.fixes_applied.append("Removed WeatherImpactModel")
    
    def fix_federated_config(self):
        """Fix federated learning configuration"""
        
        logger.info("🔧 Fixing federated learning configuration...")
        
        # Fix federated_config.json
        config_files = [
            "config/federated_config.json",
            "src/federated_learning/federated_config.py"
        ]
        
        for file_path in config_files:
            if not os.path.exists(file_path):
                continue
                
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # CORRECTED: Keep 13 teams (GSV expansion in 2025)
            # content = re.sub(r'"num_teams":\s*13', '"num_teams": 12', content)  # REVERTED
            # content = re.sub(r'num_teams = 13', 'num_teams = 12', content)  # REVERTED

            # CORRECTED: Keep GSV in team list
            # old_team_pattern = r'\[([^]]*"WAS"[^]]*)\]'
            # new_team_list = '["ATL", "CHI", "CON", "DAL", "IND", "LAS", "LV", "MIN", "NYL", "PHO", "SEA", "WAS"]'  # REVERTED
            # content = re.sub(old_team_pattern, new_team_list, content)  # REVERTED
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.files_modified.append(file_path)
    
    # REMOVED: DFS componentself):
        """Remove ALL DFS fake data generation"""
        
        logger.info("🔧 Removing ALL DFS fake data generation...")
        
        # Files that might contain DFS fake data
        dfs_files = [
            "real_dfs_data_integration.py",
            "automated_training_pipeline.py",
            "src/models/modern_player_points_model.py"
        ]
        
        for file_path in dfs_files:
            if not os.path.exists(file_path):
                continue
                
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Remove hardcoded DFS salaries
            content = re.sub(r'def _get_sample_dk_salaries.*?return \{[^}]*\}', 
                           'def _get_sample_dk_salaries(self):\n        """REMOVED: No fake salary data"""\n        return {}', 
                           content, flags=re.DOTALL)
            
            content = re.sub(r'def _get_sample_fd_salaries.*?return \{[^}]*\}', 
                           'def _get_sample_fd_salaries(self):\n        """REMOVED: No fake salary data"""\n        return {}', 
                           content, flags=re.DOTALL)
            
# REMOVED: DFS component
# REMOVED: DFS component
            content = re.sub(r"'season_long_accuracy': 0\.\d+", "'season_long_accuracy': None  # REMOVED: No fake data", content)
            content = re.sub(r"'best_ball_accuracy': 0\.\d+", "'best_ball_accuracy': None  # REMOVED: No fake data", content)
            
# REMOVED: DFS component
            fake_contest_pattern = r'def simulate_fantasy_contests(*args, **kwargs):
    """REMOVED: DFS functionality"""
    return {'error': 'DFS removed from system'}