#!/usr/bin/env python3
"""
MODEL INTEGRITY AUDIT
=====================

Investigates the critical red flags identified:
1. Identical elite penalties (3.0x) across all multiverse models
2. Identical MAE for different architectures
3. WeatherImpactModel high fantasy accuracy (WNBA is indoors!)
4. WNBA team count mismatch (13 vs 12)
5. Future test data (2025 games not yet played)
6. Performance paradoxes and metric inconsistencies
"""

import json
import pandas as pd
import numpy as np
from typing import Dict, List, Any
from pathlib import Path
import logging
from datetime import datetime, date

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelIntegrityAuditor:
    """Audit model training results for data integrity issues"""
    
    def __init__(self):
        """Initialize integrity auditor"""
        
        # Load pipeline results
        self.results_file = "pipeline_results/pipeline_results_run_20250713_034217.json"
        self.results = self._load_results()
        
        # Current date for validation
        self.current_date = date.today()
        
        # Known WNBA facts for validation
        self.wnba_facts = {
            'teams_2025': 12,  # Correct count
            'teams_list': ['ATL', 'CHI', 'CON', 'DAL', 'IND', 'LAS', 'LV', 'MIN', 'NYL', 'PHO', 'SEA', 'WAS'],
            'indoor_sport': True,
            'weather_relevance': 'minimal'
        }
        
        logger.info("Model Integrity Auditor initialized")
    
    def _load_results(self) -> Dict[str, Any]:
        """Load pipeline results"""
        
        try:
            with open(self.results_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load results: {e}")
            return {}
    
    def audit_elite_penalty_consistency(self) -> Dict[str, Any]:
        """Audit identical elite penalty across all multiverse models"""
        
        logger.info("🔥 Auditing elite penalty consistency...")
        
        penalties = {}
        models_with_penalty = []
        
        if 'models_trained' in self.results:
            for model_name, model_data in self.results['models_trained'].items():
                if model_data.get('model_type') == 'fantasy_enhanced':
                    penalty = model_data.get('fantasy_features', {}).get('elite_player_penalty', None)
                    if penalty is not None:
                        penalties[model_name] = penalty
                        models_with_penalty.append(model_name)
        
        # Check for identical penalties
        unique_penalties = set(penalties.values())
        identical_penalty = len(unique_penalties) == 1
        
        audit_result = {
            'models_checked': len(penalties),
            'unique_penalties': list(unique_penalties),
            'identical_penalty': identical_penalty,
            'penalty_value': list(unique_penalties)[0] if identical_penalty else None,
            'statistical_probability': self._calculate_identical_penalty_probability(len(penalties)),
            'verdict': 'SUSPICIOUS' if identical_penalty else 'NORMAL',
            'recommendations': []
        }
        
        if identical_penalty:
            audit_result['recommendations'] = [
                "Verify elite penalty was actually applied during training",
                "Check if penalty is hardcoded in configuration",
                "Confirm penalty wasn't just a header label",
                "Re-run training with varied penalty values (2.0x, 2.5x, 3.5x)",
                "Audit training logs for penalty application evidence"
            ]
        
        return audit_result
    
    def audit_identical_mae_architectures(self) -> Dict[str, Any]:
        """Audit identical MAE for different architectures"""
        
        logger.info("🔥 Auditing identical MAE for different architectures...")
        
        core_models = {}
        
        if 'models_trained' in self.results:
            for model_name, model_data in self.results['models_trained'].items():
                if model_data.get('model_type') == 'core':
                    mae = model_data['performance']['mae']
                    r2 = model_data['performance']['r2']
                    epochs = model_data['epochs_trained']
                    
                    core_models[model_name] = {
                        'mae': mae,
                        'r2': r2,
                        'epochs': epochs
                    }
        
        # Find identical MAE values
        mae_groups = {}
        for model, metrics in core_models.items():
            mae = metrics['mae']
            if mae not in mae_groups:
                mae_groups[mae] = []
            mae_groups[mae].append(model)
        
        # Identify suspicious identical MAE
        identical_mae_groups = {mae: models for mae, models in mae_groups.items() if len(models) > 1}
        
        audit_result = {
            'total_core_models': len(core_models),
            'mae_groups': mae_groups,
            'identical_mae_groups': identical_mae_groups,
            'suspicious_pairs': [],
            'verdict': 'SUSPICIOUS' if identical_mae_groups else 'NORMAL',
            'recommendations': []
        }
        
        # Analyze suspicious pairs
        for mae, models in identical_mae_groups.items():
            if len(models) == 2:
                model1, model2 = models
                r2_diff = abs(core_models[model1]['r2'] - core_models[model2]['r2'])
                
                audit_result['suspicious_pairs'].append({
                    'models': models,
                    'identical_mae': mae,
                    'r2_difference': r2_diff,
                    'epochs_diff': abs(core_models[model1]['epochs'] - core_models[model2]['epochs']),
                    'suspicion_level': 'HIGH' if r2_diff > 0.01 else 'MEDIUM'
                })
        
        if identical_mae_groups:
            audit_result['recommendations'] = [
                "Check for data leakage between models",
                "Verify different architectures were actually used",
                "Analyze error distributions to find where models disagree",
                "Re-run with different random seeds",
                "Audit validation set diversity",
                "Check for overfitting to validation set"
            ]
        
        return audit_result
    
    def audit_weather_impact_logic(self) -> Dict[str, Any]:
        """Audit WeatherImpactModel logic for indoor sport"""
        
        logger.info("🔥 Auditing WeatherImpactModel for indoor sport...")
        
        weather_model = None
        if 'models_trained' in self.results:
            weather_model = self.results['models_trained'].get('WeatherImpactModel')
        
        audit_result = {
            'model_exists': weather_model is not None,
            'wnba_indoor_sport': self.wnba_facts['indoor_sport'],
            'weather_relevance': self.wnba_facts['weather_relevance'],
            'verdict': 'ILLOGICAL',
            'issues_found': [],
            'recommendations': []
        }
        
        if weather_model:
            mae = weather_model['performance']['mae']
            fantasy_acc = weather_model['performance']['fantasy_accuracy']
            
            audit_result.update({
                'model_mae': mae,
                'model_fantasy_accuracy': fantasy_acc,
                'performance_rank': self._rank_weather_model_performance()
            })
            
            audit_result['issues_found'] = [
                "WNBA games are played indoors - weather should have minimal impact",
                f"High fantasy accuracy ({fantasy_acc:.1%}) suggests over-indexing on irrelevant features",
                "Weather features likely capturing spurious correlations",
                "Model may be learning noise rather than signal"
            ]
            
            audit_result['recommendations'] = [
                "Remove weather features and re-train model",
                "Run ablation study: model with vs without weather data",
                "Replace with more relevant features (arena altitude, travel fatigue)",
                "Investigate what 'weather' features actually represent",
                "Consider renaming to 'EnvironmentalFactorsModel' if using arena data"
            ]
        
        return audit_result
    
    def audit_team_count_mismatch(self) -> Dict[str, Any]:
        """Audit WNBA team count mismatch"""
        
        logger.info("🔥 Auditing WNBA team count mismatch...")
        
        # Check federated learning configuration
        federated_config = self.results.get('stages', {}).get('federated_learning', {})
        claimed_teams = federated_config.get('team_participation', {})
        
        audit_result = {
            'claimed_team_count': len(claimed_teams) if claimed_teams else 13,  # From error message
            'actual_wnba_teams_2025': self.wnba_facts['teams_2025'],
            'mismatch': True,
            'verdict': 'DATA_ERROR',
            'issues_found': [],
            'recommendations': []
        }
        
        audit_result['issues_found'] = [
            f"Claims 13 WNBA teams, but WNBA has {self.wnba_facts['teams_2025']} teams in 2025",
            "Possible outdated data or synthetic league expansion",
            "Federated learning setup may be using incorrect team list",
            "Could indicate data from different time period"
        ]
        
        audit_result['recommendations'] = [
            "Update team list to current 12 WNBA teams",
            "Verify data source and time period",
            "Check if 13th 'team' is actually aggregated data",
            "Audit federated learning client configuration",
            "Confirm team abbreviations match official WNBA codes"
        ]
        
        return audit_result
    
    def audit_future_test_data(self) -> Dict[str, Any]:
        """Audit use of future test data"""
        
        logger.info("🔥 Auditing future test data usage...")
        
        # Check data splits
        data_splits = self.results.get('stages', {}).get('core_training', {}).get('data_splits_applied', {})
        test_years = data_splits.get('test_years', [])
        
        current_year = self.current_date.year
        future_years = [year for year in test_years if year > current_year]
        
        audit_result = {
            'test_years': test_years,
            'current_year': current_year,
            'future_years': future_years,
            'using_future_data': len(future_years) > 0,
            'verdict': 'INVALID' if future_years else 'VALID',
            'issues_found': [],
            'recommendations': []
        }
        
        if future_years:
            audit_result['issues_found'] = [
                f"Test data includes future years: {future_years}",
                f"2025 games haven't been played yet (current date: {self.current_date})",
                "Results for 2025 are projections, not actual performance",
                "Model validation is compromised by future data"
            ]
            
            audit_result['recommendations'] = [
                "Use only 2024 data for testing (games already played)",
                "Clearly label 2025 results as 'projections'",
                "Re-run validation with proper temporal splits",
                "Implement rolling validation for recent data",
                "Add data cutoff date validation"
            ]
        
        return audit_result
    
    def audit_performance_paradoxes(self) -> Dict[str, Any]:
        """Audit performance paradoxes and metric inconsistencies"""
        
        logger.info("🔥 Auditing performance paradoxes...")
        
        paradoxes = []
        
        # Check PlayerPointsModel R² vs MAE inconsistency
        if 'models_trained' in self.results:
            player_model = self.results['models_trained'].get('PlayerPointsModel')
            if player_model:
                mae = player_model['performance']['mae']
                r2 = player_model['performance']['r2']
                
                # Compare to other models
                other_models = []
                for name, data in self.results['models_trained'].items():
                    if name != 'PlayerPointsModel' and data.get('model_type') == 'core':
                        other_models.append({
                            'name': name,
                            'mae': data['performance']['mae'],
                            'r2': data['performance']['r2']
                        })
                
                # Find models with worse R² but better MAE
                inconsistent_models = []
                for model in other_models:
                    if model['r2'] < r2 and model['mae'] < mae:
                        inconsistent_models.append(model)
                
                if inconsistent_models:
                    paradoxes.append({
                        'type': 'R2_MAE_INCONSISTENCY',
                        'description': f"PlayerPointsModel has highest R² ({r2:.3f}) but worse MAE ({mae:.3f})",
                        'inconsistent_models': inconsistent_models,
                        'suspicion': 'Skewed error distribution or outlier sensitivity'
                    })
        
        # Check MetaModel metrics
        meta_model = self.results.get('stages', {}).get('multiverse_training', {}).get('specialized_models', {}).get('MetaModel')
        if meta_model:
            mae = meta_model['performance']['mae']
            r2 = meta_model['performance']['r2']
            
            if r2 > 0.9 and mae > 0.75:
                paradoxes.append({
                    'type': 'METAMODEL_PARADOX',
                    'description': f"MetaModel has high R² ({r2:.3f}) but poor MAE ({mae:.3f})",
                    'suspicion': 'Incorrect problem framing or data leakage'
                })
        
        audit_result = {
            'paradoxes_found': len(paradoxes),
            'paradoxes': paradoxes,
            'verdict': 'SUSPICIOUS' if paradoxes else 'NORMAL',
            'recommendations': []
        }
        
        if paradoxes:
            audit_result['recommendations'] = [
                "Analyze error distributions for outlier sensitivity",
                "Check for data leakage in validation",
                "Verify MetaModel is solving correct problem (classification vs regression)",
                "Re-examine metric definitions and calculations",
                "Implement stratified validation by player tiers"
            ]
        
        return audit_result
    
    def _calculate_identical_penalty_probability(self, num_models: int) -> float:
        """Calculate probability of identical penalties by chance"""
        
        # Assuming penalties could range from 1.5 to 4.0 in 0.1 increments (26 options)
        # Probability all models have same penalty by chance
        return (1.0 / 26) ** (num_models - 1)
    
    def _rank_weather_model_performance(self) -> str:
        """Rank WeatherImpactModel performance vs other multiverse models"""
        
        multiverse_models = []
        if 'models_trained' in self.results:
            for name, data in self.results['models_trained'].items():
                if data.get('model_type') == 'fantasy_enhanced':
                    multiverse_models.append({
                        'name': name,
                        'fantasy_accuracy': data['performance']['fantasy_accuracy']
                    })
        
        # Sort by fantasy accuracy
        multiverse_models.sort(key=lambda x: x['fantasy_accuracy'], reverse=True)
        
        # Find WeatherImpactModel rank
        for i, model in enumerate(multiverse_models):
            if model['name'] == 'WeatherImpactModel':
                total_models = len(multiverse_models)
                if i < total_models * 0.3:
                    return 'TOP_TIER'
                elif i < total_models * 0.7:
                    return 'MIDDLE_TIER'
                else:
                    return 'BOTTOM_TIER'
        
        return 'NOT_FOUND'
    
    def run_complete_audit(self) -> Dict[str, Any]:
        """Run complete integrity audit"""
        
        logger.info("🕵️ Running complete model integrity audit...")
        
        audit_results = {
            'audit_timestamp': datetime.now().isoformat(),
            'auditor_version': '1.0',
            'elite_penalty_audit': self.audit_elite_penalty_consistency(),
            'identical_mae_audit': self.audit_identical_mae_architectures(),
            'weather_logic_audit': self.audit_weather_impact_logic(),
            'team_count_audit': self.audit_team_count_mismatch(),
            'future_data_audit': self.audit_future_test_data(),
            'performance_paradox_audit': self.audit_performance_paradoxes()
        }
        
        # Calculate overall integrity score
        issues_found = 0
        total_audits = 6
        
        for audit_name, audit_result in audit_results.items():
            if isinstance(audit_result, dict) and 'verdict' in audit_result:
                if audit_result['verdict'] in ['SUSPICIOUS', 'ILLOGICAL', 'DATA_ERROR', 'INVALID']:
                    issues_found += 1
        
        integrity_score = (total_audits - issues_found) / total_audits
        
        audit_results['overall_assessment'] = {
            'integrity_score': integrity_score,
            'issues_found': issues_found,
            'total_audits': total_audits,
            'overall_verdict': self._get_overall_verdict(integrity_score),
            'critical_issues': self._extract_critical_issues(audit_results),
            'immediate_actions': self._generate_immediate_actions(audit_results)
        }
        
        return audit_results
    
    def _get_overall_verdict(self, score: float) -> str:
        """Get overall verdict based on integrity score"""
        
        if score >= 0.8:
            return 'MOSTLY_RELIABLE'
        elif score >= 0.6:
            return 'NEEDS_VERIFICATION'
        elif score >= 0.4:
            return 'MAJOR_CONCERNS'
        else:
            return 'UNRELIABLE'
    
    def _extract_critical_issues(self, audit_results: Dict[str, Any]) -> List[str]:
        """Extract critical issues from audit results"""
        
        critical_issues = []
        
        for audit_name, audit_result in audit_results.items():
            if isinstance(audit_result, dict) and 'verdict' in audit_result:
                if audit_result['verdict'] in ['SUSPICIOUS', 'ILLOGICAL', 'DATA_ERROR', 'INVALID']:
                    critical_issues.append(f"{audit_name}: {audit_result['verdict']}")
        
        return critical_issues
    
    def _generate_immediate_actions(self, audit_results: Dict[str, Any]) -> List[str]:
        """Generate immediate action items"""
        
        actions = [
            "STOP using current model results for production decisions",
            "Re-run training with proper data validation",
            "Fix team count to 12 WNBA teams",
            "Remove or fix WeatherImpactModel",
            "Use only 2024 data for testing",
            "Verify elite penalty application in training logs"
        ]
        
        return actions


def main():
    """Run model integrity audit"""
    
    print("MODEL INTEGRITY AUDIT")
    print("=" * 50)
    
    auditor = ModelIntegrityAuditor()
    results = auditor.run_complete_audit()
    
    # Save audit results
    with open('model_integrity_audit.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # Print critical findings
    overall = results['overall_assessment']
    print(f"\n🕵️ OVERALL VERDICT: {overall['overall_verdict']}")
    print(f"📊 Integrity Score: {overall['integrity_score']:.1%}")
    print(f"🚨 Issues Found: {overall['issues_found']}/{overall['total_audits']}")
    
    print(f"\n🔥 CRITICAL ISSUES:")
    for issue in overall['critical_issues']:
        print(f"   - {issue}")
    
    print(f"\n⚡ IMMEDIATE ACTIONS:")
    for action in overall['immediate_actions']:
        print(f"   - {action}")
    
    print(f"\n📋 Full audit saved to model_integrity_audit.json")


if __name__ == "__main__":
    main()
