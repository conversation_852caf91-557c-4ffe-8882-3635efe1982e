#!/usr/bin/env python3
"""
🏀 WNBA EXPERT DATASET CONSOLIDATION SYSTEM
==========================================

Consolidates ALL scattered WNBA datasets into ONE EXPERT DATASET
that all models can use for training.

Current Situation:
- 5 different datasets with 490,068 total records
- Multiple schemas and feature sets
- Confusion about which dataset to use

Solution:
- ONE expert dataset with ALL records
- Standardized schema for all models
- Comprehensive feature engineering
- Data quality validation
- Duplicate removal and consistency checks

Author: WNBA Analytics Team
Date: 2025-07-12
"""

import pandas as pd
import numpy as np
from pathlib import Path
import logging
from datetime import datetime
import json
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WNBAExpertDatasetCreator:
    """
    Creates the definitive WNBA expert dataset by consolidating all existing datasets
    """

    def __init__(self):
        self.datasets_info = {
            'complete_2015_2025': {
                'path': 'data/master/wnba_complete_dataset_2015_2025.csv',
                'records': 49581,
                'features': 642,
                'has_target': True,
                'priority': 1,  # Highest priority - most comprehensive
                'description': 'Complete historical dataset with full feature engineering'
            },
            'comprehensive_training': {
                'path': 'consolidated_wnba/04_training_data/player_props/comprehensive_wnba_2015_2025_training_data.csv',
                'records': 262309,
                'features': 27,
                'has_target': True,
                'priority': 2,  # High priority - most records
                'description': 'Largest dataset with core features'
            },
            'definitive_master': {
                'path': 'data/master/wnba_definitive_master_dataset_FIXED.csv',
                'records': 29237,
                'features': 642,
                'has_target': True,
                'priority': 3,  # Medium priority - cleaned but smaller
                'description': 'Cleaned and validated dataset'
            },
            'training_data': {
                'path': 'consolidated_wnba/04_training_data/player_props/wnba_training_data.csv',
                'records': 89046,
                'features': 269,
                'has_target': False,
                'priority': 4,  # Lower priority - no target
                'description': 'Additional training features'
            },
            'clean_training': {
                'path': 'consolidated_wnba/04_training_data/player_props/clean_wnba_training_data.csv',
                'records': 59895,
                'features': 9,
                'has_target': False,
                'priority': 5,  # Lowest priority - minimal features
                'description': 'Basic cleaned data'
            }
        }

        self.expert_schema = self._define_expert_schema()
        self.consolidation_stats = {}

    def _define_expert_schema(self) -> Dict[str, str]:
        """Define the schema for the expert dataset"""
        return {
            # Core identifiers
            'player_name': 'string',
            'player_id': 'string',
            'team_abbrev': 'string',
            'team_id': 'string',
            'game_id': 'string',
            'game_date': 'datetime',
            'year': 'int',
            'season': 'string',

            # Target variable
            'target': 'float',  # Points scored (primary target)

            # Core game stats
            'minutes': 'float',
            'rebounds': 'float',
            'assists': 'float',
            'steals': 'float',
            'blocks': 'float',
            'turnovers': 'float',
            'field_goals_made': 'float',
            'field_goals_attempted': 'float',
            'three_pointers_made': 'float',
            'three_pointers_attempted': 'float',
            'free_throws_made': 'float',
            'free_throws_attempted': 'float',

            # Advanced metrics
            'usage_rate': 'float',
            'efficiency': 'float',
            'plus_minus': 'float',
            'offensive_rating': 'float',
            'defensive_rating': 'float',
            'true_shooting_percentage': 'float',

            # Game context
            'is_home': 'bool',
            'rest_days': 'int',
            'back_to_back': 'bool',
            'altitude': 'float',
            'season_phase': 'string',
            'game_type': 'string',

            # Team metrics
            'team_pace': 'float',
            'team_offensive_rating': 'float',
            'team_defensive_rating': 'float',

            # Rolling averages (3-game)
            'points_avg_3': 'float',
            'rebounds_avg_3': 'float',
            'assists_avg_3': 'float',
            'minutes_avg_3': 'float',

            # Rolling averages (5-game)
            'points_avg_5': 'float',
            'rebounds_avg_5': 'float',
            'assists_avg_5': 'float',
            'minutes_avg_5': 'float',

            # Rolling averages (10-game)
            'points_avg_10': 'float',
            'rebounds_avg_10': 'float',
            'assists_avg_10': 'float',
            'minutes_avg_10': 'float',

            # Player role and position
            'player_role': 'string',  # Elite, Rotation, Bench
            'position': 'string',
            'role_score': 'float',

            # Data quality and source tracking
            'data_source': 'string',
            'data_quality_score': 'float',
            'is_validated': 'bool',
            'collection_date': 'datetime'
        }

    def load_dataset(self, dataset_key: str) -> pd.DataFrame:
        """Load a specific dataset with error handling"""
        dataset_info = self.datasets_info[dataset_key]
        file_path = dataset_info['path']

        logger.info(f"📊 Loading {dataset_key}: {file_path}")

        try:
            if Path(file_path).exists():
                df = pd.read_csv(file_path, low_memory=False)
                logger.info(f"   ✅ Loaded {len(df):,} records with {len(df.columns)} features")
                return df
            else:
                logger.warning(f"   ❌ File not found: {file_path}")
                return pd.DataFrame()
        except Exception as e:
            logger.error(f"   ❌ Error loading {dataset_key}: {e}")
            return pd.DataFrame()

    def handle_all_duplicates(self, df: pd.DataFrame, dataset_key: str) -> pd.DataFrame:
        """Handle ALL types of duplicates comprehensively"""
        logger.info(f"🔍 Handling ALL duplicates for {dataset_key}")

        initial_count = len(df)
        initial_cols = len(df.columns)

        # 1. COLUMN DUPLICATES - Remove duplicate column names
        logger.info("   🔧 Removing duplicate columns...")
        duplicate_cols = df.columns[df.columns.duplicated()].tolist()
        if duplicate_cols:
            logger.info(f"      Found {len(duplicate_cols)} duplicate columns: {duplicate_cols[:5]}...")
            df = df.loc[:, ~df.columns.duplicated(keep='first')]
            logger.info(f"      ✅ Removed duplicate columns: {initial_cols} → {len(df.columns)}")

        # 2. ROW DUPLICATES - Remove completely identical rows
        logger.info("   🔧 Removing identical rows...")
        df = df.drop_duplicates(keep='first')
        identical_removed = initial_count - len(df)
        if identical_removed > 0:
            logger.info(f"      ✅ Removed {identical_removed:,} identical rows")

        # 3. LOGICAL DUPLICATES - Same player, game, date
        logger.info("   🔧 Removing logical duplicates...")
        logical_key_cols = []

        # Build logical key from available columns
        if 'player_name' in df.columns:
            logical_key_cols.append('player_name')
        if 'game_date' in df.columns:
            logical_key_cols.append('game_date')
        if 'team_abbrev' in df.columns:
            logical_key_cols.append('team_abbrev')
        if 'game_id' in df.columns:
            logical_key_cols.append('game_id')

        if logical_key_cols:
            before_logical = len(df)
            df = df.drop_duplicates(subset=logical_key_cols, keep='first')
            logical_removed = before_logical - len(df)
            if logical_removed > 0:
                logger.info(f"      ✅ Removed {logical_removed:,} logical duplicates")

        # 4. VALUE DUPLICATES - Handle duplicate values in key columns
        logger.info("   🔧 Cleaning duplicate values...")

        # Clean player names
        if 'player_name' in df.columns:
            df['player_name'] = df['player_name'].astype(str).str.strip().str.title()

        # Clean team abbreviations
        if 'team_abbrev' in df.columns:
            df['team_abbrev'] = df['team_abbrev'].astype(str).str.strip().str.upper()

        # 5. NEAR DUPLICATES - Similar but not identical records
        logger.info("   🔧 Handling near duplicates...")
        if 'player_name' in df.columns and 'target' in df.columns:
            # Group by player and date, keep record with highest data quality
            if 'game_date' in df.columns:
                # Calculate a simple quality score based on non-null values
                df['temp_quality'] = df.count(axis=1)

                # Keep the highest quality record for each player-date combination
                before_near = len(df)
                df = df.sort_values('temp_quality', ascending=False).drop_duplicates(
                    subset=['player_name', 'game_date'], keep='first'
                )
                df = df.drop('temp_quality', axis=1)
                near_removed = before_near - len(df)
                if near_removed > 0:
                    logger.info(f"      ✅ Removed {near_removed:,} near duplicates")

        total_removed = initial_count - len(df)
        logger.info(f"   🎯 Total duplicates removed: {total_removed:,} ({total_removed/initial_count*100:.1f}%)")
        logger.info(f"   📊 Final records: {len(df):,}")

        return df

    def standardize_columns(self, df: pd.DataFrame, dataset_key: str) -> pd.DataFrame:
        """Standardize column names and types for consistency"""
        logger.info(f"🔧 Standardizing columns for {dataset_key}")

        # First handle all duplicates
        df = self.handle_all_duplicates(df, dataset_key)

        # Column mapping for different datasets
        column_mappings = {
            'PLAYER_NAME': 'player_name',
            'PLAYER_ID': 'player_id',
            'TEAM_ABBREVIATION': 'team_abbrev',
            'TEAM_ID': 'team_id',
            'GAME_ID': 'game_id',
            'GAME_DATE': 'game_date',
            'PTS': 'target',
            'points': 'target',
            'MIN': 'minutes',
            'REB': 'rebounds',
            'AST': 'assists',
            'STL': 'steals',
            'BLK': 'blocks',
            'TOV': 'turnovers',
            'FGM': 'field_goals_made',
            'FGA': 'field_goals_attempted',
            'FG3M': 'three_pointers_made',
            'FG3A': 'three_pointers_attempted',
            'FTM': 'free_throws_made',
            'FTA': 'free_throws_attempted',
            # Handle variations
            'TEAM_ABBREVIATION': 'team_abbrev',
            'team_abbreviation': 'team_abbrev',
            'TEAM_ABBREV': 'team_abbrev',
            'team_name': 'team_abbrev',  # Sometimes team name is used
            'PLAYER': 'player_name',
            'Player': 'player_name',
            'DATE': 'game_date',
            'date': 'game_date'
        }

        # Apply column mappings
        df = df.rename(columns=column_mappings)

        # Handle multiple target columns (keep the first valid one)
        target_candidates = ['target', 'points', 'PTS', 'pts']
        target_found = False
        for col in target_candidates:
            if col in df.columns and not target_found:
                if col != 'target':
                    df['target'] = df[col]
                target_found = True
            elif col in df.columns and col != 'target':
                df = df.drop(col, axis=1)  # Remove duplicate target columns

        # Ensure required columns exist
        required_cols = ['player_name', 'team_abbrev', 'game_date']
        for col in required_cols:
            if col not in df.columns:
                logger.warning(f"   ⚠️ Missing required column: {col}")

        # Add data source tracking
        df['data_source'] = dataset_key
        df['collection_date'] = datetime.now()

        return df

    def remove_duplicates(self, df: pd.DataFrame) -> pd.DataFrame:
        """Remove duplicate records based on key columns"""
        logger.info("🔍 Removing duplicates...")

        initial_count = len(df)

        # Define key columns for duplicate detection
        key_columns = ['player_name', 'team_abbrev', 'game_date']

        # Only use columns that exist
        existing_key_columns = [col for col in key_columns if col in df.columns]

        if existing_key_columns:
            # Remove duplicates, keeping the first occurrence (highest priority dataset)
            df = df.drop_duplicates(subset=existing_key_columns, keep='first')

            duplicates_removed = initial_count - len(df)
            logger.info(f"   ✅ Removed {duplicates_removed:,} duplicates")
            logger.info(f"   📊 Remaining records: {len(df):,}")
        else:
            logger.warning("   ⚠️ No key columns found for duplicate removal")

        return df

    def validate_data_quality(self, df: pd.DataFrame) -> pd.DataFrame:
        """Validate and score data quality"""
        logger.info("🔍 Validating data quality...")

        # Calculate data quality score for each record
        quality_scores = []

        for _, row in df.iterrows():
            score = 1.0

            # Penalize missing target
            if pd.isna(row.get('target')):
                score -= 0.3

            # Penalize missing key identifiers
            if pd.isna(row.get('player_name')):
                score -= 0.2
            if pd.isna(row.get('team_abbrev')):
                score -= 0.2
            if pd.isna(row.get('game_date')):
                score -= 0.2

            # Penalize unrealistic values
            if not pd.isna(row.get('target')) and (row.get('target', 0) < 0 or row.get('target', 0) > 60):
                score -= 0.1

            quality_scores.append(max(0.0, score))

        df['data_quality_score'] = quality_scores
        df['is_validated'] = df['data_quality_score'] >= 0.7

        high_quality_count = sum(df['is_validated'])
        logger.info(f"   ✅ High quality records: {high_quality_count:,} ({high_quality_count/len(df)*100:.1f}%)")

        return df

    def create_expert_dataset(self) -> pd.DataFrame:
        """Create the consolidated expert dataset"""
        logger.info("🚀 CREATING EXPERT WNBA DATASET")
        logger.info("=" * 60)

        consolidated_dfs = []
        total_records = 0

        # Load datasets in priority order
        for dataset_key in sorted(self.datasets_info.keys(),
                                key=lambda x: self.datasets_info[x]['priority']):

            df = self.load_dataset(dataset_key)
            if not df.empty:
                df = self.standardize_columns(df, dataset_key)
                consolidated_dfs.append(df)
                total_records += len(df)

                self.consolidation_stats[dataset_key] = {
                    'records': len(df),
                    'features': len(df.columns),
                    'status': 'loaded'
                }
            else:
                self.consolidation_stats[dataset_key] = {
                    'records': 0,
                    'features': 0,
                    'status': 'failed'
                }

        logger.info(f"📊 Total records to consolidate: {total_records:,}")

        if not consolidated_dfs:
            raise ValueError("No datasets could be loaded!")

        # Concatenate all datasets with proper handling of different schemas
        logger.info("🔗 Concatenating all datasets...")

        # Clean up duplicate columns in each dataset first
        cleaned_dfs = []
        for i, df in enumerate(consolidated_dfs):
            # Remove duplicate columns
            df = df.loc[:, ~df.columns.duplicated()]

            # Add dataset identifier to avoid conflicts
            df = df.copy()
            cleaned_dfs.append(df)

        # Use pandas concat with outer join to handle different schemas
        expert_df = pd.concat(cleaned_dfs, ignore_index=True, sort=False, join='outer')

        logger.info(f"✅ Initial consolidation complete: {len(expert_df):,} records")

        # Remove duplicates (prioritizing higher-priority datasets)
        expert_df = self.remove_duplicates(expert_df)

        # Validate data quality
        expert_df = self.validate_data_quality(expert_df)

        # Filter to high-quality records only
        high_quality_df = expert_df[expert_df['is_validated']].copy()
        logger.info(f"🎯 Final expert dataset: {len(high_quality_df):,} high-quality records")

        return high_quality_df

    def save_expert_dataset(self, df: pd.DataFrame, output_path: str = "data/master/wnba_expert_dataset.csv"):
        """Save the expert dataset"""
        logger.info(f"💾 Saving expert dataset to: {output_path}")

        # Create output directory if it doesn't exist
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)

        # Save the dataset
        df.to_csv(output_path, index=False)

        file_size = Path(output_path).stat().st_size / (1024 * 1024)
        logger.info(f"✅ Expert dataset saved: {file_size:.1f} MB")

        # Create summary report
        summary = {
            'creation_date': datetime.now().isoformat(),
            'total_records': len(df),
            'total_features': len(df.columns),
            'file_size_mb': round(file_size, 2),
            'data_sources': df['data_source'].value_counts().to_dict(),
            'quality_distribution': {
                'high_quality': sum(df['is_validated']),
                'average_quality_score': df['data_quality_score'].mean()
            },
            'consolidation_stats': self.consolidation_stats,
            'schema': list(df.columns)
        }

        summary_path = output_path.replace('.csv', '_summary.json')
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)

        logger.info(f"📊 Summary report saved: {summary_path}")

        return output_path, summary_path


def main():
    """Main function to create the expert dataset"""

    print("WNBA EXPERT DATASET CONSOLIDATION")
    print("=" * 50)
    print("Current situation:")
    print("   • 5 different datasets")
    print("   • 490,068 total records")
    print("   • Multiple schemas")
    print("   • Model confusion")
    print()
    print("🎯 Creating ONE expert dataset for ALL models...")
    print()

    try:
        # Create the consolidator
        creator = WNBAExpertDatasetCreator()

        # Create the expert dataset
        expert_df = creator.create_expert_dataset()

        # Save the expert dataset
        output_path, summary_path = creator.save_expert_dataset(expert_df)

        print()
        print("🎉 EXPERT DATASET CREATION COMPLETE!")
        print("=" * 50)
        print(f"✅ Expert dataset: {output_path}")
        print(f"📊 Summary report: {summary_path}")
        print(f"🎯 Records: {len(expert_df):,}")
        print(f"🔧 Features: {len(expert_df.columns)}")
        print()
        print("🚀 ALL MODELS CAN NOW USE THIS SINGLE EXPERT DATASET!")
        print()
        print("📋 Next steps:")
        print("   1. Update all training scripts to use: data/master/wnba_expert_dataset.csv")
        print("   2. Remove references to old scattered datasets")
        print("   3. Test all models with the new expert dataset")

        return True

    except Exception as e:
        logger.error(f"❌ Error creating expert dataset: {e}")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)