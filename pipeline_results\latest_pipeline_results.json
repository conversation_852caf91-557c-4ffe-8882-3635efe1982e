{"run_id": "run_20250713_032442", "start_time": "2025-07-13T03:24:42.950344", "status": "failed", "stages": {"data_collection": {"success": false, "data": {"real_wnba_data_collector": {"status": "failed", "error": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\wnba\\real_wnba_data_collector.py\", line 639, in <module>\n    main()\n    ~~~~^^\n  File \"C:\\Users\\<USER>\\Documents\\wnba\\real_wnba_data_collector.py\", line 585, in main\n    print(\"\\U0001f4ca REAL WNBA DATA COLLECTOR\")\n    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\n           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f4ca' in position 0: character maps to <undefined>\n"}, "real_injury_system": {"status": "failed", "error": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\wnba\\automated_injury_monitor.py\", line 387, in <module>\n    main()\n    ~~~~^^\n  File \"C:\\Users\\<USER>\\Documents\\wnba\\automated_injury_monitor.py\", line 358, in main\n    print(\"\\U0001f916 AUTOMATED INJURY MONITORING SYSTEM\")\n    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\n           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f916' in position 0: character maps to <undefined>\n"}, "expert_odds_api": {"status": "failed", "error": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\wnba\\expert_odds_api_system.py\", line 540, in <module>\n    demonstrate_expert_odds_system()\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\Documents\\wnba\\expert_odds_api_system.py\", line 502, in demonstrate_expert_odds_system\n    print(\"\\U0001f3af EXPERT ODDS API SYSTEM DEMONSTRATION\")\n    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\n           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f3af' in position 0: character maps to <undefined>\n"}}, "validation_results": {"overall_quality": 0.5, "source_quality": {"real_wnba_data_collector": 0.5, "real_injury_system": 0.5, "expert_odds_api": 0.5}, "issues": []}, "sources_collected": ["real_wnba_data_collector", "real_injury_system", "expert_odds_api"], "errors": []}}, "models_trained": {}, "performance_metrics": {}, "errors": ["Pipeline run run_20250713_032442 failed: Data collection failed"], "end_time": "2025-07-13T03:24:46.390400", "duration_seconds": 3.440056, "success": false, "error": "Pipeline run run_20250713_032442 failed: Data collection failed"}