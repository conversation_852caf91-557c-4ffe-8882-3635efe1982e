#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EXPERT MODEL TARGET MAPPING SYSTEM
==================================

CRITICAL FIX: Defines correct training targets for each model type.
Previously all models were incorrectly trained to predict points.

This system ensures each model is trained on its correct target:
- Core models: Points prediction
- Win probability models: Win probability (0-1)
- Alternates models: Specific stats (rebounds, assists, etc.)
- Repurposed models: Their new targets (game totals, efficiency, etc.)
"""

import logging
from typing import Dict, Any, List, Tuple
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

class ExpertModelTargetMapping:
    """Expert-level target mapping for all model types"""
    
    def __init__(self):
        """Initialize expert target mapping system"""
        
        self.target_mappings = self._define_expert_target_mappings()
        self.target_preprocessing = self._define_target_preprocessing()
        self.evaluation_metrics = self._define_evaluation_metrics()
        
        logger.info("🎯 Expert Model Target Mapping System initialized")
        logger.info(f"   📊 {len(self.target_mappings)} model types configured")
    
    def _define_expert_target_mappings(self) -> Dict[str, Dict[str, Any]]:
        """Define correct targets for each model type"""
        
        return {
            # CORE MODELS (Points Prediction)
            'PlayerPointsModel': {
                'target_column': 'points',
                'target_type': 'regression',
                'target_range': (0, 50),
                'loss_function': 'mse',
                'primary_metric': 'mae'
            },
            'HybridPlayerPointsModel': {
                'target_column': 'points',
                'target_type': 'regression',
                'target_range': (0, 50),
                'loss_function': 'mse',
                'primary_metric': 'mae'
            },
            'MultiTaskPlayerModel': {
                'target_column': 'points',  # Primary target
                'auxiliary_targets': ['minutes', 'rebounds', 'assists', 'steals', 'blocks', 'turnovers', 'plus_minus'],
                'target_type': 'multi_task_regression',
                'target_range': (0, 50),
                'loss_function': 'multi_task_mse',
                'primary_metric': 'mae'
            },
            'BayesianPlayerModel': {
                'target_column': 'points',
                'target_type': 'bayesian_regression',
                'target_range': (0, 50),
                'loss_function': 'bayesian_mse',
                'primary_metric': 'mae'
            },
            'FederatedPlayerModel': {
                'target_column': 'points',
                'target_type': 'regression',
                'target_range': (0, 50),
                'loss_function': 'mse',
                'primary_metric': 'mae'
            },
            
            # WIN PROBABILITY MODELS
            'PreGameWinProbabilityModel': {
                'target_column': 'home_team_win',  # Binary: 1 if home team wins, 0 otherwise
                'target_type': 'binary_classification',
                'target_range': (0, 1),
                'loss_function': 'bce',
                'primary_metric': 'accuracy'
            },
            'LiveWinProbabilityModel': {
                'target_column': 'current_win_probability',  # Real-time win probability
                'target_type': 'regression',
                'target_range': (0, 1),
                'loss_function': 'mse',
                'primary_metric': 'mae'
            },
            'UpsetPredictionModel': {
                'target_column': 'upset_occurred',  # Binary: 1 if upset, 0 otherwise
                'target_type': 'binary_classification',
                'target_range': (0, 1),
                'loss_function': 'bce',
                'primary_metric': 'accuracy'
            },
            
            # ALTERNATES MODELS
            'PlayerReboundsModel': {
                'target_column': 'rebounds',
                'target_type': 'regression',
                'target_range': (0, 20),
                'loss_function': 'mse',
                'primary_metric': 'mae'
            },
            'PlayerAssistsModel': {
                'target_column': 'assists',
                'target_type': 'regression',
                'target_range': (0, 15),
                'loss_function': 'mse',
                'primary_metric': 'mae'
            },
            'PlayerThreePointersModel': {
                'target_column': 'three_pointers_made',
                'target_type': 'regression',
                'target_range': (0, 10),
                'loss_function': 'mse',
                'primary_metric': 'mae'
            },
            'PlayerDoubleDoubleModel': {
                'target_column': 'double_double',  # Binary: 1 if double-double, 0 otherwise
                'target_type': 'binary_classification',
                'target_range': (0, 1),
                'loss_function': 'bce',
                'primary_metric': 'accuracy'
            },
            
            # REPURPOSED MODELS
            'GameTotalsModel': {
                'target_column': 'game_total_points',  # Combined team total
                'target_type': 'regression',
                'target_range': (120, 220),
                'loss_function': 'mse',
                'primary_metric': 'mae'
            },
            'TeamScoringModel': {
                'target_column': 'team_points',  # Individual team total
                'target_type': 'regression',
                'target_range': (60, 120),
                'loss_function': 'mse',
                'primary_metric': 'mae'
            },
            'ReboundPredictionModel': {
                'target_column': 'rebounds',
                'target_type': 'regression',
                'target_range': (0, 20),
                'loss_function': 'mse',
                'primary_metric': 'mae'
            },
            'AssistPredictionModel': {
                'target_column': 'assists',
                'target_type': 'regression',
                'target_range': (0, 15),
                'loss_function': 'mse',
                'primary_metric': 'mae'
            },
            'MinutesPredictionModel': {
                'target_column': 'minutes',
                'target_type': 'regression',
                'target_range': (0, 40),
                'loss_function': 'mse',
                'primary_metric': 'mae'
            },
            'EfficiencyRatingModel': {
                'target_column': 'efficiency_rating',  # PER or similar
                'target_type': 'regression',
                'target_range': (-10, 40),
                'loss_function': 'mse',
                'primary_metric': 'mae'
            }
        }
    
    def _define_target_preprocessing(self) -> Dict[str, Dict[str, Any]]:
        """Define preprocessing steps for each target type"""
        
        return {
            'regression': {
                'normalization': 'min_max',
                'outlier_handling': 'clip',
                'missing_value_strategy': 'median'
            },
            'binary_classification': {
                'encoding': 'binary',
                'class_balance': 'weighted',
                'missing_value_strategy': 'mode'
            },
            'multi_task_regression': {
                'normalization': 'standard',
                'outlier_handling': 'clip',
                'missing_value_strategy': 'median',
                'task_weighting': 'adaptive'
            },
            'bayesian_regression': {
                'normalization': 'standard',
                'uncertainty_estimation': 'enabled',
                'prior_specification': 'weakly_informative'
            }
        }
    
    def _define_evaluation_metrics(self) -> Dict[str, List[str]]:
        """Define evaluation metrics for each target type"""
        
        return {
            'regression': ['mae', 'mse', 'r2', 'mape'],
            'binary_classification': ['accuracy', 'precision', 'recall', 'f1', 'auc'],
            'multi_task_regression': ['mae', 'mse', 'r2', 'task_specific_mae'],
            'bayesian_regression': ['mae', 'mse', 'r2', 'uncertainty_calibration']
        }
    
    def get_model_target_info(self, model_name: str) -> Dict[str, Any]:
        """Get complete target information for a model"""
        
        if model_name not in self.target_mappings:
            logger.warning(f"⚠️ Unknown model: {model_name}, using default points target")
            return self.target_mappings['PlayerPointsModel']
        
        target_info = self.target_mappings[model_name].copy()
        target_type = target_info['target_type']
        
        # Add preprocessing and evaluation info
        target_info['preprocessing'] = self.target_preprocessing.get(target_type, {})
        target_info['evaluation_metrics'] = self.evaluation_metrics.get(target_type, ['mae'])
        
        return target_info
    
    def prepare_target_data(self, data: pd.DataFrame, model_name: str) -> Tuple[pd.DataFrame, np.ndarray]:
        """Prepare target data for a specific model"""
        
        target_info = self.get_model_target_info(model_name)
        target_column = target_info['target_column']
        target_type = target_info['target_type']
        
        if target_column not in data.columns:
            logger.error(f"❌ Target column '{target_column}' not found for {model_name}")
            # Fallback to points if target not available
            if 'points' in data.columns:
                logger.warning(f"⚠️ Using 'points' as fallback target for {model_name}")
                target_column = 'points'
            else:
                raise ValueError(f"Neither '{target_column}' nor 'points' found in data")
        
        # Extract target values
        targets = data[target_column].values
        
        # Handle multi-task models
        if target_type == 'multi_task_regression' and 'auxiliary_targets' in target_info:
            auxiliary_targets = target_info['auxiliary_targets']
            available_aux = [col for col in auxiliary_targets if col in data.columns]
            
            if available_aux:
                aux_data = data[available_aux].values
                targets = {'primary': targets, 'auxiliary': aux_data, 'aux_columns': available_aux}
                logger.info(f"🎯 Multi-task targets for {model_name}: primary + {len(available_aux)} auxiliary")
        
        # Apply preprocessing
        targets = self._apply_target_preprocessing(targets, target_info)
        
        return data, targets
    
    def _apply_target_preprocessing(self, targets: np.ndarray, target_info: Dict[str, Any]) -> np.ndarray:
        """Apply preprocessing to target values"""
        
        target_range = target_info.get('target_range', (0, 50))
        
        if isinstance(targets, dict):
            # Multi-task case
            targets['primary'] = np.clip(targets['primary'], target_range[0], target_range[1])
            return targets
        else:
            # Single task case
            return np.clip(targets, target_range[0], target_range[1])
    
    def get_model_categories(self) -> Dict[str, List[str]]:
        """Get models grouped by category"""
        
        categories = {
            'core_models': [],
            'win_probability_models': [],
            'alternates_models': [],
            'repurposed_models': []
        }
        
        for model_name, target_info in self.target_mappings.items():
            target_column = target_info['target_column']
            
            if target_column == 'points':
                categories['core_models'].append(model_name)
            elif 'win' in target_column.lower() or 'upset' in target_column.lower():
                categories['win_probability_models'].append(model_name)
            elif model_name.startswith('Player') and target_column != 'points':
                categories['alternates_models'].append(model_name)
            else:
                categories['repurposed_models'].append(model_name)
        
        return categories
    
    def validate_training_targets(self, data: pd.DataFrame) -> Dict[str, bool]:
        """Validate that all required target columns exist in data"""
        
        validation_results = {}
        missing_targets = []
        
        for model_name, target_info in self.target_mappings.items():
            target_column = target_info['target_column']
            
            if target_column in data.columns:
                validation_results[model_name] = True
            else:
                validation_results[model_name] = False
                missing_targets.append(f"{model_name}: {target_column}")
        
        if missing_targets:
            logger.warning(f"⚠️ Missing target columns for {len(missing_targets)} models:")
            for missing in missing_targets:
                logger.warning(f"   {missing}")
        
        return validation_results

# Global instance
expert_target_mapper = ExpertModelTargetMapping()
