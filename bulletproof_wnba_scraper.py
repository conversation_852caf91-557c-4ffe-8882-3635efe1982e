#!/usr/bin/env python3
"""
🏀 BULLETPROOF WNBA PROPS SCRAPER - ALL FIXES APPLIED
====================================================

COMPREHENSIVE FIXES IMPLEMENTED:
- ✅ Connection Stability Enhancements
- ✅ GPU Configuration Overhaul
- ✅ Deprecated Endpoint Workaround
- ✅ TensorFlow Conflict Resolution
- ✅ Connection Reset Protection
- ✅ Network Stack Hardening
- ✅ Robust Error Handling

Version: 8.0 (Bulletproof)
Date: 2025-07-13
"""

import time
import json
import re
import random
import os
import socket
import ctypes
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Network and connection imports
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Selenium imports
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager

# Environment hardening
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # Suppress TensorFlow logs
os.environ['NO_PROXY'] = 'localhost,127.0.0.1'

# Global socket timeout
socket.setdefaulttimeout(45)

# Windows-specific connection reset protection
if os.name == 'nt':
    try:
        INTERNET_OPTION_HTTP_DECODING = 65
        value = ctypes.c_ulong(0)
        ctypes.windll.winhttp.WinHttpSetOption(None, INTERNET_OPTION_HTTP_DECODING, 
                                               ctypes.byref(value), ctypes.sizeof(value))
    except Exception as e:
        print(f"⚠️ Windows network optimization failed: {e}")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BulletproofWNBAScraper:
    """Bulletproof WNBA Props Scraper with all fixes applied"""
    
    def __init__(self, visible: bool = True):
        """Initialize bulletproof scraper"""
        
        self.visible = visible
        self.driver = None
        self.session = None
        
        # Setup robust HTTP session
        self.setup_robust_session()
        
        # WNBA Props indicators with weights
        self.WNBA_INDICATORS = {
            'player prop': 10, 'proposition': 9, 'points': 9, 'rebounds': 8, 'assists': 8,
            'pra': 10, 'double double': 9, 'odds': 8, 'line': 8, 'over/under': 9,
            'sportsbook': 7, 'bet': 7, 'wager': 6, 'wnba': 8, 'basketball': 6
        }
        
        # WNBA Players
        self.WNBA_PLAYERS = [
            "A'ja Wilson", "Breanna Stewart", "Diana Taurasi", "Sabrina Ionescu",
            "Alyssa Thomas", "Kelsey Plum", "Jonquel Jones", "Napheesa Collier",
            "Caitlin Clark", "Angel Reese", "Cameron Brink"
        ]
        
        logger.info("🏀 Bulletproof WNBA Scraper initialized with all fixes")
    
    def setup_robust_session(self):
        """Setup robust HTTP session with retry strategy"""
        
        # Robust connection handling
        retry_strategy = Retry(
            total=5,
            backoff_factor=1,
            status_forcelist=[500, 502, 503, 504, 429],
            allowed_methods=["GET", "POST"],
            respect_retry_after_header=True
        )
        
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=100,
            pool_maxsize=100
        )
        
        self.session = requests.Session()
        self.session.mount("https://", adapter)
        self.session.mount("http://", adapter)
        
        # Set headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
        
        logger.info("✅ Robust HTTP session configured")
    
    def setup_bulletproof_driver(self) -> bool:
        """Setup bulletproof Chrome driver with all fixes"""
        
        try:
            # GPU Configuration Overhaul
            chrome_options = Options()
            
            # Core stability options
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-software-rasterizer")
            chrome_options.add_argument("--disable-webgl")
            chrome_options.add_argument("--disable-webgl2")
            
            # Deprecated endpoint workaround
            chrome_options.add_argument("--disable-features=GCM,NetworkService")
            chrome_options.add_argument("--disable-background-networking")
            chrome_options.add_argument("--disable-background-timer-throttling")
            chrome_options.add_argument("--disable-renderer-backgrounding")
            
            # Anti-detection measures
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option('excludeSwitches', ['enable-logging', 'enable-automation'])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # Logging suppression
            chrome_options.add_argument("--log-level=3")
            chrome_options.add_argument("--silent")
            chrome_options.add_argument("--disable-logging")
            
            # Performance optimizations
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            chrome_options.add_argument("--disable-images")
            chrome_options.add_argument("--disable-javascript")  # We'll enable selectively
            
            # Visibility setting
            if not self.visible:
                chrome_options.add_argument("--headless=new")
                logger.info("🔧 Running in bulletproof headless mode")
            else:
                logger.info("🔧 Running in visible mode for monitoring")
            
            # Window configuration
            chrome_options.add_argument("--window-size=1366,768")
            chrome_options.add_argument("--start-maximized")
            
            # User agent
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            # Preferences
            prefs = {
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0,
                "profile.managed_default_content_settings.images": 2,  # Block images
                "profile.managed_default_content_settings.javascript": 1,  # Allow JS
                "profile.managed_default_content_settings.plugins": 2,
                "profile.managed_default_content_settings.media_stream": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            # Create service
            service = Service(ChromeDriverManager().install())
            
            # Create driver with timeouts
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.set_page_load_timeout(60)
            self.driver.implicitly_wait(10)
            
            # Execute anti-detection scripts
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
            self.driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})")
            
            logger.info("✅ Bulletproof Chrome driver setup complete")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup bulletproof driver: {e}")
            return False
    
    def robust_navigate(self, url: str, max_attempts: int = 5) -> bool:
        """Navigate with robust error handling and retries"""
        
        for attempt in range(max_attempts):
            try:
                logger.info(f"🌐 Attempt {attempt + 1}/{max_attempts}: Navigating to {url}")
                
                # Use JavaScript navigation for better reliability
                self.driver.execute_script(f"window.location.href = '{url}'")
                
                # Wait for page load
                WebDriverWait(self.driver, 30).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                
                # Additional wait for dynamic content
                time.sleep(3)
                
                # Verify page loaded
                current_url = self.driver.current_url
                page_title = self.driver.title
                
                if current_url and page_title:
                    logger.info(f"✅ Successfully loaded: {page_title}")
                    return True
                else:
                    raise Exception("Page did not load properly")
                    
            except (TimeoutException, WebDriverException, Exception) as e:
                logger.warning(f"⚠️ Attempt {attempt + 1} failed: {str(e)}")
                
                if attempt < max_attempts - 1:
                    # Exponential backoff
                    wait_time = (2 ** attempt) + random.uniform(1, 3)
                    logger.info(f"⏰ Waiting {wait_time:.1f}s before retry...")
                    time.sleep(wait_time)
                    
                    # Try to refresh driver if needed
                    try:
                        self.driver.refresh()
                        time.sleep(2)
                    except:
                        pass
                else:
                    logger.error(f"❌ All navigation attempts failed for {url}")
                    return False
        
        return False

    def robust_content_extraction(self, url: str) -> Dict[str, Any]:
        """Extract content with robust error handling"""

        results = {
            'url': url,
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'content_analysis': {},
            'props_found': [],
            'errors': []
        }

        try:
            # Navigate with retries
            if not self.robust_navigate(url):
                results['errors'].append("Navigation failed")
                return results

            # Get page content
            page_source = self.driver.page_source
            page_title = self.driver.title

            # Content analysis
            results['content_analysis'] = {
                'title': page_title,
                'page_size': len(page_source),
                'current_url': self.driver.current_url
            }

            # Calculate relevance
            relevance_score = self.calculate_relevance(page_source)
            results['content_analysis']['relevance_score'] = relevance_score

            # Detect WNBA context
            wnba_context = self.detect_wnba_context(page_source)
            results['content_analysis']['wnba_context'] = wnba_context

            logger.info(f"📊 Content Analysis: Relevance {relevance_score}, WNBA Context {wnba_context}")

            # Extract props if relevant
            if relevance_score > 5 or wnba_context:
                props = self.extract_props_patterns(page_source)
                results['props_found'] = props
                logger.info(f"🎯 Extracted {len(props)} props")

            results['success'] = True
            return results

        except Exception as e:
            error_msg = f"Content extraction failed: {str(e)}"
            logger.error(f"❌ {error_msg}")
            results['errors'].append(error_msg)
            return results

    def calculate_relevance(self, text: str) -> int:
        """Calculate relevance score based on indicators"""

        score = 0
        text_lower = text.lower()

        for indicator, weight in self.WNBA_INDICATORS.items():
            if indicator in text_lower:
                score += weight

        return score

    def detect_wnba_context(self, text: str) -> bool:
        """Detect WNBA context in content"""

        text_lower = text.lower()

        # Check for WNBA terms
        wnba_terms = ['wnba', 'women\'s basketball', 'women basketball']
        if any(term in text_lower for term in wnba_terms):
            return True

        # Check for player names
        if any(player.lower() in text_lower for player in self.WNBA_PLAYERS):
            return True

        return False

    def extract_props_patterns(self, text: str) -> List[Dict[str, Any]]:
        """Extract props using pattern recognition"""

        props = []

        # Prop patterns
        patterns = {
            'points': r'(\w+\s+\w+)\s+points?\s+(over|under)\s+(\d+\.?\d*)',
            'rebounds': r'(\w+\s+\w+)\s+rebounds?\s+(over|under)\s+(\d+\.?\d*)',
            'assists': r'(\w+\s+\w+)\s+assists?\s+(over|under)\s+(\d+\.?\d*)',
            'pra': r'(\w+\s+\w+)\s+(?:pra|pts\+reb\+ast)\s+(over|under)\s+(\d+\.?\d*)'
        }

        for prop_type, pattern in patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)

            for match in matches:
                if len(match) >= 3:
                    prop = {
                        'player': match[0].strip(),
                        'prop_type': prop_type,
                        'side': match[1].upper(),
                        'line': match[2],
                        'timestamp': datetime.now().isoformat()
                    }

                    # Validate player name
                    if any(player.lower() in prop['player'].lower() for player in self.WNBA_PLAYERS):
                        props.append(prop)

        return props

    def run_bulletproof_session(self) -> Dict[str, Any]:
        """Run bulletproof scraping session"""

        logger.info("🏀 Starting BULLETPROOF WNBA Scraping Session")
        logger.info("=" * 70)

        # Setup driver
        if not self.setup_bulletproof_driver():
            return {"error": "Failed to setup bulletproof driver"}

        session_results = {
            'session_start': datetime.now().isoformat(),
            'sites_scraped': {},
            'total_props': [],
            'session_summary': {}
        }

        # Target sites
        target_sites = {
            "ESPN_WNBA": "https://www.espn.com/wnba/",
            "WNBA_Official": "https://www.wnba.com/",
            "DraftKings": "https://sportsbook.draftkings.com/",
            "FanDuel": "https://sportsbook.fanduel.com/"
        }

        try:
            total_props = []
            successful_sites = 0

            for site_name, url in target_sites.items():
                logger.info(f"🎯 Bulletproof scraping {site_name}...")

                # Extract content with robust handling
                site_results = self.robust_content_extraction(url)
                session_results['sites_scraped'][site_name] = site_results

                if site_results['success']:
                    successful_sites += 1
                    site_props = site_results.get('props_found', [])
                    total_props.extend(site_props)

                    logger.info(f"✅ {site_name}: {len(site_props)} props found")
                else:
                    logger.warning(f"❌ {site_name}: Failed")

                # Delay between sites
                time.sleep(random.uniform(3, 7))

            # Deduplicate props
            unique_props = self.deduplicate_props(total_props)
            session_results['total_props'] = unique_props

            # Session summary
            session_results['session_summary'] = {
                'sites_attempted': len(target_sites),
                'successful_sites': successful_sites,
                'total_props_found': len(unique_props),
                'session_status': 'completed'
            }

        finally:
            # Always close driver
            if self.driver:
                self.driver.quit()
                logger.info("✅ Bulletproof session ended - driver closed")

        return session_results

    def deduplicate_props(self, props: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate props"""

        seen = set()
        unique_props = []

        for prop in props:
            key = (prop.get('player', '').lower(), prop.get('prop_type', ''), prop.get('line', ''))
            if key not in seen:
                seen.add(key)
                unique_props.append(prop)

        return unique_props

    def display_bulletproof_results(self, results: Dict[str, Any]):
        """Display bulletproof session results"""

        print("\n🏀 BULLETPROOF WNBA SCRAPING RESULTS")
        print("=" * 70)
        print(f"📅 Session: {results.get('session_start', 'Unknown')}")
        print("🛡️ All Fixes Applied: GPU, Network, Endpoints, TensorFlow")
        print("=" * 70)

        if 'error' in results:
            print(f"❌ Session failed: {results['error']}")
            return

        # Session summary
        summary = results.get('session_summary', {})
        print(f"📊 SESSION SUMMARY:")
        print(f"   Sites Attempted: {summary.get('sites_attempted', 0)}")
        print(f"   Successful Sites: {summary.get('successful_sites', 0)}")
        print(f"   Total Props Found: {summary.get('total_props_found', 0)}")
        print(f"   Status: {summary.get('session_status', 'Unknown')}")

        # Props found
        props_found = results.get('total_props', [])
        if props_found:
            print(f"\n🎯 PROPS EXTRACTED ({len(props_found)} total):")
            for i, prop in enumerate(props_found, 1):
                print(f"   {i}. {prop['player']} - {prop['prop_type'].upper()}")
                print(f"      {prop['side']} {prop['line']}")
        else:
            print(f"\n❌ No props extracted")

        # Site analysis
        print(f"\n🌐 SITE-BY-SITE ANALYSIS:")
        sites_scraped = results.get('sites_scraped', {})

        for site_name, site_data in sites_scraped.items():
            print(f"\n🏪 {site_name.upper()}:")

            if not site_data.get('success', False):
                errors = site_data.get('errors', [])
                print(f"   ❌ Failed: {', '.join(errors)}")
                continue

            content = site_data.get('content_analysis', {})
            props = site_data.get('props_found', [])

            print(f"   ✅ Success")
            print(f"   📄 Title: {content.get('title', 'Unknown')[:40]}...")
            print(f"   🎯 Relevance: {content.get('relevance_score', 0)}")
            print(f"   🏀 WNBA Context: {'✅' if content.get('wnba_context') else '❌'}")
            print(f"   📊 Props: {len(props)}")

        print("=" * 70)
        print("🎉 Bulletproof scraping complete!")


def main():
    """Main execution with bulletproof configuration"""

    print("🏀 BULLETPROOF WNBA PROPS SCRAPER")
    print("=" * 50)
    print("🛡️ COMPREHENSIVE FIXES APPLIED:")
    print("   ✅ Connection Stability Enhancements")
    print("   ✅ GPU Configuration Overhaul")
    print("   ✅ Deprecated Endpoint Workaround")
    print("   ✅ TensorFlow Conflict Resolution")
    print("   ✅ Connection Reset Protection")
    print("   ✅ Network Stack Hardening")
    print("   ✅ Robust Error Handling")
    print("=" * 50)
    print("⚠️  Running bulletproof session with all fixes")
    print("=" * 50)

    # Initialize bulletproof scraper
    scraper = BulletproofWNBAScraper(visible=True)

    try:
        # Run bulletproof session
        results = scraper.run_bulletproof_session()

        # Display results
        scraper.display_bulletproof_results(results)

        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"bulletproof_wnba_results_{timestamp}.json"

        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        total_props = len(results.get('total_props', []))
        successful_sites = results.get('session_summary', {}).get('successful_sites', 0)

        print(f"\n💾 Results saved to {filename}")
        print(f"🎯 Total props extracted: {total_props}")
        print(f"✅ Successful sites: {successful_sites}")

        return results

    except KeyboardInterrupt:
        print("\n⚠️ Bulletproof session interrupted by user")
    except Exception as e:
        print(f"\n❌ Bulletproof session failed: {e}")


if __name__ == "__main__":
    main()
