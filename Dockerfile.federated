FROM python:3.11-slim

WORKDIR /app

# Install dependencies
RUN apt-get update && apt-get install -y gcc g++ && rm -rf /var/lib/apt/lists/*
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy federated learning components
COPY src/federated_learning/ ./src/federated_learning/
COPY federated_multiverse_integration.py .
COPY *.py .

# Create user
RUN useradd -m -u 1000 federated && chown -R federated:federated /app
USER federated

EXPOSE 8080

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8080/health')"

CMD ["python", "src/federated_learning/federated_wnba_server.py"]