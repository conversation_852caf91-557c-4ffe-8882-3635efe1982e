#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
REAL-TIME OVERFITTING DETECTION SYSTEM
======================================

Prevents future MultiTaskPlayerModel disasters by detecting:
- Training/validation performance gaps
- Data leakage in multi-task targets
- Systematic biases across player tiers
- Metric inconsistencies (MAE/R² paradoxes)

Real-time monitoring with automated alerts and model quarantine.
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from typing import Dict, List, Tuple, Optional, Any
import logging
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
import warnings

# Fix Windows encoding
if os.name == 'nt':
    if hasattr(sys.stdout, 'reconfigure'):
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    if hasattr(sys.stderr, 'reconfigure'):
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')

logger = logging.getLogger(__name__)

class RealTimeOverfittingDetector:
    """
    Real-Time Overfitting Detection System
    
    Monitors training in real-time to detect:
    1. Overfitting (train/val performance gaps)
    2. Data leakage (suspicious correlations)
    3. Systematic biases (tier-specific errors)
    4. Metric inconsistencies (MAE/R² paradoxes)
    """
    
    def __init__(self, alert_thresholds: Optional[Dict[str, float]] = None):
        """Initialize real-time overfitting detector"""
        
        # Default alert thresholds
        self.thresholds = alert_thresholds or {
            'severe_overfitting_mae_gap': 0.1,      # MAE gap > 0.1 = severe
            'moderate_overfitting_mae_gap': 0.05,   # MAE gap > 0.05 = moderate
            'severe_overfitting_r2_gap': 0.05,      # R² gap > 0.05 = severe
            'data_leakage_correlation': 0.8,        # Correlation > 0.8 = leakage
            'bias_threshold': 0.3,                  # Bias > 0.3 = systematic
            'metric_inconsistency_threshold': 0.02, # MAE/R² rank diff > 2 = inconsistent
            'validation_instability': 0.1          # Val std > 0.1 = unstable
        }
        
        # Monitoring state
        self.training_history = []
        self.alerts = []
        self.quarantined_models = set()
        self.monitoring_active = False
        
        # Performance tracking
        self.epoch_metrics = {
            'train_mae': [], 'val_mae': [], 'train_r2': [], 'val_r2': [],
            'mae_gap': [], 'r2_gap': [], 'timestamps': []
        }
        
        logger.info("Real-Time Overfitting Detector initialized")
    
    def start_monitoring(self, model_name: str):
        """Start real-time monitoring for a model"""
        self.monitoring_active = True
        self.current_model = model_name
        self.epoch_metrics = {k: [] for k in self.epoch_metrics.keys()}
        
        logger.info(f"Started real-time monitoring for {model_name}")
    
    def log_epoch_metrics(self, epoch: int, train_metrics: Dict[str, float], 
                         val_metrics: Dict[str, float]) -> Dict[str, Any]:
        """Log metrics for current epoch and detect overfitting"""
        
        if not self.monitoring_active:
            return {'status': 'monitoring_inactive'}
        
        timestamp = datetime.now()
        
        # Extract metrics
        train_mae = train_metrics.get('mae', 0.0)
        train_r2 = train_metrics.get('r2', 0.0)
        val_mae = val_metrics.get('mae', 0.0)
        val_r2 = val_metrics.get('r2', 0.0)
        
        # Calculate gaps
        mae_gap = val_mae - train_mae
        r2_gap = train_r2 - val_r2
        
        # Store metrics
        self.epoch_metrics['train_mae'].append(train_mae)
        self.epoch_metrics['val_mae'].append(val_mae)
        self.epoch_metrics['train_r2'].append(train_r2)
        self.epoch_metrics['val_r2'].append(val_r2)
        self.epoch_metrics['mae_gap'].append(mae_gap)
        self.epoch_metrics['r2_gap'].append(r2_gap)
        self.epoch_metrics['timestamps'].append(timestamp)
        
        # Detect overfitting
        overfitting_alerts = self._detect_overfitting(epoch, mae_gap, r2_gap)
        
        # Detect metric inconsistencies
        consistency_alerts = self._detect_metric_inconsistencies(train_mae, train_r2, val_mae, val_r2)
        
        # Detect validation instability
        stability_alerts = self._detect_validation_instability()
        
        # Combine all alerts
        all_alerts = overfitting_alerts + consistency_alerts + stability_alerts
        
        # Log alerts
        for alert in all_alerts:
            self._log_alert(alert)
        
        # Check for quarantine conditions
        quarantine_decision = self._check_quarantine_conditions(all_alerts)
        
        return {
            'epoch': epoch,
            'mae_gap': mae_gap,
            'r2_gap': r2_gap,
            'alerts': all_alerts,
            'quarantine_recommended': quarantine_decision,
            'monitoring_status': 'active'
        }
    
    def _detect_overfitting(self, epoch: int, mae_gap: float, r2_gap: float) -> List[Dict[str, Any]]:
        """Detect overfitting patterns"""
        alerts = []
        
        # Severe overfitting detection
        if mae_gap > self.thresholds['severe_overfitting_mae_gap']:
            alerts.append({
                'type': 'SEVERE_OVERFITTING',
                'severity': 'CRITICAL',
                'metric': 'MAE_GAP',
                'value': mae_gap,
                'threshold': self.thresholds['severe_overfitting_mae_gap'],
                'epoch': epoch,
                'message': f"SEVERE overfitting detected: MAE gap {mae_gap:.3f} > {self.thresholds['severe_overfitting_mae_gap']}"
            })
        
        elif mae_gap > self.thresholds['moderate_overfitting_mae_gap']:
            alerts.append({
                'type': 'MODERATE_OVERFITTING',
                'severity': 'WARNING',
                'metric': 'MAE_GAP',
                'value': mae_gap,
                'threshold': self.thresholds['moderate_overfitting_mae_gap'],
                'epoch': epoch,
                'message': f"Moderate overfitting detected: MAE gap {mae_gap:.3f} > {self.thresholds['moderate_overfitting_mae_gap']}"
            })
        
        # R² gap detection
        if r2_gap > self.thresholds['severe_overfitting_r2_gap']:
            alerts.append({
                'type': 'R2_OVERFITTING',
                'severity': 'CRITICAL',
                'metric': 'R2_GAP',
                'value': r2_gap,
                'threshold': self.thresholds['severe_overfitting_r2_gap'],
                'epoch': epoch,
                'message': f"R² overfitting detected: R² gap {r2_gap:.3f} > {self.thresholds['severe_overfitting_r2_gap']}"
            })
        
        return alerts
    
    def _detect_metric_inconsistencies(self, train_mae: float, train_r2: float, 
                                     val_mae: float, val_r2: float) -> List[Dict[str, Any]]:
        """Detect MAE/R² paradoxes like MultiTaskPlayerModel"""
        alerts = []
        
        # Check for MAE/R² ranking inconsistency
        # Good MAE should correlate with good R²
        mae_percentile = self._calculate_percentile(val_mae, self.epoch_metrics['val_mae'])
        r2_percentile = self._calculate_percentile(val_r2, self.epoch_metrics['val_r2'])
        
        # If MAE is much better than R² (or vice versa), flag inconsistency
        percentile_diff = abs(mae_percentile - r2_percentile)
        
        if percentile_diff > 0.3:  # 30% percentile difference
            alerts.append({
                'type': 'METRIC_INCONSISTENCY',
                'severity': 'WARNING',
                'metric': 'MAE_R2_PARADOX',
                'value': percentile_diff,
                'threshold': 0.3,
                'message': f"MAE/R² paradox detected: MAE percentile {mae_percentile:.2f}, R² percentile {r2_percentile:.2f}"
            })
        
        return alerts
    
    def _detect_validation_instability(self) -> List[Dict[str, Any]]:
        """Detect unstable validation performance"""
        alerts = []
        
        if len(self.epoch_metrics['val_mae']) < 5:
            return alerts  # Need at least 5 epochs
        
        # Calculate recent validation stability
        recent_val_mae = self.epoch_metrics['val_mae'][-5:]
        val_std = np.std(recent_val_mae)
        
        if val_std > self.thresholds['validation_instability']:
            alerts.append({
                'type': 'VALIDATION_INSTABILITY',
                'severity': 'WARNING',
                'metric': 'VAL_STD',
                'value': val_std,
                'threshold': self.thresholds['validation_instability'],
                'message': f"Validation instability detected: std {val_std:.3f} > {self.thresholds['validation_instability']}"
            })
        
        return alerts
    
    def detect_data_leakage(self, features: pd.DataFrame, targets: pd.DataFrame) -> List[Dict[str, Any]]:
        """Detect data leakage in features/targets"""
        alerts = []
        
        # Calculate correlations between features and targets
        correlations = {}
        
        for feature_col in features.columns:
            if feature_col in targets.columns:
                continue  # Skip if feature is also a target
            
            for target_col in targets.columns:
                try:
                    corr = features[feature_col].corr(targets[target_col])
                    if not np.isnan(corr):
                        correlations[f"{feature_col}_vs_{target_col}"] = abs(corr)
                except:
                    continue
        
        # Check for suspicious correlations
        for pair, corr in correlations.items():
            if corr > self.thresholds['data_leakage_correlation']:
                alerts.append({
                    'type': 'DATA_LEAKAGE',
                    'severity': 'CRITICAL',
                    'metric': 'CORRELATION',
                    'value': corr,
                    'threshold': self.thresholds['data_leakage_correlation'],
                    'feature_pair': pair,
                    'message': f"Data leakage detected: {pair} correlation {corr:.3f} > {self.thresholds['data_leakage_correlation']}"
                })
        
        return alerts
    
    def _calculate_percentile(self, value: float, history: List[float]) -> float:
        """Calculate percentile of value in history"""
        if not history:
            return 0.5
        
        return np.mean([v <= value for v in history])
    
    def _log_alert(self, alert: Dict[str, Any]):
        """Log alert with timestamp"""
        alert['timestamp'] = datetime.now().isoformat()
        alert['model'] = getattr(self, 'current_model', 'unknown')
        
        self.alerts.append(alert)
        
        # Log to console based on severity
        if alert['severity'] == 'CRITICAL':
            logger.error(f"CRITICAL ALERT: {alert['message']}")
        else:
            logger.warning(f"WARNING: {alert['message']}")
    
    def _check_quarantine_conditions(self, alerts: List[Dict[str, Any]]) -> bool:
        """Check if model should be quarantined"""
        
        # Quarantine conditions
        critical_alerts = [a for a in alerts if a['severity'] == 'CRITICAL']
        severe_overfitting = any(a['type'] == 'SEVERE_OVERFITTING' for a in alerts)
        data_leakage = any(a['type'] == 'DATA_LEAKAGE' for a in alerts)
        
        # Quarantine if critical conditions met
        if critical_alerts or severe_overfitting or data_leakage:
            if hasattr(self, 'current_model'):
                self.quarantined_models.add(self.current_model)
                logger.error(f"MODEL QUARANTINED: {self.current_model}")
            return True
        
        return False
    
    def get_monitoring_report(self) -> Dict[str, Any]:
        """Generate comprehensive monitoring report"""
        
        if not self.epoch_metrics['timestamps']:
            return {'status': 'no_data'}
        
        # Calculate summary statistics
        final_mae_gap = self.epoch_metrics['mae_gap'][-1] if self.epoch_metrics['mae_gap'] else 0
        final_r2_gap = self.epoch_metrics['r2_gap'][-1] if self.epoch_metrics['r2_gap'] else 0
        
        # Count alerts by type
        alert_counts = {}
        for alert in self.alerts:
            alert_type = alert['type']
            alert_counts[alert_type] = alert_counts.get(alert_type, 0) + 1
        
        return {
            'model': getattr(self, 'current_model', 'unknown'),
            'monitoring_duration': len(self.epoch_metrics['timestamps']),
            'final_mae_gap': final_mae_gap,
            'final_r2_gap': final_r2_gap,
            'total_alerts': len(self.alerts),
            'alert_breakdown': alert_counts,
            'quarantined': getattr(self, 'current_model', '') in self.quarantined_models,
            'overfitting_detected': final_mae_gap > self.thresholds['moderate_overfitting_mae_gap'],
            'recommendations': self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on detected issues"""
        recommendations = []
        
        if self.alerts:
            critical_alerts = [a for a in self.alerts if a['severity'] == 'CRITICAL']
            
            if critical_alerts:
                recommendations.append("IMMEDIATE: Stop training and investigate critical alerts")
            
            if any(a['type'] == 'DATA_LEAKAGE' for a in self.alerts):
                recommendations.append("Remove leaky features from training data")
            
            if any(a['type'] == 'SEVERE_OVERFITTING' for a in self.alerts):
                recommendations.append("Reduce model complexity or increase regularization")
            
            if any(a['type'] == 'METRIC_INCONSISTENCY' for a in self.alerts):
                recommendations.append("Investigate MAE/R² paradox - possible overfitting or bias")
        
        return recommendations

def main():
    """Test real-time overfitting detector"""
    
    print("REAL-TIME OVERFITTING DETECTOR TEST")
    print("=" * 50)
    
    # Initialize detector
    detector = RealTimeOverfittingDetector()
    
    # Start monitoring
    detector.start_monitoring("TestModel")
    
    # Simulate training with overfitting
    print("Simulating training with overfitting pattern...")
    
    for epoch in range(1, 11):
        # Simulate overfitting: good training, poor validation
        train_metrics = {'mae': 0.6 - epoch * 0.01, 'r2': 0.9 + epoch * 0.001}
        val_metrics = {'mae': 0.7 + epoch * 0.02, 'r2': 0.85 - epoch * 0.005}
        
        result = detector.log_epoch_metrics(epoch, train_metrics, val_metrics)
        
        if result['alerts']:
            print(f"Epoch {epoch}: {len(result['alerts'])} alerts detected")
    
    # Generate report
    report = detector.get_monitoring_report()
    print(f"\nMonitoring Report:")
    print(f"Total alerts: {report['total_alerts']}")
    print(f"Quarantined: {report['quarantined']}")
    print(f"Overfitting detected: {report['overfitting_detected']}")
    
    print("\nReal-Time Overfitting Detector ready for production!")

if __name__ == "__main__":
    main()
