# 🔧 **WN<PERSON> PREDICTION SYSTEM - ENVIRO<PERSON>MENT SETUP GUIDE**
## **Complete API Access & Credentials Configuration**

---

## 🚨 **SECURITY NOTICE**
**✅ FIXED:** Hardcoded API key removed from `expert_odds_api_system.py`
**🔒 SECURE:** All credentials now use environment variables

---

## 📋 **REQUIRED API CREDENTIALS**

### **1. 🎯 The Odds API (Required for Live Odds)**
```bash
# Get your API key from: https://the-odds-api.com/
export ODDS_API_KEY="your_odds_api_key_here"
```
**Usage:** Live WNBA odds, spreads, totals
**Rate Limit:** 500 requests/day (free tier)
**Cost:** Free tier available, paid plans for higher limits

### **2. 🏀 NBA/WNBA API (Public - No Key Required)**
```bash
# No API key required - uses headers for access
# Rate limited to 1.5 requests/second
```
**Usage:** Player stats, team stats, game data
**Rate Limit:** 1000 requests/day, 15000/month
**Cost:** Free

### **3. 🗄️ Database (Production Only)**
```bash
# For production deployment
export DATABASE_URL="postgresql://user:password@host:port/database"
```

---

## 🔧 **ENVIRONMENT SETUP**

### **🖥️ Local Development Setup**

#### **1. Create Environment File**
```bash
# Create .env file in project root
cat > .env << EOF
# The Odds API
ODDS_API_KEY=your_odds_api_key_here

# Development Database (optional)
DATABASE_URL=sqlite:///local_wnba.db

# API Rate Limits
NBA_API_DAILY_LIMIT=1000
ODDS_API_DAILY_LIMIT=500

# Scraping Configuration
SCRAPING_DELAY_SECONDS=2
USER_AGENT_ROTATION=true

# Logging
LOG_LEVEL=INFO
LOG_FILE=wnba_system.log
EOF
```

#### **2. Load Environment Variables**
```bash
# Option 1: Source the file
source .env

# Option 2: Use python-dotenv (recommended)
pip install python-dotenv
```

#### **3. Python Environment Loading**
```python
# Add to your Python scripts
import os
from dotenv import load_dotenv

load_dotenv()  # Load .env file

# Access variables
odds_api_key = os.getenv('ODDS_API_KEY')
```

### **🐳 Docker Setup**

#### **1. Docker Environment File**
```bash
# Create docker.env
cat > docker.env << EOF
ODDS_API_KEY=your_odds_api_key_here
DATABASE_URL=**********************************/wnba
ENVIRONMENT=production
LOG_LEVEL=INFO
EOF
```

#### **2. Docker Compose Configuration**
```yaml
# docker-compose.yml
version: '3.8'
services:
  wnba-prediction:
    build: .
    env_file:
      - docker.env
    ports:
      - "5000:5000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
```

### **☸️ Kubernetes Setup**

#### **1. Create Secrets**
```bash
# Create Kubernetes secret
kubectl create secret generic wnba-secrets \
  --from-literal=odds-api-key="your_odds_api_key_here" \
  --from-literal=database-url="postgresql://user:password@host:port/db" \
  -n wnba-prediction
```

#### **2. Update Deployment**
```yaml
# k8s/deployment.yaml
env:
- name: ODDS_API_KEY
  valueFrom:
    secretKeyRef:
      name: wnba-secrets
      key: odds-api-key
- name: DATABASE_URL
  valueFrom:
    secretKeyRef:
      name: wnba-secrets
      key: database-url
```

---

## 🔍 **API ACCESS VERIFICATION**

### **🧪 Test API Connections**
```bash
# Run the API test script
python test_api_connections.py
```

### **📊 Check API Usage**
```bash
# View current API usage
cat api_credit_tracking.json

# Check odds API cache
sqlite3 expert_odds_cache.db "SELECT COUNT(*) FROM api_calls;"
```

### **🔧 Verify Environment**
```python
# Quick environment check
import os

required_vars = ['ODDS_API_KEY']
missing_vars = [var for var in required_vars if not os.getenv(var)]

if missing_vars:
    print(f"❌ Missing environment variables: {missing_vars}")
else:
    print("✅ All required environment variables set")
```

---

## 🌐 **NETWORK REQUIREMENTS**

### **📡 Outbound Connections Required**
```bash
# The Odds API
https://api.the-odds-api.com:443

# NBA/WNBA Stats API
https://stats.nba.com:443

# WNBA Official Website
https://www.wnba.com:443

# Sportsbook websites (for props scraping)
# Various HTTPS endpoints on port 443
```

### **🔒 Firewall Configuration**
```bash
# Allow outbound HTTPS
iptables -A OUTPUT -p tcp --dport 443 -j ACCEPT

# Allow outbound HTTP (if needed)
iptables -A OUTPUT -p tcp --dport 80 -j ACCEPT

# Allow DNS
iptables -A OUTPUT -p udp --dport 53 -j ACCEPT
```

---

## 📊 **MONITORING & LOGGING**

### **📈 API Usage Monitoring**
- **File:** `api_credit_tracking.json`
- **Database:** `expert_odds_cache.db`
- **Logs:** `automated_training_pipeline.log`

### **🔍 Health Checks**
```bash
# Check prediction server
curl http://localhost:5000/health

# Check API connectivity
python -c "
import requests
response = requests.get('https://api.the-odds-api.com/v4/sports')
print(f'Odds API Status: {response.status_code}')
"
```

---

## 🚨 **TROUBLESHOOTING**

### **❌ Common Issues**

#### **1. Missing API Key**
```
Error: ODDS_API_KEY environment variable not set
Solution: Set the environment variable or add to .env file
```

#### **2. Rate Limit Exceeded**
```
Error: API rate limit exceeded
Solution: Check api_credit_tracking.json and wait for reset
```

#### **3. Network Connection Issues**
```
Error: Connection timeout
Solution: Check firewall settings and network connectivity
```

#### **4. Database Connection Failed**
```
Error: Database connection failed
Solution: Verify DATABASE_URL and database availability
```

### **🔧 Debug Commands**
```bash
# Test environment variables
env | grep -E "(ODDS_API_KEY|DATABASE_URL)"

# Test API connectivity
curl -I https://api.the-odds-api.com/v4/sports

# Check log files
tail -f automated_training_pipeline.log

# Verify database files
ls -la *.db
```

---

## 🎯 **DEPLOYMENT CHECKLIST**

### **✅ Pre-Deployment**
- [ ] API keys obtained and tested
- [ ] Environment variables configured
- [ ] Network connectivity verified
- [ ] Database setup completed
- [ ] Logs directory created
- [ ] Rate limits configured

### **✅ Post-Deployment**
- [ ] Health checks passing
- [ ] API calls working
- [ ] Data collection active
- [ ] Monitoring enabled
- [ ] Backups configured
- [ ] Alerts set up

---

## 📞 **SUPPORT & RESOURCES**

### **🔗 API Documentation**
- **The Odds API:** https://the-odds-api.com/liveapi/guides/v4/
- **NBA Stats API:** https://github.com/swar/nba_api

### **🛠️ Tools & Utilities**
- **Environment Testing:** `test_api_connections.py`
- **Usage Monitoring:** `api_credit_tracking.json`
- **Health Checks:** `hybrid_prediction_server.py`

---

**🏆 READY FOR DEPLOYMENT:** All API access requirements documented and secured!
