#!/usr/bin/env python3
"""
🎯 EXPERT ODDS API SYSTEM
========================

SMART ODDS API INTEGRATION with:
- ✅ API Call Tracking & Rate Limiting
- ✅ Intelligent Caching System
- ✅ Fresh Odds Collection
- ✅ Expert Integration Across Codebase
- ✅ Minimal API Usage Strategy

ONLY for ODDS - Rosters from WNBA.com/ESPN
"""

import requests
import json
import sqlite3
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
from pathlib import Path
import hashlib

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ExpertOddsAPISystem:
    """Expert Odds API System with smart caching and call tracking"""
    
    def __init__(self, api_key: str, cache_duration_minutes: int = 30):
        """
        Initialize Expert Odds API System
        
        Args:
            api_key: Odds API key
            cache_duration_minutes: How long to cache odds (default 30 min)
        """
        self.api_key = api_key
        self.base_url = "https://api.the-odds-api.com/v4"
        self.sport = "basketball_wnba"
        self.cache_duration = timedelta(minutes=cache_duration_minutes)
        
        # Initialize database for caching and tracking
        self.db_path = "expert_odds_cache.db"
        self._init_database()
        
        # API call tracking
        self.daily_limit = 500  # Adjust based on your plan
        self.session = requests.Session()
        
        logger.info(f"🎯 Expert Odds API System initialized")
        logger.info(f"   Cache Duration: {cache_duration_minutes} minutes")
        logger.info(f"   Daily Limit: {self.daily_limit} calls")
    
    def _init_database(self):
        """Initialize SQLite database for caching and tracking"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Odds cache table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS odds_cache (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cache_key TEXT UNIQUE,
                odds_data TEXT,
                timestamp DATETIME,
                expires_at DATETIME,
                game_id TEXT,
                market_type TEXT
            )
        ''')
        
        # API call tracking table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS api_calls (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME,
                endpoint TEXT,
                response_code INTEGER,
                requests_used INTEGER,
                requests_remaining INTEGER,
                cache_hit BOOLEAN DEFAULT FALSE
            )
        ''')
        
        # Fresh odds tracking
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fresh_odds (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT,
                home_team TEXT,
                away_team TEXT,
                commence_time DATETIME,
                odds_data TEXT,
                last_updated DATETIME,
                bookmaker_count INTEGER
            )
        ''')
        
        conn.commit()
        conn.close()
        
        logger.info("✅ Expert Odds database initialized")
    
    def get_api_usage_today(self) -> Dict[str, Any]:
        """Get today's API usage statistics"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        today = datetime.now().date()
        cursor.execute('''
            SELECT COUNT(*) as total_calls,
                   SUM(CASE WHEN cache_hit = 0 THEN 1 ELSE 0 END) as api_calls,
                   SUM(CASE WHEN cache_hit = 1 THEN 1 ELSE 0 END) as cache_hits,
                   MAX(requests_remaining) as remaining
            FROM api_calls 
            WHERE DATE(timestamp) = ?
        ''', (today,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result and result[0]:
            return {
                'total_requests': result[0],
                'api_calls': result[1] or 0,
                'cache_hits': result[2] or 0,
                'remaining': result[3] or self.daily_limit,
                'cache_hit_rate': (result[2] or 0) / result[0] if result[0] > 0 else 0
            }
        
        return {
            'total_requests': 0,
            'api_calls': 0,
            'cache_hits': 0,
            'remaining': self.daily_limit,
            'cache_hit_rate': 0.0
        }
    
    def _generate_cache_key(self, endpoint: str, params: Dict[str, Any]) -> str:
        """Generate cache key for request"""
        
        # Sort params for consistent key generation
        sorted_params = sorted(params.items())
        key_string = f"{endpoint}_{sorted_params}"
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _get_cached_odds(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached odds if still valid"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT odds_data, expires_at 
            FROM odds_cache 
            WHERE cache_key = ? AND expires_at > ?
        ''', (cache_key, datetime.now()))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            logger.info(f"✅ Cache HIT for key: {cache_key[:8]}...")
            return json.loads(result[0])
        
        return None
    
    def _cache_odds(self, cache_key: str, odds_data: Dict[str, Any], 
                   game_id: str = None, market_type: str = None):
        """Cache odds data"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        expires_at = datetime.now() + self.cache_duration
        
        cursor.execute('''
            INSERT OR REPLACE INTO odds_cache 
            (cache_key, odds_data, timestamp, expires_at, game_id, market_type)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (cache_key, json.dumps(odds_data), datetime.now(), 
              expires_at, game_id, market_type))
        
        conn.commit()
        conn.close()
        
        logger.info(f"💾 Cached odds for key: {cache_key[:8]}... (expires: {expires_at.strftime('%H:%M')})")
    
    def _log_api_call(self, endpoint: str, response_code: int, 
                     requests_used: int = None, requests_remaining: int = None,
                     cache_hit: bool = False):
        """Log API call for tracking"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO api_calls 
            (timestamp, endpoint, response_code, requests_used, requests_remaining, cache_hit)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (datetime.now(), endpoint, response_code, 
              requests_used, requests_remaining, cache_hit))
        
        conn.commit()
        conn.close()
    
    def get_fresh_wnba_odds(self, markets: List[str] = None) -> Dict[str, Any]:
        """
        Get fresh WNBA odds with smart caching
        
        Args:
            markets: List of markets to fetch (default: ['h2h', 'spreads', 'totals'])
        
        Returns:
            Dict with odds data and metadata
        """
        
        if markets is None:
            markets = ['h2h', 'spreads', 'totals']
        
        # Check API usage
        usage = self.get_api_usage_today()
        if usage['remaining'] <= 10:  # Keep 10 calls as buffer
            logger.warning(f"⚠️ Low API calls remaining: {usage['remaining']}")
            return self._get_cached_odds_fallback()
        
        all_odds = {}
        
        for market in markets:
            # Generate cache key
            params = {
                'apiKey': self.api_key,
                'regions': 'us',
                'markets': market,
                'oddsFormat': 'decimal',
                'dateFormat': 'iso'
            }
            
            cache_key = self._generate_cache_key(f"odds_{market}", params)
            
            # Try cache first
            cached_data = self._get_cached_odds(cache_key)
            if cached_data:
                all_odds[market] = cached_data
                self._log_api_call(f"odds_{market}", 200, cache_hit=True)
                continue
            
            # Make API call
            url = f"{self.base_url}/sports/{self.sport}/odds"
            
            try:
                logger.info(f"🌐 Fetching fresh {market} odds from API...")
                response = self.session.get(url, params=params)
                
                # Extract usage info from headers
                requests_used = response.headers.get('x-requests-used')
                requests_remaining = response.headers.get('x-requests-remaining')
                
                if response.status_code == 200:
                    odds_data = response.json()
                    all_odds[market] = odds_data
                    
                    # Cache the data
                    self._cache_odds(cache_key, odds_data, market_type=market)
                    
                    # Log successful call
                    self._log_api_call(f"odds_{market}", 200, 
                                     requests_used, requests_remaining, False)
                    
                    logger.info(f"✅ Fresh {market} odds fetched - {len(odds_data)} games")
                    logger.info(f"📊 API Usage: {requests_used} used, {requests_remaining} remaining")
                    
                else:
                    logger.error(f"❌ API call failed for {market}: {response.status_code}")
                    self._log_api_call(f"odds_{market}", response.status_code)
                    
            except Exception as e:
                logger.error(f"❌ Error fetching {market} odds: {e}")
                self._log_api_call(f"odds_{market}", 500)
        
        # Store fresh odds in database
        self._store_fresh_odds(all_odds)
        
        return {
            'odds': all_odds,
            'timestamp': datetime.now().isoformat(),
            'markets': markets,
            'games_count': sum(len(odds) for odds in all_odds.values()),
            'api_usage': self.get_api_usage_today()
        }
    
    def _get_cached_odds_fallback(self) -> Dict[str, Any]:
        """Get most recent cached odds when API limit reached"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT market_type, odds_data, timestamp
            FROM odds_cache 
            WHERE timestamp > ?
            ORDER BY timestamp DESC
        ''', (datetime.now() - timedelta(hours=6),))  # Last 6 hours
        
        results = cursor.fetchall()
        conn.close()
        
        cached_odds = {}
        for market_type, odds_data, timestamp in results:
            if market_type not in cached_odds:
                cached_odds[market_type] = json.loads(odds_data)
        
        logger.info(f"📦 Using cached odds fallback - {len(cached_odds)} markets")
        
        return {
            'odds': cached_odds,
            'timestamp': datetime.now().isoformat(),
            'markets': list(cached_odds.keys()),
            'games_count': sum(len(odds) for odds in cached_odds.values()),
            'api_usage': self.get_api_usage_today(),
            'cache_fallback': True
        }
    
    def _store_fresh_odds(self, all_odds: Dict[str, Any]):
        """Store fresh odds in database for analysis"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Process each market's games
        for market, odds_data in all_odds.items():
            for game in odds_data:
                game_id = game.get('id')
                home_team = game.get('home_team')
                away_team = game.get('away_team')
                commence_time = game.get('commence_time')
                bookmaker_count = len(game.get('bookmakers', []))
                
                cursor.execute('''
                    INSERT OR REPLACE INTO fresh_odds
                    (game_id, home_team, away_team, commence_time, 
                     odds_data, last_updated, bookmaker_count)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (game_id, home_team, away_team, commence_time,
                      json.dumps(game), datetime.now(), bookmaker_count))
        
        conn.commit()
        conn.close()
        
        logger.info(f"💾 Stored fresh odds for analysis")

    def get_game_odds(self, home_team: str, away_team: str,
                     market: str = 'h2h') -> Optional[Dict[str, Any]]:
        """Get odds for specific game"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT odds_data FROM fresh_odds
            WHERE (home_team = ? AND away_team = ?) OR
                  (home_team = ? AND away_team = ?)
            ORDER BY last_updated DESC LIMIT 1
        ''', (home_team, away_team, away_team, home_team))

        result = cursor.fetchone()
        conn.close()

        if result:
            game_data = json.loads(result[0])
            return self._extract_market_odds(game_data, market)

        return None

    def get_player_prop_odds(self, player_name: str,
                           prop_type: str = 'player_points') -> List[Dict[str, Any]]:
        """Get player prop odds (if available)"""

        # Note: Player props require different API endpoints
        # This is a placeholder for future implementation
        logger.info(f"🎯 Player prop odds for {player_name} ({prop_type}) - Coming soon")
        return []

    def _extract_market_odds(self, game_data: Dict[str, Any],
                           market: str) -> Dict[str, Any]:
        """Extract specific market odds from game data"""

        extracted_odds = {
            'game_id': game_data.get('id'),
            'home_team': game_data.get('home_team'),
            'away_team': game_data.get('away_team'),
            'commence_time': game_data.get('commence_time'),
            'market': market,
            'bookmakers': []
        }

        for bookmaker in game_data.get('bookmakers', []):
            for market_data in bookmaker.get('markets', []):
                if market_data.get('key') == market:
                    extracted_odds['bookmakers'].append({
                        'name': bookmaker.get('title'),
                        'outcomes': market_data.get('outcomes', [])
                    })

        return extracted_odds

    def get_odds_summary(self) -> Dict[str, Any]:
        """Get comprehensive odds summary"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Get recent games count
        cursor.execute('''
            SELECT COUNT(DISTINCT game_id) as games,
                   COUNT(*) as total_records,
                   MAX(last_updated) as last_update
            FROM fresh_odds
            WHERE last_updated > ?
        ''', (datetime.now() - timedelta(hours=24),))

        games_info = cursor.fetchone()

        # Get API usage
        usage = self.get_api_usage_today()

        # Get cache stats
        cursor.execute('''
            SELECT COUNT(*) as cached_items,
                   COUNT(CASE WHEN expires_at > ? THEN 1 END) as valid_cache
            FROM odds_cache
        ''', (datetime.now(),))

        cache_info = cursor.fetchone()

        conn.close()

        return {
            'games_tracked': games_info[0] if games_info else 0,
            'total_odds_records': games_info[1] if games_info else 0,
            'last_update': games_info[2] if games_info else None,
            'api_usage': usage,
            'cache_stats': {
                'total_items': cache_info[0] if cache_info else 0,
                'valid_items': cache_info[1] if cache_info else 0
            }
        }

    def cleanup_old_data(self, days_to_keep: int = 7):
        """Clean up old cached data"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cutoff_date = datetime.now() - timedelta(days=days_to_keep)

        # Clean old cache
        cursor.execute('DELETE FROM odds_cache WHERE timestamp < ?', (cutoff_date,))
        cache_deleted = cursor.rowcount

        # Clean old API calls
        cursor.execute('DELETE FROM api_calls WHERE timestamp < ?', (cutoff_date,))
        calls_deleted = cursor.rowcount

        # Clean old odds
        cursor.execute('DELETE FROM fresh_odds WHERE last_updated < ?', (cutoff_date,))
        odds_deleted = cursor.rowcount

        conn.commit()
        conn.close()

        logger.info(f"🧹 Cleanup complete:")
        logger.info(f"   Cache records deleted: {cache_deleted}")
        logger.info(f"   API call records deleted: {calls_deleted}")
        logger.info(f"   Odds records deleted: {odds_deleted}")


def get_expert_odds_integration() -> ExpertOddsAPISystem:
    """Get singleton instance of Expert Odds API System"""

    # 🔒 SECURITY FIX: Load API key from environment variable
    import os
    API_KEY = os.getenv('ODDS_API_KEY')

    if not API_KEY:
        logger.warning("⚠️ ODDS_API_KEY environment variable not set - using demo mode")
        API_KEY = "demo_key_for_testing"  # Demo key for testing

    if not hasattr(get_expert_odds_integration, '_instance'):
        get_expert_odds_integration._instance = ExpertOddsAPISystem(
            api_key=API_KEY,
            cache_duration_minutes=30  # 30-minute cache
        )

    return get_expert_odds_integration._instance


def demonstrate_expert_odds_system():
    """Demonstrate the Expert Odds API System"""

    print("🎯 EXPERT ODDS API SYSTEM DEMONSTRATION")
    print("=" * 60)

    # Initialize system
    odds_system = get_expert_odds_integration()

    # Show current usage
    usage = odds_system.get_api_usage_today()
    print(f"📊 Current API Usage:")
    print(f"   Total Requests: {usage['total_requests']}")
    print(f"   API Calls: {usage['api_calls']}")
    print(f"   Cache Hits: {usage['cache_hits']}")
    print(f"   Cache Hit Rate: {usage['cache_hit_rate']:.1%}")
    print(f"   Remaining: {usage['remaining']}")

    # Get fresh odds
    print(f"\n🌐 Fetching Fresh WNBA Odds...")
    fresh_odds = odds_system.get_fresh_wnba_odds(['h2h', 'spreads'])

    print(f"✅ Fresh odds retrieved:")
    print(f"   Markets: {fresh_odds['markets']}")
    print(f"   Games: {fresh_odds['games_count']}")
    print(f"   Timestamp: {fresh_odds['timestamp']}")

    # Show odds summary
    summary = odds_system.get_odds_summary()
    print(f"\n📈 Odds Summary:")
    print(f"   Games Tracked: {summary['games_tracked']}")
    print(f"   Total Records: {summary['total_odds_records']}")
    print(f"   Cache Items: {summary['cache_stats']['valid_items']}")

    print(f"\n🎉 Expert Odds API System working perfectly!")
    print(f"✅ Smart caching minimizes API calls")
    print(f"✅ Fresh odds available for predictions")
    print(f"✅ Ready for codebase integration")


if __name__ == "__main__":
    demonstrate_expert_odds_system()
