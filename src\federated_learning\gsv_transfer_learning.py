#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GSV TRANSFER LEARNING FOR FEDERATED TRAINING
============================================

Solves the critical GSV data imbalance problem in federated learning:
- GSV: 273 records (expansion team 2025)
- Other teams: 2,300+ records each
- Imbalance ratio: 1:9.6

Solution: Transfer learning from similar teams to bootstrap GSV model
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from typing import Dict, List, Tuple, Optional, Any
import logging
from pathlib import Path

# Fix Windows encoding
if os.name == 'nt':
    if hasattr(sys.stdout, 'reconfigure'):
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    if hasattr(sys.stderr, 'reconfigure'):
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')

logger = logging.getLogger(__name__)

class GSVTransferLearning:
    """
    Transfer Learning System for GSV (Golden State Valkyries)
    
    Addresses critical federated learning imbalance:
    - Identifies similar teams for knowledge transfer
    - Creates synthetic GSV training data
    - Enables GSV participation in federated learning
    - Maintains privacy guarantees
    """
    
    def __init__(self, similarity_threshold: float = 0.8):
        """Initialize GSV transfer learning system"""
        self.similarity_threshold = similarity_threshold
        self.similar_teams = []
        self.transfer_weights = {}
        self.gsv_synthetic_data = None
        
        # Team similarity profiles (based on playing style, market size, etc.)
        self.team_profiles = {
            'GSV': {'market_size': 'large', 'style': 'fast_pace', 'region': 'west'},
            'SEA': {'market_size': 'medium', 'style': 'fast_pace', 'region': 'west'},
            'LAS': {'market_size': 'large', 'style': 'fast_pace', 'region': 'west'},
            'PHO': {'market_size': 'large', 'style': 'fast_pace', 'region': 'west'},
            'LV': {'market_size': 'medium', 'style': 'balanced', 'region': 'west'},
            'MIN': {'market_size': 'medium', 'style': 'balanced', 'region': 'west'},
            'DAL': {'market_size': 'large', 'style': 'balanced', 'region': 'west'},
            'ATL': {'market_size': 'large', 'style': 'fast_pace', 'region': 'east'},
            'CHI': {'market_size': 'large', 'style': 'balanced', 'region': 'east'},
            'CON': {'market_size': 'small', 'style': 'defensive', 'region': 'east'},
            'IND': {'market_size': 'medium', 'style': 'balanced', 'region': 'east'},
            'NYL': {'market_size': 'large', 'style': 'fast_pace', 'region': 'east'},
            'WAS': {'market_size': 'large', 'style': 'balanced', 'region': 'east'}
        }
        
        logger.info("GSV Transfer Learning System initialized")
    
    def identify_similar_teams(self) -> List[str]:
        """Identify teams most similar to GSV for transfer learning"""
        
        gsv_profile = self.team_profiles['GSV']
        similarities = {}
        
        for team, profile in self.team_profiles.items():
            if team == 'GSV':
                continue
                
            # Calculate similarity score
            similarity = 0.0
            
            # Market size similarity (40% weight)
            if profile['market_size'] == gsv_profile['market_size']:
                similarity += 0.4
            elif abs(['small', 'medium', 'large'].index(profile['market_size']) - 
                     ['small', 'medium', 'large'].index(gsv_profile['market_size'])) == 1:
                similarity += 0.2
            
            # Playing style similarity (40% weight)
            if profile['style'] == gsv_profile['style']:
                similarity += 0.4
            elif profile['style'] == 'balanced':  # Balanced style is somewhat similar to all
                similarity += 0.2
            
            # Regional similarity (20% weight)
            if profile['region'] == gsv_profile['region']:
                similarity += 0.2
            
            similarities[team] = similarity
        
        # Select teams above similarity threshold
        self.similar_teams = [team for team, sim in similarities.items() 
                             if sim >= self.similarity_threshold]
        
        # If no teams meet threshold, select top 3 most similar
        if not self.similar_teams:
            sorted_teams = sorted(similarities.items(), key=lambda x: x[1], reverse=True)
            self.similar_teams = [team for team, _ in sorted_teams[:3]]
        
        # Calculate transfer weights (higher similarity = higher weight)
        total_similarity = sum(similarities[team] for team in self.similar_teams)
        self.transfer_weights = {
            team: similarities[team] / total_similarity 
            for team in self.similar_teams
        }
        
        logger.info(f"Similar teams identified: {self.similar_teams}")
        logger.info(f"Transfer weights: {self.transfer_weights}")
        
        return self.similar_teams
    
    def create_synthetic_gsv_data(self, team_data: Dict[str, pd.DataFrame], 
                                 target_samples: int = 1500) -> pd.DataFrame:
        """Create synthetic GSV training data from similar teams"""
        
        if not self.similar_teams:
            self.identify_similar_teams()
        
        synthetic_data_parts = []
        
        for team in self.similar_teams:
            if team not in team_data:
                logger.warning(f"Team {team} data not available for transfer")
                continue
            
            team_df = team_data[team].copy()
            weight = self.transfer_weights[team]
            samples_from_team = int(target_samples * weight)
            
            # Sample data from this team
            if len(team_df) >= samples_from_team:
                sampled_data = team_df.sample(n=samples_from_team, random_state=42)
            else:
                # Oversample if team has insufficient data
                sampled_data = team_df.sample(n=samples_from_team, replace=True, random_state=42)
            
            # Add noise to create variation (privacy-preserving)
            numeric_cols = sampled_data.select_dtypes(include=[np.number]).columns
            noise_scale = 0.05  # 5% noise
            
            for col in numeric_cols:
                if col != 'target':  # Don't add noise to target variable
                    noise = np.random.normal(0, sampled_data[col].std() * noise_scale, len(sampled_data))
                    sampled_data[col] = sampled_data[col] + noise
            
            # Mark as synthetic GSV data
            sampled_data['team_abbrev'] = 'GSV'
            sampled_data['data_source'] = f'transfer_from_{team}'
            sampled_data['is_synthetic'] = True
            
            synthetic_data_parts.append(sampled_data)
        
        # Combine all synthetic data
        self.gsv_synthetic_data = pd.concat(synthetic_data_parts, ignore_index=True)
        
        logger.info(f"Created {len(self.gsv_synthetic_data)} synthetic GSV samples")
        logger.info(f"Source distribution: {dict(self.gsv_synthetic_data['data_source'].value_counts())}")
        
        return self.gsv_synthetic_data
    
    def prepare_gsv_for_federation(self, team_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Prepare GSV data for federated learning participation"""
        
        # Create synthetic training data
        synthetic_data = self.create_synthetic_gsv_data(team_data)
        
        # Combine with real GSV test data (if available)
        if 'GSV' in team_data and len(team_data['GSV']) > 0:
            real_gsv_data = team_data['GSV'].copy()
            real_gsv_data['is_synthetic'] = False
            
            # Combine synthetic and real data
            combined_gsv_data = pd.concat([synthetic_data, real_gsv_data], ignore_index=True)
        else:
            combined_gsv_data = synthetic_data
        
        # Update team data with enhanced GSV dataset
        enhanced_team_data = team_data.copy()
        enhanced_team_data['GSV'] = combined_gsv_data
        
        logger.info(f"GSV prepared for federation: {len(combined_gsv_data)} total samples")
        logger.info(f"Synthetic: {sum(combined_gsv_data['is_synthetic'])}, Real: {sum(~combined_gsv_data['is_synthetic'])}")
        
        return enhanced_team_data
    
    def validate_transfer_quality(self, original_gsv_data: pd.DataFrame, 
                                 synthetic_gsv_data: pd.DataFrame) -> Dict[str, float]:
        """Validate quality of transfer learning"""
        
        if len(original_gsv_data) == 0:
            logger.warning("No original GSV data for validation")
            return {'transfer_quality': 0.0}
        
        # Compare statistical properties
        numeric_cols = original_gsv_data.select_dtypes(include=[np.number]).columns
        
        mean_similarity = []
        std_similarity = []
        
        for col in numeric_cols:
            if col in synthetic_gsv_data.columns:
                orig_mean = original_gsv_data[col].mean()
                synth_mean = synthetic_gsv_data[col].mean()
                
                orig_std = original_gsv_data[col].std()
                synth_std = synthetic_gsv_data[col].std()
                
                # Calculate similarity (1 - normalized difference)
                mean_sim = 1 - abs(orig_mean - synth_mean) / (abs(orig_mean) + 1e-8)
                std_sim = 1 - abs(orig_std - synth_std) / (abs(orig_std) + 1e-8)
                
                mean_similarity.append(max(0, mean_sim))
                std_similarity.append(max(0, std_sim))
        
        validation_metrics = {
            'mean_similarity': np.mean(mean_similarity) if mean_similarity else 0.0,
            'std_similarity': np.mean(std_similarity) if std_similarity else 0.0,
            'transfer_quality': np.mean([np.mean(mean_similarity), np.mean(std_similarity)]) if mean_similarity and std_similarity else 0.0,
            'synthetic_samples': len(synthetic_gsv_data),
            'real_samples': len(original_gsv_data)
        }
        
        logger.info(f"Transfer validation: {validation_metrics}")
        
        return validation_metrics

class FederatedGSVIntegration:
    """Integration layer for GSV transfer learning in federated training"""

    def __init__(self):
        self.gsv_transfer = GSVTransferLearning(similarity_threshold=0.6)
        self.integration_metrics = {}

    def prepare_federated_training_with_gsv(self, team_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Prepare all teams including GSV for federated training"""

        logger.info("Preparing federated training with GSV integration")

        # Check GSV data availability
        gsv_original_size = len(team_data.get('GSV', pd.DataFrame()))

        if gsv_original_size < 500:  # Insufficient data threshold
            logger.warning(f"GSV has insufficient data ({gsv_original_size} samples), applying transfer learning")

            # Apply transfer learning
            enhanced_team_data = self.gsv_transfer.prepare_gsv_for_federation(team_data)

            # Validate transfer quality
            if gsv_original_size > 0:
                validation_metrics = self.gsv_transfer.validate_transfer_quality(
                    team_data['GSV'],
                    enhanced_team_data['GSV'][enhanced_team_data['GSV']['is_synthetic']]
                )
                self.integration_metrics['transfer_validation'] = validation_metrics

            logger.info("GSV transfer learning applied successfully")
            return enhanced_team_data

        else:
            logger.info("GSV has sufficient data, no transfer learning needed")
            return team_data

    def get_federation_status(self) -> Dict[str, Any]:
        """Get status of GSV federation integration"""

        return {
            'gsv_transfer_applied': bool(self.gsv_transfer.gsv_synthetic_data is not None),
            'similar_teams': self.gsv_transfer.similar_teams,
            'transfer_weights': self.gsv_transfer.transfer_weights,
            'integration_metrics': self.integration_metrics,
            'federation_ready': True
        }

def main():
    """Test GSV transfer learning system"""

    print("GSV TRANSFER LEARNING SYSTEM TEST")
    print("=" * 50)

    # Initialize transfer learning
    gsv_transfer = GSVTransferLearning(similarity_threshold=0.6)

    # Identify similar teams
    similar_teams = gsv_transfer.identify_similar_teams()
    print(f"Similar teams to GSV: {similar_teams}")
    print(f"Transfer weights: {gsv_transfer.transfer_weights}")

    # Test integration
    integration = FederatedGSVIntegration()
    status = integration.get_federation_status()
    print(f"Federation status: {status}")

    print("\nGSV Transfer Learning System ready for federated training!")

if __name__ == "__main__":
    main()
