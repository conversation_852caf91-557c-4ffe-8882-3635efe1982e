<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WNBA WAR ROOM - COMMAND CENTER</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            /* 2025 ESPN WNBA Broadcast Colors */
            --vampire-black: #050707;
            --quartz: #4C4C4D;
            --pearl: #EFE3C6;
            --princeton-orange: #F57B20;
            
            /* War Room Colors */
            --alert-red: #FF1744;
            --success-green: #00E676;
            --warning-amber: #FFD600;
            --info-blue: #00B0FF;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: var(--vampire-black);
            color: var(--pearl);
            font-family: 'Courier New', monospace;
            overflow: hidden;
            height: 100vh;
        }
        
        .war-room {
            display: grid;
            grid-template-columns: 300px 1fr 350px;
            grid-template-rows: 60px 1fr 200px;
            height: 100vh;
            gap: 2px;
            background: var(--vampire-black);
            grid-template-areas:
                "header header header"
                "games-list live-feed alerts"
                "props-tracker live-feed value-scanner";
        }
        
        .panel {
            background: rgba(76, 76, 77, 0.2);
            border: 1px solid var(--quartz);
            padding: 10px;
            overflow: hidden;
        }
        
        .header-panel {
            grid-area: header;
            background: linear-gradient(90deg, var(--vampire-black), var(--princeton-orange));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            border-bottom: 2px solid var(--princeton-orange);
        }
        
        .games-list {
            grid-area: games-list;
            overflow-y: auto;
        }
        
        .live-feed {
            grid-area: live-feed;
            display: flex;
            flex-direction: column;
        }
        
        .alerts-panel {
            grid-area: alerts;
            overflow-y: auto;
        }
        
        .props-tracker {
            grid-area: props-tracker;
            overflow-y: auto;
        }
        
        .value-scanner {
            grid-area: value-scanner;
            overflow-y: auto;
        }
        
        .game-card {
            background: rgba(5, 7, 7, 0.8);
            border: 1px solid var(--quartz);
            margin: 5px 0;
            padding: 10px;
            border-left: 4px solid var(--princeton-orange);
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .game-card:hover {
            border-left-color: var(--pearl);
            background: rgba(5, 7, 7, 1);
        }
        
        .game-card.live {
            border-left-color: var(--alert-red);
            animation: pulse 2s infinite;
        }
        
        .game-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .teams {
            font-weight: bold;
            font-size: 14px;
        }
        
        .game-time {
            color: var(--princeton-orange);
            font-size: 12px;
        }
        
        .score {
            display: flex;
            justify-content: space-between;
            font-size: 18px;
            font-weight: bold;
        }
        
        .quarter-info {
            text-align: center;
            color: var(--warning-amber);
            font-size: 12px;
            margin-top: 5px;
        }
        
        .live-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 10px;
            height: 100%;
        }
        
        .live-game {
            background: rgba(5, 7, 7, 0.9);
            border: 1px solid var(--alert-red);
            padding: 15px;
            position: relative;
        }
        
        .live-game::before {
            content: "LIVE";
            position: absolute;
            top: 5px;
            right: 5px;
            background: var(--alert-red);
            color: white;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 3px;
        }
        
        .alert-item {
            background: rgba(255, 23, 68, 0.1);
            border-left: 3px solid var(--alert-red);
            padding: 8px;
            margin: 3px 0;
            font-size: 12px;
        }
        
        .alert-item.warning {
            background: rgba(255, 214, 0, 0.1);
            border-left-color: var(--warning-amber);
        }
        
        .alert-item.success {
            background: rgba(0, 230, 118, 0.1);
            border-left-color: var(--success-green);
        }
        
        .prop-hit {
            display: flex;
            justify-content: space-between;
            padding: 5px 8px;
            margin: 2px 0;
            background: rgba(0, 230, 118, 0.2);
            border-radius: 3px;
            font-size: 11px;
        }
        
        .prop-miss {
            display: flex;
            justify-content: space-between;
            padding: 5px 8px;
            margin: 2px 0;
            background: rgba(255, 23, 68, 0.2);
            border-radius: 3px;
            font-size: 11px;
        }
        
        .value-bet {
            display: flex;
            justify-content: space-between;
            padding: 6px;
            margin: 2px 0;
            background: rgba(0, 230, 118, 0.15);
            border-radius: 3px;
            font-size: 11px;
            cursor: pointer;
        }
        
        .value-bet:hover {
            background: rgba(0, 230, 118, 0.3);
        }
        
        .edge {
            color: var(--success-green);
            font-weight: bold;
        }
        
        .status-bar {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        .live-dot { background: var(--alert-red); }
        .active-dot { background: var(--success-green); }
        .warning-dot { background: var(--warning-amber); }
        
        .control-btn {
            background: rgba(76, 76, 77, 0.5);
            border: 1px solid var(--princeton-orange);
            color: var(--pearl);
            padding: 5px 12px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
        }
        
        .control-btn:hover {
            background: var(--princeton-orange);
            color: var(--vampire-black);
        }
        
        .control-btn.active {
            background: var(--princeton-orange);
            color: var(--vampire-black);
        }
        
        h1 {
            font-size: 20px;
            color: var(--pearl);
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }
        
        h2 {
            font-size: 14px;
            color: var(--princeton-orange);
            margin-bottom: 10px;
            border-bottom: 1px solid var(--quartz);
            padding-bottom: 5px;
        }
        
        .timestamp {
            color: var(--quartz);
            font-size: 10px;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .scrollable {
            max-height: 100%;
            overflow-y: auto;
        }
        
        .scrollable::-webkit-scrollbar {
            width: 4px;
        }
        
        .scrollable::-webkit-scrollbar-track {
            background: var(--vampire-black);
        }
        
        .scrollable::-webkit-scrollbar-thumb {
            background: var(--quartz);
            border-radius: 2px;
        }
        
        .game-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px;
            margin-top: 5px;
            font-size: 10px;
        }
        
        .stat {
            text-align: center;
            padding: 2px;
            background: rgba(76, 76, 77, 0.3);
            border-radius: 2px;
        }
        
        .play-by-play {
            flex: 1;
            overflow-y: auto;
            font-size: 11px;
            line-height: 1.3;
        }
        
        .play-event {
            padding: 3px 5px;
            margin: 1px 0;
            border-left: 2px solid var(--quartz);
        }
        
        .play-event.score {
            border-left-color: var(--princeton-orange);
            background: rgba(245, 123, 32, 0.1);
        }
        
        .play-event.prop-trigger {
            border-left-color: var(--success-green);
            background: rgba(0, 230, 118, 0.1);
        }
    </style>
</head>
<body>
    <div class="war-room">
        <!-- HEADER COMMAND BAR -->
        <div class="panel header-panel">
            <div>
                <h1>🎯 WNBA WAR ROOM - COMMAND CENTER</h1>
                <div class="status-bar">
                    <div class="status-indicator">
                        <div class="status-dot live-dot"></div>
                        <span>4 LIVE GAMES</span>
                    </div>
                    <div class="status-indicator">
                        <div class="status-dot active-dot"></div>
                        <span>127 ACTIVE PROPS</span>
                    </div>
                    <div class="status-indicator">
                        <div class="status-dot warning-dot"></div>
                        <span>23 VALUE ALERTS</span>
                    </div>
                </div>
            </div>
            <div class="controls">
                <button class="control-btn active">AUTO-REFRESH</button>
                <button class="control-btn">PAUSE ALERTS</button>
                <button class="control-btn">EXPORT DATA</button>
                <button class="control-btn">EMERGENCY STOP</button>
            </div>
        </div>

        <!-- GAMES LIST PANEL -->
        <div class="panel games-list">
            <h2>📊 TODAY'S GAMES (4)</h2>
            <div class="scrollable">
                <div class="game-card live">
                    <div class="game-header">
                        <div class="teams">LV @ NYL</div>
                        <div class="game-time">LIVE Q3</div>
                    </div>
                    <div class="score">
                        <span>68</span>
                        <span>72</span>
                    </div>
                    <div class="quarter-info">Q3 6:24 | 2nd Half</div>
                    <div class="game-stats">
                        <div class="stat">Pace: 98.2</div>
                        <div class="stat">O/U: 162.5</div>
                        <div class="stat">Props: 32</div>
                        <div class="stat">Alerts: 8</div>
                    </div>
                </div>

                <div class="game-card live">
                    <div class="game-header">
                        <div class="teams">CON @ ATL</div>
                        <div class="game-time">LIVE Q2</div>
                    </div>
                    <div class="score">
                        <span>41</span>
                        <span>38</span>
                    </div>
                    <div class="quarter-info">Q2 3:15 | 1st Half</div>
                    <div class="game-stats">
                        <div class="stat">Pace: 94.8</div>
                        <div class="stat">O/U: 158.0</div>
                        <div class="stat">Props: 28</div>
                        <div class="stat">Alerts: 5</div>
                    </div>
                </div>

                <div class="game-card">
                    <div class="game-header">
                        <div class="teams">SEA @ PHO</div>
                        <div class="game-time">10:00 PM ET</div>
                    </div>
                    <div class="score">
                        <span>--</span>
                        <span>--</span>
                    </div>
                    <div class="quarter-info">Starting Soon</div>
                    <div class="game-stats">
                        <div class="stat">Spread: -3.5</div>
                        <div class="stat">O/U: 165.5</div>
                        <div class="stat">Props: 35</div>
                        <div class="stat">Ready: ✓</div>
                    </div>
                </div>

                <div class="game-card">
                    <div class="game-header">
                        <div class="teams">CHI @ MIN</div>
                        <div class="game-time">10:30 PM ET</div>
                    </div>
                    <div class="score">
                        <span>--</span>
                        <span>--</span>
                    </div>
                    <div class="quarter-info">Pregame</div>
                    <div class="game-stats">
                        <div class="stat">Spread: -6.0</div>
                        <div class="stat">O/U: 171.0</div>
                        <div class="stat">Props: 32</div>
                        <div class="stat">Ready: ✓</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- LIVE FEED CENTRAL -->
        <div class="panel live-feed">
            <h2>🔴 LIVE MULTI-GAME FEED</h2>
            <div class="live-grid">
                <div class="live-game">
                    <div style="font-weight: bold; margin-bottom: 10px;">LV @ NYL - Q3 6:24</div>
                    <div class="play-by-play">
                        <div class="play-event prop-trigger">A'ja Wilson 20th point (OVER 19.5 ✅)</div>
                        <div class="play-event score">Sabrina Ionescu 3-pointer</div>
                        <div class="play-event">Stewart defensive rebound</div>
                        <div class="play-event">Wilson assist to Gray</div>
                        <div class="play-event score">Plum driving layup</div>
                        <div class="play-event">Timeout called by Liberty</div>
                        <div class="play-event prop-trigger">Ionescu 4th assist (OVER 3.5 ✅)</div>
                    </div>
                </div>

                <div class="live-game">
                    <div style="font-weight: bold; margin-bottom: 10px;">CON @ ATL - Q2 3:15</div>
                    <div class="play-by-play">
                        <div class="play-event score">Alyssa Thomas layup</div>
                        <div class="play-event">Rhyne Howard 3-pt attempt (miss)</div>
                        <div class="play-event">Thomas defensive rebound</div>
                        <div class="play-event prop-trigger">Thomas 8th rebound (OVER 7.5 ✅)</div>
                        <div class="play-event score">Bonner 3-pointer</div>
                        <div class="play-event">Howard steal</div>
                        <div class="play-event">End of 2nd quarter</div>
                    </div>
                </div>

                <div class="live-game">
                    <div style="font-weight: bold; margin-bottom: 10px;">SEA @ PHO - Pregame</div>
                    <div class="play-by-play">
                        <div class="play-event">Lineups confirmed</div>
                        <div class="play-event">Jewell Loyd starting</div>
                        <div class="play-event">Diana Taurasi starting</div>
                        <div class="play-event">Warm-ups in progress</div>
                        <div class="play-event">Props lines locked</div>
                        <div class="play-event">Game starts in 15 min</div>
                    </div>
                </div>

                <div class="live-game">
                    <div style="font-weight: bold; margin-bottom: 10px;">CHI @ MIN - Pregame</div>
                    <div class="play-by-play">
                        <div class="play-event">Injury report updated</div>
                        <div class="play-event">Napheesa Collier probable</div>
                        <div class="play-event">Kahleah Copper starting</div>
                        <div class="play-event">Line movement detected</div>
                        <div class="play-event">O/U moved to 171.5</div>
                        <div class="play-event">Game starts in 45 min</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ALERTS PANEL -->
        <div class="panel alerts-panel">
            <h2>🚨 CRITICAL ALERTS</h2>
            <div class="scrollable">
                <div class="alert-item">
                    <strong>PROP HIT:</strong> A'ja Wilson OVER 19.5 points ✅
                    <div class="timestamp">6:24 Q3 - LV@NYL</div>
                </div>
                <div class="alert-item warning">
                    <strong>LINE MOVEMENT:</strong> Ionescu assists moved 3.5→4.0
                    <div class="timestamp">6:30 Q3 - LV@NYL</div>
                </div>
                <div class="alert-item success">
                    <strong>VALUE DETECTED:</strong> Thomas rebounds 7.5 (+15% edge)
                    <div class="timestamp">3:15 Q2 - CON@ATL</div>
                </div>
                <div class="alert-item">
                    <strong>PACE ALERT:</strong> LV@NYL pace up 12% from projection
                    <div class="timestamp">5:00 Q3 - LV@NYL</div>
                </div>
                <div class="alert-item warning">
                    <strong>INJURY UPDATE:</strong> Collier upgraded to probable
                    <div class="timestamp">Pregame - CHI@MIN</div>
                </div>
                <div class="alert-item success">
                    <strong>PROP HIT:</strong> Thomas OVER 7.5 rebounds ✅
                    <div class="timestamp">3:15 Q2 - CON@ATL</div>
                </div>
            </div>
        </div>

        <!-- PROPS TRACKER -->
        <div class="panel props-tracker">
            <h2>📈 PROPS TRACKER</h2>
            <div class="scrollable">
                <div class="prop-hit">
                    <span>Wilson Points O19.5</span>
                    <span>✅ HIT</span>
                </div>
                <div class="prop-hit">
                    <span>Thomas Reb O7.5</span>
                    <span>✅ HIT</span>
                </div>
                <div class="prop-hit">
                    <span>Ionescu Ast O3.5</span>
                    <span>✅ HIT</span>
                </div>
                <div class="prop-miss">
                    <span>Stewart Pts O20.5</span>
                    <span>❌ MISS</span>
                </div>
                <div style="color: var(--warning-amber); padding: 5px; font-size: 11px;">
                    <strong>PENDING (Live):</strong>
                </div>
                <div style="padding: 3px 8px; font-size: 10px; color: var(--quartz);">
                    • Plum Assists O4.5 (Currently: 3)
                    • Stewart Rebounds O8.5 (Currently: 6)
                    • Wilson Double-Double (20pts, 8reb)
                    • Game Total O162.5 (Currently: 140)
                </div>
            </div>
        </div>

        <!-- VALUE SCANNER -->
        <div class="panel value-scanner">
            <h2>💰 VALUE SCANNER</h2>
            <div class="scrollable">
                <div class="value-bet">
                    <div>
                        <div>Loyd Points O18.5</div>
                        <div style="font-size: 9px; color: var(--quartz);">SEA@PHO</div>
                    </div>
                    <div class="edge">+22%</div>
                </div>
                <div class="value-bet">
                    <div>
                        <div>Collier Reb O9.5</div>
                        <div style="font-size: 9px; color: var(--quartz);">CHI@MIN</div>
                    </div>
                    <div class="edge">+18%</div>
                </div>
                <div class="value-bet">
                    <div>
                        <div>Taurasi 3PM O2.5</div>
                        <div style="font-size: 9px; color: var(--quartz);">SEA@PHO</div>
                    </div>
                    <div class="edge">+15%</div>
                </div>
                <div class="value-bet">
                    <div>
                        <div>Copper Pts O16.5</div>
                        <div style="font-size: 9px; color: var(--quartz);">CHI@MIN</div>
                    </div>
                    <div class="edge">+12%</div>
                </div>
                <div class="value-bet">
                    <div>
                        <div>Game Total U171.5</div>
                        <div style="font-size: 9px; color: var(--quartz);">CHI@MIN</div>
                    </div>
                    <div class="edge">+8%</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class WNBAWarRoom {
            constructor() {
                this.isActive = true;
                this.updateInterval = null;
                this.gameData = new Map();
                this.propsData = new Map();
                this.alertsQueue = [];

                this.init();
            }

            init() {
                this.setupEventListeners();
                this.startWarRoomUpdates();
                this.initializeGameData();

                console.log('🎯 WNBA War Room Command Center ACTIVE');
            }

            setupEventListeners() {
                // Control buttons
                document.querySelectorAll('.control-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const action = e.target.textContent;
                        this.handleControlAction(action);
                    });
                });

                // Game card selection
                document.querySelectorAll('.game-card').forEach(card => {
                    card.addEventListener('click', (e) => {
                        this.selectGame(card);
                    });
                });

                // Value bet clicks
                document.querySelectorAll('.value-bet').forEach(bet => {
                    bet.addEventListener('click', (e) => {
                        this.highlightValueBet(bet);
                    });
                });
            }

            handleControlAction(action) {
                switch(action) {
                    case 'AUTO-REFRESH':
                        this.toggleAutoRefresh();
                        break;
                    case 'PAUSE ALERTS':
                        this.toggleAlerts();
                        break;
                    case 'EXPORT DATA':
                        this.exportWarRoomData();
                        break;
                    case 'EMERGENCY STOP':
                        this.emergencyStop();
                        break;
                }
            }

            toggleAutoRefresh() {
                this.isActive = !this.isActive;
                const btn = document.querySelector('.control-btn.active');

                if (this.isActive) {
                    btn.textContent = 'AUTO-REFRESH';
                    btn.classList.add('active');
                    this.startWarRoomUpdates();
                } else {
                    btn.textContent = 'PAUSED';
                    btn.classList.remove('active');
                    this.stopWarRoomUpdates();
                }
            }

            startWarRoomUpdates() {
                if (this.updateInterval) {
                    clearInterval(this.updateInterval);
                }

                this.updateInterval = setInterval(() => {
                    if (this.isActive) {
                        this.updateLiveGames();
                        this.updatePropsTracker();
                        this.updateValueScanner();
                        this.updateAlerts();
                        this.updatePlayByPlay();
                    }
                }, 2000); // Update every 2 seconds for war room intensity
            }

            stopWarRoomUpdates() {
                if (this.updateInterval) {
                    clearInterval(this.updateInterval);
                    this.updateInterval = null;
                }
            }

            updateLiveGames() {
                // Update live game scores and times
                const liveGames = document.querySelectorAll('.game-card.live');

                liveGames.forEach(game => {
                    const scores = game.querySelectorAll('.score span');
                    const quarterInfo = game.querySelector('.quarter-info');

                    // Simulate score updates
                    if (Math.random() > 0.7) { // 30% chance of score change
                        const homeScore = parseInt(scores[0].textContent) || 0;
                        const awayScore = parseInt(scores[1].textContent) || 0;

                        if (Math.random() > 0.5) {
                            scores[0].textContent = homeScore + (Math.random() > 0.7 ? 3 : 2);
                        } else {
                            scores[1].textContent = awayScore + (Math.random() > 0.7 ? 3 : 2);
                        }
                    }

                    // Update game time
                    if (Math.random() > 0.8) { // 20% chance of time update
                        const currentTime = quarterInfo.textContent;
                        if (currentTime.includes('Q3')) {
                            const minutes = Math.floor(Math.random() * 12);
                            const seconds = Math.floor(Math.random() * 60);
                            quarterInfo.textContent = `Q3 ${minutes}:${seconds.toString().padStart(2, '0')} | 2nd Half`;
                        }
                    }
                });
            }

            updatePropsTracker() {
                // Simulate new prop hits/misses
                if (Math.random() > 0.85) { // 15% chance of new prop result
                    const propsContainer = document.querySelector('.props-tracker .scrollable');
                    const isHit = Math.random() > 0.4; // 60% hit rate

                    const props = [
                        'Loyd Points O18.5',
                        'Collier Reb O9.5',
                        'Taurasi 3PM O2.5',
                        'Copper Ast O4.5',
                        'Howard Pts O15.5'
                    ];

                    const randomProp = props[Math.floor(Math.random() * props.length)];

                    const newProp = document.createElement('div');
                    newProp.className = isHit ? 'prop-hit' : 'prop-miss';
                    newProp.innerHTML = `
                        <span>${randomProp}</span>
                        <span>${isHit ? '✅ HIT' : '❌ MISS'}</span>
                    `;

                    propsContainer.insertBefore(newProp, propsContainer.children[3]);

                    // Add corresponding alert
                    this.addAlert(isHit ? 'success' : '', `PROP ${isHit ? 'HIT' : 'MISS'}: ${randomProp} ${isHit ? '✅' : '❌'}`);
                }
            }

            updateValueScanner() {
                // Update edge percentages
                document.querySelectorAll('.edge').forEach(edge => {
                    const currentEdge = parseInt(edge.textContent.replace('%', '').replace('+', ''));
                    const change = (Math.random() - 0.5) * 4; // ±2% change
                    const newEdge = Math.max(1, Math.min(30, currentEdge + change));
                    edge.textContent = `+${Math.round(newEdge)}%`;
                });
            }

            updateAlerts() {
                if (Math.random() > 0.9) { // 10% chance of new alert
                    const alertTypes = [
                        { type: '', message: 'PACE ALERT: Game pace increased 8%' },
                        { type: 'warning', message: 'LINE MOVEMENT: Spread moved 0.5 points' },
                        { type: 'success', message: 'VALUE DETECTED: New +20% edge opportunity' },
                        { type: '', message: 'INJURY UPDATE: Player status changed' }
                    ];

                    const randomAlert = alertTypes[Math.floor(Math.random() * alertTypes.length)];
                    this.addAlert(randomAlert.type, randomAlert.message);
                }
            }

            addAlert(type, message) {
                const alertsContainer = document.querySelector('.alerts-panel .scrollable');
                const newAlert = document.createElement('div');
                newAlert.className = `alert-item ${type}`;
                newAlert.innerHTML = `
                    <strong>${message}</strong>
                    <div class="timestamp">${new Date().toLocaleTimeString()}</div>
                `;

                alertsContainer.insertBefore(newAlert, alertsContainer.firstChild);

                // Keep only 10 most recent alerts
                while (alertsContainer.children.length > 10) {
                    alertsContainer.removeChild(alertsContainer.lastChild);
                }
            }

            updatePlayByPlay() {
                // Add new play-by-play events
                if (Math.random() > 0.8) { // 20% chance of new play
                    const liveGames = document.querySelectorAll('.live-game .play-by-play');

                    const events = [
                        { text: 'Defensive rebound', type: '' },
                        { text: 'Three-pointer made', type: 'score' },
                        { text: 'Assist recorded', type: '' },
                        { text: 'Steal by player', type: '' },
                        { text: 'Free throw made', type: 'score' },
                        { text: 'Timeout called', type: '' },
                        { text: 'Player reaches milestone', type: 'prop-trigger' }
                    ];

                    liveGames.forEach(playByPlay => {
                        if (Math.random() > 0.7) { // 30% chance per game
                            const randomEvent = events[Math.floor(Math.random() * events.length)];

                            const newPlay = document.createElement('div');
                            newPlay.className = `play-event ${randomEvent.type}`;
                            newPlay.textContent = randomEvent.text;

                            playByPlay.insertBefore(newPlay, playByPlay.firstChild);

                            // Keep only 8 most recent plays
                            while (playByPlay.children.length > 8) {
                                playByPlay.removeChild(playByPlay.lastChild);
                            }
                        }
                    });
                }
            }

            selectGame(gameCard) {
                // Highlight selected game
                document.querySelectorAll('.game-card').forEach(card => {
                    card.style.borderLeftColor = '#F57B20';
                });

                gameCard.style.borderLeftColor = '#EFE3C6';

                // Focus on selected game in live feed
                console.log('Selected game:', gameCard.querySelector('.teams').textContent);
            }

            highlightValueBet(betElement) {
                betElement.style.background = 'rgba(0, 230, 118, 0.5)';
                setTimeout(() => {
                    betElement.style.background = 'rgba(0, 230, 118, 0.15)';
                }, 1000);
            }

            emergencyStop() {
                this.isActive = false;
                this.stopWarRoomUpdates();

                // Flash red alert
                document.body.style.background = '#FF1744';
                setTimeout(() => {
                    document.body.style.background = '#050707';
                }, 200);

                console.log('🚨 EMERGENCY STOP ACTIVATED');
            }

            exportWarRoomData() {
                const data = {
                    timestamp: new Date().toISOString(),
                    games: Array.from(document.querySelectorAll('.game-card')).map(card => ({
                        teams: card.querySelector('.teams').textContent,
                        time: card.querySelector('.game-time').textContent,
                        score: card.querySelector('.score').textContent
                    })),
                    alerts: Array.from(document.querySelectorAll('.alert-item')).map(alert => alert.textContent),
                    props: Array.from(document.querySelectorAll('.prop-hit, .prop-miss')).map(prop => prop.textContent)
                };

                console.log('📊 War Room Data Export:', data);

                // In real implementation, this would download a file
                alert('War Room data exported to console');
            }

            initializeGameData() {
                // Initialize with current game states
                console.log('🏀 War Room initialized with 4 games');
                console.log('📊 127 active props being monitored');
                console.log('🚨 23 value alerts detected');
            }
        }

        // Initialize War Room when page loads
        document.addEventListener('DOMContentLoaded', () => {
            window.warRoom = new WNBAWarRoom();
        });

        // Performance monitoring
        window.addEventListener('load', () => {
            console.log('🎯 WNBA War Room Command Center ONLINE');
            console.log('⚡ Real-time monitoring ACTIVE');
            console.log('🔴 Multi-game tracking ENABLED');
        });
    </script>
</body>
</html>
