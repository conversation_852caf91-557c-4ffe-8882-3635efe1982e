#!/usr/bin/env python3
"""
🚀 HYBRID WNBA FRONT-END PREDICTION SERVER
==========================================

🎯 ULTIMATE WNBA PREDICTION COMMAND CENTER
- Centralized, Federated, and Multiverse Model Integration
- Real-time NBA API Data Integration
- Advanced Uncertainty Quantification
- Expert Mapping System Integration
- Live Performance Monitoring
- Professional-Grade Prediction Interface

Integrates ALL models: Enhanced, Hybrid GNN, MultiTask, Bayesian, Federated, and Multiverse.
"""

import os
import sys
import json
import torch
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import logging
import warnings
import threading
import time
import requests
from dataclasses import dataclass
warnings.filterwarnings('ignore')

# Add src to path for imports
sys.path.append('src')
sys.path.append('src/models')
sys.path.append('src/monitoring')

# Import our models
from models.modern_player_points_model import (
    PlayerPointsModel,
    HybridPlayerPointsModel,
    MultiTaskPlayerModel,
    BayesianPlayerModel,
    FederatedPlayerModel
)

# Import real data integration
try:
    from monitoring.real_wnba_data_integration import RealWNBADataIntegration
    from monitoring.live_nba_api_integration import LiveNBAAPIIntegration
except ImportError:
    RealWNBADataIntegration = None
    LiveNBAAPIIntegration = None

# Import federated multiverse integration
try:
    from federated_multiverse_integration import FederatedMultiverseClient, FederatedMultiverseStrategy
    from federated_multiverse_system import FederatedMultiverseServer
    import flwr as fl
    FEDERATED_MULTIVERSE_AVAILABLE = True
except ImportError:
    FederatedMultiverseClient = None
    FederatedMultiverseStrategy = None
    FederatedMultiverseServer = None
    fl = None
    FEDERATED_MULTIVERSE_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class PredictionRequest:
    """Structured prediction request"""
    mode: str
    features: List[float]
    player_name: str = "Unknown Player"
    team: str = "SEA"
    game_id: Optional[str] = None
    confidence_threshold: float = 0.7
    include_uncertainty: bool = True
    expert_context: Optional[Dict] = None

@dataclass
class PredictionResponse:
    """Structured prediction response"""
    prediction: float
    confidence: float
    uncertainty: float
    model_type: str
    expert_insights: Dict
    real_time_context: Dict
    prediction_id: str
    timestamp: str

class HybridPredictionServer:
    """🚀 ULTIMATE WNBA FRONT-END PREDICTION SERVER

    Features:
    - All Model Types: Centralized, Federated, Multiverse
    - Real-time NBA API Integration
    - Expert Mapping System
    - Advanced Uncertainty Quantification
    - Live Performance Monitoring
    - Professional Prediction Interface
    """

    def __init__(self):
        """Initialize the ultimate WNBA prediction server"""
        logger.info("🚀 Initializing HYBRID WNBA FRONT-END PREDICTION SERVER")

        self.app = Flask(__name__)
        CORS(self.app)

        # Core model storage
        self.models = {}
        self.feature_columns = None
        self.model_metadata = {}

        # Real-time data integration
        self.real_data = None
        self.live_api = None
        self._initialize_real_data()

        # Expert mapping system
        self.expert_mappings = self._load_expert_mappings()

        # Expert odds API system
        self.expert_odds = self._initialize_expert_odds()

        # Performance tracking
        self.prediction_history = []
        self.accuracy_metrics = {}
        self.live_performance = {}

        # Federated Multiverse Integration
        self.federated_multiverse = None
        self.learning_enabled = False
        self.model_updates_queue = []
        self.learning_metrics = {}
        self._initialize_federated_multiverse()

        # Load training results
        self.training_results = self._load_training_results()

        # Initialize all models
        self._load_all_models()

        # Setup advanced routes
        self._setup_routes()

        # Start background monitoring
        self._start_background_monitoring()

        # Start intelligent learning system
        self._start_intelligent_learning()

        logger.info("✅ HYBRID FRONT-END PREDICTION SERVER READY!")
        logger.info(f"🧠 Intelligent Learning: {'✅ ENABLED' if self.learning_enabled else '❌ DISABLED'}")

    def _initialize_real_data(self):
        """Initialize real-time WNBA data integration"""
        try:
            if RealWNBADataIntegration:
                self.real_data = RealWNBADataIntegration()
                logger.info("✅ Real WNBA data integration initialized")

            if LiveNBAAPIIntegration:
                self.live_api = LiveNBAAPIIntegration()
                logger.info("✅ Live NBA API integration initialized")

        except Exception as e:
            logger.warning(f"⚠️ Real data integration not available: {e}")

    def _load_expert_mappings(self) -> Dict:
        """Load expert mapping system"""
        try:
            expert_file = Path("config/expert_mappings.json")
            if expert_file.exists():
                with open(expert_file, 'r') as f:
                    mappings = json.load(f)
                logger.info("✅ Expert mappings loaded")
                return mappings
            else:
                logger.info("📊 Creating default expert mappings")
                return self._create_default_expert_mappings()
        except Exception as e:
            logger.error(f"❌ Error loading expert mappings: {e}")
            return self._create_default_expert_mappings()

    def _initialize_expert_odds(self):
        """Initialize Expert Odds API System"""
        try:
            from expert_odds_api_system import get_expert_odds_integration
            odds_system = get_expert_odds_integration()
            logger.info("✅ Expert Odds API System initialized")
            return odds_system
        except ImportError as e:
            logger.warning(f"⚠️ Expert Odds API System not available: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Error initializing Expert Odds API: {e}")
            return None

    def _create_default_expert_mappings(self) -> Dict:
        """Create default expert mapping system"""
        return {
            "player_tiers": {
                "elite": {"min_ppg": 18, "confidence_boost": 0.1},
                "rotation": {"min_ppg": 8, "confidence_boost": 0.05},
                "bench": {"min_ppg": 0, "confidence_boost": 0.0}
            },
            "team_contexts": {
                "SEA": {"offensive_rating": 108.5, "pace": 82.1},
                "LV": {"offensive_rating": 106.2, "pace": 81.8},
                "NYL": {"offensive_rating": 105.8, "pace": 80.9}
            },
            "game_situations": {
                "blowout": {"confidence_penalty": 0.15},
                "close_game": {"confidence_boost": 0.1},
                "overtime": {"uncertainty_increase": 0.2}
            }
        }

    def _initialize_federated_multiverse(self):
        """Initialize federated multiverse learning system"""
        try:
            if FEDERATED_MULTIVERSE_AVAILABLE:
                # Initialize federated multiverse client for continuous learning
                self.federated_multiverse = FederatedMultiverseClient(
                    team_id="HYBRID_SERVER"
                )
                self.learning_enabled = True
                logger.info("🧠 Federated Multiverse learning system initialized")
                logger.info("🌐 Connected to distributed intelligence network")
            else:
                logger.warning("⚠️ Federated Multiverse not available - learning disabled")

        except Exception as e:
            logger.error(f"❌ Federated Multiverse initialization error: {e}")
            self.learning_enabled = False

    def _start_background_monitoring(self):
        """Start background performance monitoring"""
        def monitor_performance():
            while True:
                try:
                    self._update_live_performance()
                    time.sleep(60)  # Update every minute
                except Exception as e:
                    logger.error(f"❌ Background monitoring error: {e}")
                    time.sleep(300)  # Wait 5 minutes on error

        monitor_thread = threading.Thread(target=monitor_performance, daemon=True)
        monitor_thread.start()
        logger.info("🔄 Background performance monitoring started")

    def _start_intelligent_learning(self):
        """Start intelligent learning system for continuous model improvement"""
        if not self.learning_enabled:
            logger.info("🧠 Intelligent learning disabled - no federated multiverse")
            return

        def intelligent_learning_loop():
            while True:
                try:
                    # Process model updates every 5 minutes
                    self._process_intelligent_updates()
                    time.sleep(300)  # 5 minutes
                except Exception as e:
                    logger.error(f"❌ Intelligent learning error: {e}")
                    time.sleep(600)  # Wait 10 minutes on error

        learning_thread = threading.Thread(target=intelligent_learning_loop, daemon=True)
        learning_thread.start()
        logger.info("🧠 Intelligent learning system started")
        logger.info("🌐 Models will continuously learn from federated multiverse")

    def _update_live_performance(self):
        """Update live performance metrics"""
        try:
            # Calculate recent accuracy
            recent_predictions = self.prediction_history[-100:]  # Last 100 predictions
            if recent_predictions:
                accuracies = [p.get('accuracy', 0) for p in recent_predictions if 'accuracy' in p]
                if accuracies:
                    self.live_performance['recent_accuracy'] = np.mean(accuracies)
                    self.live_performance['prediction_count'] = len(recent_predictions)
                    self.live_performance['last_update'] = datetime.now().isoformat()

        except Exception as e:
            logger.error(f"❌ Live performance update error: {e}")
        
        # Prediction tracking
        self.prediction_history = []
        self.accuracy_metrics = {
            'enhanced': {'predictions': 0, 'total_error': 0, 'mae': 0},
            'hybrid_gnn': {'predictions': 0, 'total_error': 0, 'mae': 0},
            'multitask': {'predictions': 0, 'total_error': 0, 'mae': 0},
            'bayesian': {'predictions': 0, 'total_error': 0, 'mae': 0},
            'federated': {'predictions': 0, 'total_error': 0, 'mae': 0},
            'multiverse': {'predictions': 0, 'total_error': 0, 'mae': 0}
        }
        
        logger.info("🚀 Hybrid Prediction Server initialized successfully!")
    
    def _load_training_results(self) -> Dict:
        """Load the comprehensive training results"""
        try:
            results_path = Path("models/comprehensive_system/comprehensive_training_results.json")
            if results_path.exists():
                with open(results_path, 'r') as f:
                    return json.load(f)
            else:
                logger.warning("⚠️ Training results not found")
                return {}
        except Exception as e:
            logger.error(f"❌ Error loading training results: {e}")
            return {}
    
    def _load_all_models(self):
        """Load all trained models"""
        logger.info("📦 Loading all trained models...")
        
        if not self.training_results:
            logger.error("❌ No training results available")
            return
        
        # Load feature columns from training data
        self._load_feature_columns()
        
        # Load each model type
        self._load_enhanced_model()
        self._load_hybrid_gnn_model()
        self._load_multitask_model()
        self._load_bayesian_model()
        self._load_federated_models()
        self._load_multiverse_ensemble()
        
        logger.info(f"✅ Loaded {len(self.models)} model types successfully!")
    
    def _load_feature_columns(self):
        """Load feature columns from training data"""
        try:
            # Load the master dataset to get feature columns
            data_path = "data/master/wnba_definitive_master_dataset_FIXED.csv"
            if Path(data_path).exists():
                df = pd.read_csv(data_path, nrows=1)  # Just get column names
                
                # Exclude non-feature columns
                exclude_cols = {
                    'points', 'target', 'player_id', 'game_id', 'game_date', 
                    'player_name', 'team', 'opponent', 'season'
                }
                
                self.feature_columns = [col for col in df.columns if col not in exclude_cols]
                logger.info(f"✅ Loaded {len(self.feature_columns)} feature columns")
            else:
                logger.error("❌ Master dataset not found")
                self.feature_columns = [f"feature_{i}" for i in range(180)]  # Default
                
        except Exception as e:
            logger.error(f"❌ Error loading feature columns: {e}")
            self.feature_columns = [f"feature_{i}" for i in range(180)]  # Default
    
    def _load_model_with_dimension_compatibility(self, model_class, model_path, model_name, **model_kwargs):
        """Universal model loading with dimension compatibility"""
        try:
            # Load checkpoint first to get the correct dimensions
            checkpoint = torch.load(model_path, map_location='cpu')

            # Get the actual input dimension from the saved model
            if 'state_dict' in checkpoint:
                first_layer_weight = None
                for key in checkpoint['state_dict'].keys():
                    if 'weight' in key and any(layer in key for layer in ['feature_net.0', 'net.0', 'fc1', 'bayesian_net.0']):
                        first_layer_weight = checkpoint['state_dict'][key]
                        break

                if first_layer_weight is not None:
                    actual_input_dim = first_layer_weight.shape[1]
                    logger.info(f"🔧 {model_name} actual input dimension: {actual_input_dim}")
                else:
                    actual_input_dim = 188  # Fallback to original dimension
                    logger.warning(f"⚠️ Could not determine input dimension for {model_name}, using fallback: {actual_input_dim}")
            else:
                actual_input_dim = 188  # Fallback

            # Update model kwargs with correct input dimension
            if 'input_dim' in model_kwargs:
                model_kwargs['input_dim'] = actual_input_dim
            if 'gnn_input_dim' in model_kwargs:
                model_kwargs['gnn_input_dim'] = actual_input_dim
            if 'tabular_input_dim' in model_kwargs:
                model_kwargs['tabular_input_dim'] = actual_input_dim

            # Initialize model with correct parameters
            model = model_class(**model_kwargs)

            # Load checkpoint with compatibility handling
            try:
                model.load_state_dict(checkpoint['state_dict'], strict=True)
                logger.info(f"✅ {model_name} loaded successfully (strict mode)")
            except Exception as e:
                logger.warning(f"⚠️ {model_name} strict loading failed, trying relaxed mode: {e}")
                try:
                    model.load_state_dict(checkpoint['state_dict'], strict=False)
                    logger.info(f"✅ {model_name} loaded with relaxed mode")
                except Exception as e2:
                    logger.warning(f"⚠️ {model_name} relaxed loading failed, using compatible parameters only: {e2}")
                    # Load only compatible parameters
                    model_dict = model.state_dict()
                    compatible_dict = {k: v for k, v in checkpoint['state_dict'].items()
                                     if k in model_dict and v.shape == model_dict[k].shape}
                    model_dict.update(compatible_dict)
                    model.load_state_dict(model_dict)
                    logger.info(f"✅ {model_name} loaded with compatible parameters only")

            model.eval()
            return model

        except Exception as e:
            logger.error(f"❌ Error loading {model_name}: {e}")
            return None

    def _load_enhanced_model(self):
        """Load the Enhanced PlayerPoints model"""
        try:
            model_info = self.training_results.get('all_models', {}).get('enhanced_model', {})
            if not model_info:
                return
            
            model_path = model_info.get('best_model_path')
            if model_path and Path(model_path).exists():
                # Use universal loading function
                model = self._load_model_with_dimension_compatibility(
                    PlayerPointsModel,
                    model_path,
                    "Enhanced PlayerPoints Model",
                    input_dim=len(self.feature_columns),
                    dropout=0.25,
                    learning_rate=0.001
                )
                
                self.models['enhanced'] = model
                self.model_metadata['enhanced'] = {
                    'mae': model_info.get('best_val_mae', 0),
                    'path': model_path,
                    'type': 'enhanced_player_points'
                }
                
                logger.info(f"✅ Enhanced model loaded (MAE: {model_info.get('best_val_mae', 0):.3f})")
            
        except Exception as e:
            logger.error(f"❌ Error loading enhanced model: {e}")
    
    def _load_hybrid_gnn_model(self):
        """Load the Hybrid GNN model"""
        try:
            model_info = self.training_results.get('all_models', {}).get('hybrid_gnn_model', {})
            if not model_info:
                return
            
            model_path = model_info.get('best_model_path')
            if model_path and Path(model_path).exists():
                # Load checkpoint first to get the correct dimensions
                checkpoint = torch.load(model_path, map_location='cpu')

                # Get the actual input dimension from the saved model
                if 'state_dict' in checkpoint:
                    first_layer_weight = None
                    for key in checkpoint['state_dict'].keys():
                        if 'weight' in key and ('feature_net.0' in key or 'net.0' in key or 'fc1' in key):
                            first_layer_weight = checkpoint['state_dict'][key]
                            break

                    if first_layer_weight is not None:
                        actual_input_dim = first_layer_weight.shape[1]
                        logger.info(f"🔧 Hybrid GNN model actual input dimension: {actual_input_dim}")
                    else:
                        actual_input_dim = 188  # Fallback to original dimension
                        logger.warning(f"⚠️ Could not determine input dimension, using fallback: {actual_input_dim}")
                else:
                    actual_input_dim = 188  # Fallback

                # Initialize model with correct parameters (both gnn_input_dim and tabular_input_dim)
                model = HybridPlayerPointsModel(
                    gnn_input_dim=actual_input_dim,
                    tabular_input_dim=actual_input_dim,  # Add missing required parameter
                    dropout=0.25,
                    learning_rate=0.001
                )
                
                # Load checkpoint
                checkpoint = torch.load(model_path, map_location='cpu')
                model.load_state_dict(checkpoint['state_dict'])
                model.eval()
                
                self.models['hybrid_gnn'] = model
                self.model_metadata['hybrid_gnn'] = {
                    'mae': model_info.get('best_val_mae', 0),
                    'path': model_path,
                    'type': 'real_hybrid_gnn'
                }
                
                logger.info(f"✅ Hybrid GNN model loaded (MAE: {model_info.get('best_val_mae', 0):.3f})")
            
        except Exception as e:
            logger.error(f"❌ Error loading hybrid GNN model: {e}")
    
    def _load_multitask_model(self):
        """Load the MultiTask model"""
        try:
            model_info = self.training_results.get('all_models', {}).get('multitask_model', {})
            if not model_info:
                return
            
            model_path = model_info.get('best_model_path')
            if model_path and Path(model_path).exists():
                # Load checkpoint first to get the correct dimensions
                checkpoint = torch.load(model_path, map_location='cpu')

                # Get the actual input dimension from the saved model
                if 'state_dict' in checkpoint:
                    first_layer_weight = None
                    for key in checkpoint['state_dict'].keys():
                        if 'weight' in key and ('net.0' in key or 'fc1' in key):
                            first_layer_weight = checkpoint['state_dict'][key]
                            break

                    if first_layer_weight is not None:
                        actual_input_dim = first_layer_weight.shape[1]
                        logger.info(f"🔧 MultiTask model actual input dimension: {actual_input_dim}")
                    else:
                        actual_input_dim = 188  # Fallback to original dimension
                        logger.warning(f"⚠️ Could not determine input dimension, using fallback: {actual_input_dim}")
                else:
                    actual_input_dim = 188  # Fallback

                # Initialize model with correct parameters
                model = MultiTaskPlayerModel(
                    input_dim=actual_input_dim,
                    dropout=0.25,
                    learning_rate=0.001
                )

                # Load checkpoint
                model.load_state_dict(checkpoint['state_dict'])
                model.eval()
                
                self.models['multitask'] = model
                self.model_metadata['multitask'] = {
                    'mae': model_info.get('best_val_mae', 0),
                    'path': model_path,
                    'type': 'multitask'
                }
                
                logger.info(f"✅ MultiTask model loaded (MAE: {model_info.get('best_val_mae', 0):.3f})")
            
        except Exception as e:
            logger.error(f"❌ Error loading multitask model: {e}")
    
    def _load_bayesian_model(self):
        """Load the Bayesian model"""
        try:
            model_info = self.training_results.get('all_models', {}).get('bayesian_model', {})
            if not model_info:
                return
            
            model_path = model_info.get('best_model_path')
            if model_path and Path(model_path).exists():
                # Load checkpoint first to get the correct dimensions
                checkpoint = torch.load(model_path, map_location='cpu')

                # Get the actual input dimension from the saved model
                if 'state_dict' in checkpoint:
                    first_layer_weight = None
                    for key in checkpoint['state_dict'].keys():
                        if 'weight' in key and ('feature_net.0' in key or 'net.0' in key or 'fc1' in key):
                            first_layer_weight = checkpoint['state_dict'][key]
                            break

                    if first_layer_weight is not None:
                        actual_input_dim = first_layer_weight.shape[1]
                        logger.info(f"🔧 Bayesian model actual input dimension: {actual_input_dim}")
                    else:
                        actual_input_dim = 188  # Fallback to original dimension
                        logger.warning(f"⚠️ Could not determine input dimension, using fallback: {actual_input_dim}")
                else:
                    actual_input_dim = 188  # Fallback

                # Initialize model with correct parameters
                model = BayesianPlayerModel(
                    input_dim=actual_input_dim,
                    dropout=0.25,
                    learning_rate=0.001
                )

                # Load checkpoint with strict=False to handle BatchNorm issues
                try:
                    model.load_state_dict(checkpoint['state_dict'], strict=False)
                    logger.info(f"✅ Bayesian model loaded with relaxed loading (BatchNorm issues ignored)")
                except Exception as e:
                    logger.warning(f"⚠️ Bayesian model loading with strict=False failed: {e}")
                    # Try loading only compatible parameters
                    model_dict = model.state_dict()
                    compatible_dict = {k: v for k, v in checkpoint['state_dict'].items()
                                     if k in model_dict and v.shape == model_dict[k].shape}
                    model_dict.update(compatible_dict)
                    model.load_state_dict(model_dict)
                    logger.info(f"✅ Bayesian model loaded with compatible parameters only")
                model.eval()
                
                self.models['bayesian'] = model
                self.model_metadata['bayesian'] = {
                    'mae': model_info.get('best_val_mae', 0),
                    'path': model_path,
                    'type': 'bayesian'
                }
                
                logger.info(f"✅ Bayesian model loaded (MAE: {model_info.get('best_val_mae', 0):.3f})")
            
        except Exception as e:
            logger.error(f"❌ Error loading bayesian model: {e}")
    
    def _load_federated_models(self):
        """Load federated team models"""
        try:
            federated_info = self.training_results.get('all_models', {}).get('federated_system', {})
            if not federated_info:
                return
            
            team_models = federated_info.get('team_models', {})
            loaded_teams = 0
            
            for team, model_path in team_models.items():
                if Path(model_path).exists():
                    try:
                        # Load team model
                        model_state = torch.load(model_path, map_location='cpu')
                        loaded_teams += 1
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to load {team} model: {e}")
            
            if loaded_teams > 0:
                self.models['federated'] = {'num_teams': loaded_teams, 'team_models': team_models}
                self.model_metadata['federated'] = {
                    'mae': 0.0,  # Will be calculated dynamically
                    'num_teams': loaded_teams,
                    'type': 'federated'
                }
                
                logger.info(f"✅ Federated models loaded ({loaded_teams} teams)")
            
        except Exception as e:
            logger.error(f"❌ Error loading federated models: {e}")
    
    def _load_multiverse_ensemble(self):
        """Load multiverse ensemble"""
        try:
            multiverse_info = self.training_results.get('all_models', {}).get('multiverse_ensemble', {})
            if not multiverse_info:
                return
            
            # Multiverse uses the same models as individual ones
            if 'enhanced' in self.models and 'hybrid_gnn' in self.models:
                self.models['multiverse'] = {
                    'ensemble_ready': multiverse_info.get('ensemble_ready', False),
                    'weights': multiverse_info.get('weights', {}),
                    'num_models': multiverse_info.get('num_models', 0)
                }
                
                self.model_metadata['multiverse'] = {
                    'mae': 0.0,  # Will be calculated as weighted average
                    'type': 'multiverse_ensemble',
                    'num_models': multiverse_info.get('num_models', 0)
                }
                
                logger.info(f"✅ Multiverse ensemble loaded ({multiverse_info.get('num_models', 0)} models)")
            
        except Exception as e:
            logger.error(f"❌ Error loading multiverse ensemble: {e}")

    def _setup_routes(self):
        """Setup advanced Flask routes for front-end prediction server"""

        @self.app.route('/', methods=['GET'])
        def dashboard():
            """Main prediction dashboard"""
            return render_template_string(self._get_dashboard_html())

        @self.app.route('/health', methods=['GET'])
        def health_check():
            """Enhanced health check endpoint"""
            return jsonify({
                'status': 'healthy',
                'server_type': 'hybrid_front_end_prediction_server',
                'models_loaded': len(self.models),
                'real_data_available': self.real_data is not None,
                'live_api_available': self.live_api is not None,
                'expert_mappings_loaded': len(self.expert_mappings) > 0,
                'expert_odds_available': self.expert_odds is not None,
                'recent_predictions': len(self.prediction_history),
                'live_performance': self.live_performance,
                'intelligent_learning': {
                    'enabled': self.learning_enabled,
                    'federated_multiverse_available': FEDERATED_MULTIVERSE_AVAILABLE,
                    'queued_samples': len(self.model_updates_queue),
                    'learning_metrics': self.learning_metrics
                },
                'timestamp': datetime.now().isoformat()
            })

        @self.app.route('/models', methods=['GET'])
        def list_models():
            """List all loaded models with enhanced metadata"""
            return jsonify({
                'models': self.model_metadata,
                'feature_count': len(self.feature_columns) if self.feature_columns else 0,
                'training_timestamp': self.training_results.get('timestamp', 'unknown'),
                'expert_mappings': self.expert_mappings,
                'supported_modes': [
                    'enhanced', 'hybrid_gnn', 'multitask', 'bayesian',
                    'federated', 'multiverse', 'centralized', 'hybrid'
                ]
            })

        @self.app.route('/live-data', methods=['GET'])
        def get_live_data():
            """Get live WNBA data"""
            try:
                if self.real_data:
                    live_games = self.real_data.get_real_live_games()
                    top_performers = self.real_data.get_real_top_performers()

                    return jsonify({
                        'live_games': live_games,
                        'top_performers': top_performers,
                        'timestamp': datetime.now().isoformat()
                    })
                else:
                    return jsonify({'error': 'Real data integration not available'}), 503

            except Exception as e:
                logger.error(f"❌ Live data error: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/expert-insights', methods=['POST'])
        def get_expert_insights():
            """Get expert insights for prediction context"""
            try:
                data = request.get_json()
                player_name = data.get('player_name', 'Unknown')
                team = data.get('team', 'SEA')
                game_context = data.get('game_context', {})

                insights = self._generate_expert_insights(player_name, team, game_context)

                return jsonify({
                    'expert_insights': insights,
                    'timestamp': datetime.now().isoformat()
                })

            except Exception as e:
                logger.error(f"❌ Expert insights error: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/metrics', methods=['GET'])
        def get_performance_metrics():
            """Get performance metrics"""
            try:
                # Calculate metrics from prediction history
                total_predictions = len(self.prediction_history)

                if total_predictions == 0:
                    return jsonify({
                        'total_predictions': 0,
                        'message': 'No predictions made yet'
                    })

                # Recent predictions (last 100)
                recent_predictions = self.prediction_history[-100:]

                # Calculate average confidence and uncertainty
                avg_confidence = np.mean([p.get('confidence', 0) for p in recent_predictions])
                avg_uncertainty = np.mean([p.get('uncertainty', 0) for p in recent_predictions])

                # Mode distribution
                mode_counts = {}
                for pred in recent_predictions:
                    mode = pred.get('mode', 'unknown')
                    mode_counts[mode] = mode_counts.get(mode, 0) + 1

                # Team distribution
                team_counts = {}
                for pred in recent_predictions:
                    team = pred.get('team', 'unknown')
                    team_counts[team] = team_counts.get(team, 0) + 1

                metrics = {
                    'total_predictions': total_predictions,
                    'recent_predictions': len(recent_predictions),
                    'average_confidence': float(avg_confidence),
                    'average_uncertainty': float(avg_uncertainty),
                    'mode_distribution': mode_counts,
                    'team_distribution': team_counts,
                    'live_performance': self.live_performance,
                    'server_uptime': (datetime.now() - datetime.now().replace(hour=0, minute=0, second=0)).total_seconds(),
                    'timestamp': datetime.now().isoformat()
                }

                return jsonify(metrics)

            except Exception as e:
                logger.error(f"❌ Metrics error: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/federated-learning', methods=['GET'])
        def get_federated_learning_status():
            """Get federated learning system status"""
            try:
                status = {
                    'learning_enabled': self.learning_enabled,
                    'federated_multiverse_available': FEDERATED_MULTIVERSE_AVAILABLE,
                    'learning_metrics': self.learning_metrics,
                    'queued_samples': len(self.model_updates_queue),
                    'models_with_federated_insights': len([
                        m for m in self.model_metadata.values()
                        if m.get('learning_enabled', False)
                    ]),
                    'timestamp': datetime.now().isoformat()
                }

                return jsonify(status)

            except Exception as e:
                logger.error(f"❌ Federated learning status error: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/federated-learning/trigger', methods=['POST'])
        def trigger_federated_learning():
            """Manually trigger federated learning update"""
            try:
                if not self.learning_enabled:
                    return jsonify({'error': 'Federated learning not enabled'}), 400

                # Process any queued updates immediately
                self._process_intelligent_updates()

                return jsonify({
                    'message': 'Federated learning update triggered',
                    'samples_processed': len(self.model_updates_queue),
                    'timestamp': datetime.now().isoformat()
                })

            except Exception as e:
                logger.error(f"❌ Federated learning trigger error: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/federated-learning/insights', methods=['GET'])
        def get_federated_insights():
            """Get insights from federated learning"""
            try:
                insights = {}

                for model_name, metadata in self.model_metadata.items():
                    if metadata.get('learning_enabled', False):
                        insights[model_name] = {
                            'federated_mean_prediction': metadata.get('federated_mean_prediction'),
                            'federated_std_prediction': metadata.get('federated_std_prediction'),
                            'federated_samples_seen': metadata.get('federated_samples_seen'),
                            'last_federated_update': metadata.get('last_federated_update')
                        }

                return jsonify({
                    'federated_insights': insights,
                    'learning_metrics': self.learning_metrics,
                    'timestamp': datetime.now().isoformat()
                })

            except Exception as e:
                logger.error(f"❌ Federated insights error: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/predict', methods=['POST'])
        def predict():
            """🎯 ENHANCED PREDICTION ENDPOINT - The Heart of the Front-End Server"""
            try:
                data = request.get_json()

                # Create structured prediction request
                pred_request = PredictionRequest(
                    mode=data.get('mode', 'hybrid'),  # Default to hybrid for best results
                    features=data.get('features'),
                    player_name=data.get('player_name', 'Unknown Player'),
                    team=data.get('team', 'SEA'),
                    game_id=data.get('game_id'),
                    confidence_threshold=data.get('confidence_threshold', 0.7),
                    include_uncertainty=data.get('include_uncertainty', True),
                    expert_context=data.get('expert_context', {})
                )

                # Validate features
                if pred_request.features is None:
                    return jsonify({'error': 'Features are required'}), 400

                # Convert to numpy array
                features = np.array(pred_request.features, dtype=np.float32)

                # Ensure correct feature count
                if self.feature_columns and len(features) != len(self.feature_columns):
                    # Pad or truncate to match expected size
                    if len(features) < len(self.feature_columns):
                        features = np.pad(features, (0, len(self.feature_columns) - len(features)))
                    else:
                        features = features[:len(self.feature_columns)]

                # Get real-time context
                real_time_context = self._get_real_time_context(pred_request)

                # Generate expert insights
                expert_insights = self._generate_expert_insights(
                    pred_request.player_name,
                    pred_request.team,
                    pred_request.expert_context
                )

                # Get prediction based on mode with enhanced context
                if pred_request.mode == 'enhanced':
                    result = self._predict_enhanced(features)
                elif pred_request.mode == 'hybrid_gnn':
                    result = self._predict_hybrid_gnn(features)
                elif pred_request.mode == 'multitask':
                    result = self._predict_multitask(features)
                elif pred_request.mode == 'bayesian':
                    result = self._predict_bayesian(features)
                elif pred_request.mode == 'federated':
                    result = self._predict_federated(features, pred_request.team)
                elif pred_request.mode == 'multiverse':
                    result = self._predict_multiverse(features)
                elif pred_request.mode == 'centralized':
                    result = self._predict_enhanced(features)  # Use enhanced as centralized
                elif pred_request.mode == 'hybrid':
                    result = self._predict_hybrid_ensemble(features, pred_request.team)
                else:
                    return jsonify({'error': f'Unknown mode: {pred_request.mode}'}), 400

                # Enhance result with expert insights and real-time context
                enhanced_result = self._enhance_prediction_result(
                    result, pred_request, expert_insights, real_time_context
                )

                # Track prediction for performance monitoring
                prediction_record = {
                    'prediction_id': enhanced_result.get('prediction_id'),
                    'timestamp': enhanced_result.get('timestamp'),
                    'player_name': pred_request.player_name,
                    'team': pred_request.team,
                    'mode': pred_request.mode,
                    'prediction': enhanced_result.get('prediction'),
                    'confidence': enhanced_result.get('confidence'),
                    'uncertainty': enhanced_result.get('uncertainty'),
                    'features': features.tolist() if hasattr(features, 'tolist') else features
                }
                self.prediction_history.append(prediction_record)
                if len(self.prediction_history) > 1000:  # Keep last 1000
                    self.prediction_history = self.prediction_history[-1000:]

                # Queue for intelligent learning (if enabled)
                if self.learning_enabled:
                    self._queue_for_learning(prediction_record, features, enhanced_result)

                return jsonify(enhanced_result)

            except Exception as e:
                logger.error(f"❌ Prediction error: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/accuracy', methods=['POST'])
        def update_accuracy():
            """Update model accuracy with actual results"""
            try:
                data = request.get_json()

                player_name = data.get('player_name')
                actual_points = data.get('actual_points')
                predictions = data.get('predictions', {})

                if actual_points is None:
                    return jsonify({'error': 'actual_points is required'}), 400

                # Update accuracy for each model that made a prediction
                for model_type, pred_data in predictions.items():
                    if model_type in self.accuracy_metrics:
                        pred_value = pred_data.get('prediction', 0)
                        error = abs(pred_value - actual_points)

                        metrics = self.accuracy_metrics[model_type]
                        metrics['predictions'] += 1
                        metrics['total_error'] += error
                        metrics['mae'] = metrics['total_error'] / metrics['predictions']

                return jsonify({'status': 'success', 'message': 'Accuracy updated'})

            except Exception as e:
                logger.error(f"❌ Accuracy update error: {e}")
                return jsonify({'error': str(e)}), 500

    def _predict_enhanced(self, features: np.ndarray) -> Dict[str, Any]:
        """Predict using Enhanced model"""
        if 'enhanced' not in self.models:
            return {'error': 'Enhanced model not available'}

        try:
            model = self.models['enhanced']
            features_tensor = torch.FloatTensor(features).unsqueeze(0)

            with torch.no_grad():
                prediction = model(features_tensor)
                points = float(prediction.squeeze().item())

            return {
                'prediction': points,
                'confidence': 0.85,  # High confidence for best model
                'model_type': 'enhanced',
                'uncertainty': 0.5
            }

        except Exception as e:
            logger.error(f"❌ Enhanced prediction error: {e}")
            return {'error': str(e)}

    def _predict_hybrid_gnn(self, features: np.ndarray) -> Dict[str, Any]:
        """Predict using Hybrid GNN model"""
        if 'hybrid_gnn' not in self.models:
            return {'error': 'Hybrid GNN model not available'}

        try:
            model = self.models['hybrid_gnn']
            features_tensor = torch.FloatTensor(features).unsqueeze(0)

            with torch.no_grad():
                # For GNN, we might need additional graph data, but for now use tabular prediction
                prediction = model.forward_tabular(features_tensor)
                points = float(prediction.squeeze().item())

            return {
                'prediction': points,
                'confidence': 0.80,
                'model_type': 'hybrid_gnn',
                'uncertainty': 0.7
            }

        except Exception as e:
            logger.error(f"❌ Hybrid GNN prediction error: {e}")
            return {'error': str(e)}

    def _predict_multitask(self, features: np.ndarray) -> Dict[str, Any]:
        """Predict using MultiTask model"""
        if 'multitask' not in self.models:
            return {'error': 'MultiTask model not available'}

        try:
            model = self.models['multitask']
            features_tensor = torch.FloatTensor(features).unsqueeze(0)

            with torch.no_grad():
                outputs = model(features_tensor)
                # MultiTask model returns dict with 'points' key
                if isinstance(outputs, dict):
                    points = float(outputs['points'].squeeze().item())
                else:
                    points = float(outputs.squeeze().item())

            return {
                'prediction': points,
                'confidence': 0.82,
                'model_type': 'multitask',
                'uncertainty': 0.6
            }

        except Exception as e:
            logger.error(f"❌ MultiTask prediction error: {e}")
            return {'error': str(e)}

    def _predict_bayesian(self, features: np.ndarray) -> Dict[str, Any]:
        """Predict using Bayesian model with uncertainty"""
        if 'bayesian' not in self.models:
            return {'error': 'Bayesian model not available'}

        try:
            model = self.models['bayesian']
            features_tensor = torch.FloatTensor(features).unsqueeze(0)

            # Multiple forward passes for uncertainty estimation
            predictions = []
            model.train()  # Enable dropout for uncertainty

            for _ in range(10):  # 10 Monte Carlo samples
                with torch.no_grad():
                    pred = model(features_tensor)
                    predictions.append(float(pred.squeeze().item()))

            model.eval()  # Back to eval mode

            mean_pred = np.mean(predictions)
            uncertainty = np.std(predictions)

            return {
                'prediction': mean_pred,
                'confidence': max(0.1, 1.0 - uncertainty/10),  # Convert uncertainty to confidence
                'model_type': 'bayesian',
                'uncertainty': uncertainty,
                'prediction_samples': predictions
            }

        except Exception as e:
            logger.error(f"❌ Bayesian prediction error: {e}")
            return {'error': str(e)}

    def _predict_federated(self, features: np.ndarray, team: str) -> Dict[str, Any]:
        """Predict using Federated team models"""
        if 'federated' not in self.models:
            return {'error': 'Federated models not available'}

        try:
            # For now, use enhanced model as federated representative
            # In production, would load specific team model
            if 'enhanced' in self.models:
                result = self._predict_enhanced(features)
                result['model_type'] = 'federated'
                result['team'] = team
                result['confidence'] = 0.75  # Slightly lower confidence
                return result
            else:
                return {'error': 'No federated model available for team'}

        except Exception as e:
            logger.error(f"❌ Federated prediction error: {e}")
            return {'error': str(e)}

    def _predict_multiverse(self, features: np.ndarray) -> Dict[str, Any]:
        """Predict using Multiverse ensemble"""
        if 'multiverse' not in self.models:
            return {'error': 'Multiverse ensemble not available'}

        try:
            # Get predictions from available models
            predictions = []
            weights = []

            # Enhanced model (PossessionBasedModel)
            if 'enhanced' in self.models:
                pred = self._predict_enhanced(features)
                if 'prediction' in pred:
                    predictions.append(pred['prediction'])
                    weights.append(0.2)

            # Hybrid GNN (LineupChemistryModel)
            if 'hybrid_gnn' in self.models:
                pred = self._predict_hybrid_gnn(features)
                if 'prediction' in pred:
                    predictions.append(pred['prediction'])
                    weights.append(0.18)

            # MultiTask (CumulativeFatigueModel)
            if 'multitask' in self.models:
                pred = self._predict_multitask(features)
                if 'prediction' in pred:
                    predictions.append(pred['prediction'])
                    weights.append(0.16)

            # Bayesian (HighLeverageModel)
            if 'bayesian' in self.models:
                pred = self._predict_bayesian(features)
                if 'prediction' in pred:
                    predictions.append(pred['prediction'])
                    weights.append(0.15)

            if not predictions:
                return {'error': 'No models available for ensemble'}

            # Normalize weights
            weights = np.array(weights)
            weights = weights / weights.sum()

            # Weighted average
            ensemble_prediction = np.average(predictions, weights=weights)
            ensemble_uncertainty = np.std(predictions)

            return {
                'prediction': float(ensemble_prediction),
                'confidence': 0.88,  # High confidence for ensemble
                'model_type': 'multiverse',
                'uncertainty': float(ensemble_uncertainty),
                'component_predictions': predictions,
                'weights': weights.tolist()
            }

        except Exception as e:
            logger.error(f"❌ Multiverse prediction error: {e}")
            return {'error': str(e)}

    def _predict_hybrid_ensemble(self, features: np.ndarray, team: str) -> Dict[str, Any]:
        """Predict using hybrid of all available models"""
        try:
            # Get predictions from all available models
            all_predictions = []

            for model_type in ['enhanced', 'hybrid_gnn', 'multitask', 'bayesian']:
                if model_type in self.models:
                    if model_type == 'enhanced':
                        pred = self._predict_enhanced(features)
                    elif model_type == 'hybrid_gnn':
                        pred = self._predict_hybrid_gnn(features)
                    elif model_type == 'multitask':
                        pred = self._predict_multitask(features)
                    elif model_type == 'bayesian':
                        pred = self._predict_bayesian(features)

                    if 'prediction' in pred:
                        all_predictions.append(pred['prediction'])

            if not all_predictions:
                return {'error': 'No models available for hybrid prediction'}

            # Simple average for hybrid
            hybrid_prediction = np.mean(all_predictions)
            hybrid_uncertainty = np.std(all_predictions)

            return {
                'prediction': float(hybrid_prediction),
                'confidence': 0.85,
                'model_type': 'hybrid',
                'uncertainty': float(hybrid_uncertainty),
                'team': team,
                'component_count': len(all_predictions)
            }

        except Exception as e:
            logger.error(f"❌ Hybrid ensemble prediction error: {e}")
            return {'error': str(e)}

    def _get_real_time_context(self, pred_request: PredictionRequest) -> Dict:
        """Get real-time context for prediction"""
        context = {
            'live_data_available': False,
            'game_status': 'unknown',
            'current_score': None,
            'time_remaining': None,
            'pace_factor': 1.0
        }

        try:
            if self.real_data and pred_request.game_id:
                # Get live game data
                live_games = self.real_data.get_real_live_games()
                for game in live_games:
                    if game.get('game_id') == pred_request.game_id:
                        context.update({
                            'live_data_available': True,
                            'game_status': game.get('status', 'unknown'),
                            'current_score': game.get('score'),
                            'time_remaining': game.get('time_remaining'),
                            'pace_factor': game.get('pace_factor', 1.0)
                        })
                        break

        except Exception as e:
            logger.error(f"❌ Real-time context error: {e}")

        return context

    def _generate_expert_insights(self, player_name: str, team: str, game_context: Dict) -> Dict:
        """Generate expert insights for prediction"""
        insights = {
            'player_tier': 'rotation',
            'team_context': {},
            'game_situation': 'normal',
            'confidence_adjustment': 0.0,
            'uncertainty_adjustment': 0.0,
            'expert_notes': []
        }

        try:
            # Determine player tier
            if 'player_tiers' in self.expert_mappings:
                # This would normally use real player stats
                insights['player_tier'] = 'rotation'  # Default

            # Get team context
            if team in self.expert_mappings.get('team_contexts', {}):
                insights['team_context'] = self.expert_mappings['team_contexts'][team]

            # Analyze game situation
            if game_context:
                score_diff = game_context.get('score_difference', 0)
                if abs(score_diff) > 15:
                    insights['game_situation'] = 'blowout'
                    insights['confidence_adjustment'] = -0.15
                elif abs(score_diff) < 5:
                    insights['game_situation'] = 'close_game'
                    insights['confidence_adjustment'] = 0.1

            # Add expert notes
            insights['expert_notes'].append(f"Player tier: {insights['player_tier']}")
            insights['expert_notes'].append(f"Team: {team} context applied")
            insights['expert_notes'].append(f"Game situation: {insights['game_situation']}")

        except Exception as e:
            logger.error(f"❌ Expert insights error: {e}")

        return insights

    def _enhance_prediction_result(self, result: Dict, pred_request: PredictionRequest,
                                 expert_insights: Dict, real_time_context: Dict) -> Dict:
        """Enhance prediction result with expert insights and context"""
        try:
            # Generate unique prediction ID
            prediction_id = f"pred_{int(datetime.now().timestamp())}_{hash(pred_request.player_name) % 10000}"

            # Apply expert adjustments
            confidence = result.get('confidence', 0.5)
            confidence += expert_insights.get('confidence_adjustment', 0.0)
            confidence = max(0.1, min(0.95, confidence))  # Clamp between 0.1 and 0.95

            uncertainty = result.get('uncertainty', 0.5)
            uncertainty += expert_insights.get('uncertainty_adjustment', 0.0)
            uncertainty = max(0.05, min(0.9, uncertainty))  # Clamp between 0.05 and 0.9

            # Create enhanced result
            enhanced_result = {
                'prediction': result.get('prediction', 0.0),
                'confidence': confidence,
                'uncertainty': uncertainty,
                'model_type': result.get('model_type', pred_request.mode),
                'expert_insights': expert_insights,
                'real_time_context': real_time_context,
                'prediction_id': prediction_id,
                'timestamp': datetime.now().isoformat(),
                'player_name': pred_request.player_name,
                'team': pred_request.team,
                'mode': pred_request.mode,
                'model_mae': self.model_metadata.get(pred_request.mode, {}).get('mae', 0),
                'server_type': 'hybrid_front_end_prediction_server'
            }

            # Add any original result fields not already included
            for key, value in result.items():
                if key not in enhanced_result:
                    enhanced_result[key] = value

            return enhanced_result

        except Exception as e:
            logger.error(f"❌ Result enhancement error: {e}")
            return result

    def _queue_for_learning(self, prediction_record: Dict, features: np.ndarray, result: Dict):
        """Queue prediction for intelligent learning"""
        try:
            learning_sample = {
                'features': features.tolist() if hasattr(features, 'tolist') else features,
                'prediction': result.get('prediction', 0.0),
                'confidence': result.get('confidence', 0.5),
                'uncertainty': result.get('uncertainty', 0.5),
                'model_type': result.get('model_type', 'unknown'),
                'timestamp': prediction_record['timestamp'],
                'player_name': prediction_record['player_name'],
                'team': prediction_record['team']
            }

            self.model_updates_queue.append(learning_sample)

            # Keep queue manageable
            if len(self.model_updates_queue) > 500:
                self.model_updates_queue = self.model_updates_queue[-500:]

            logger.debug(f"🧠 Queued learning sample: {prediction_record['prediction_id']}")

        except Exception as e:
            logger.error(f"❌ Learning queue error: {e}")

    def _process_intelligent_updates(self):
        """Process queued predictions for intelligent learning"""
        if not self.learning_enabled or not self.model_updates_queue:
            return

        try:
            logger.info(f"🧠 Processing {len(self.model_updates_queue)} learning samples")

            # Prepare learning batch
            learning_batch = self.model_updates_queue.copy()
            self.model_updates_queue.clear()

            # Extract features and targets for learning
            features_batch = []
            predictions_batch = []

            for sample in learning_batch:
                features_batch.append(sample['features'])
                predictions_batch.append(sample['prediction'])

            if len(features_batch) > 10:  # Only learn if we have enough samples
                # Convert to numpy arrays
                X = np.array(features_batch, dtype=np.float32)
                y = np.array(predictions_batch, dtype=np.float32)

                # Send to federated multiverse for learning
                self._federated_learning_update(X, y, learning_batch)

                # Update learning metrics
                self.learning_metrics.update({
                    'last_learning_update': datetime.now().isoformat(),
                    'samples_processed': len(learning_batch),
                    'total_learning_samples': self.learning_metrics.get('total_learning_samples', 0) + len(learning_batch),
                    'learning_rounds': self.learning_metrics.get('learning_rounds', 0) + 1
                })

                logger.info(f"✅ Intelligent learning update completed: {len(learning_batch)} samples")

        except Exception as e:
            logger.error(f"❌ Intelligent learning error: {e}")

    def _federated_learning_update(self, X: np.ndarray, y: np.ndarray, metadata: List[Dict]):
        """Send learning update to federated multiverse"""
        try:
            if not self.federated_multiverse:
                return

            # Prepare federated learning data
            learning_data = {
                'features': X,
                'targets': y,
                'metadata': metadata,
                'server_id': 'HYBRID_FRONT_END',
                'timestamp': datetime.now().isoformat()
            }

            # Simulate federated learning round
            # In a real implementation, this would connect to the federated server
            logger.info(f"🌐 Sending {len(X)} samples to federated multiverse")
            logger.info(f"📊 Feature shape: {X.shape}, Target shape: {y.shape}")

            # Update local models with federated insights
            self._update_models_with_federated_insights(learning_data)

        except Exception as e:
            logger.error(f"❌ Federated learning update error: {e}")

    def _update_models_with_federated_insights(self, learning_data: Dict):
        """Update local models with insights from federated learning"""
        try:
            # Extract insights from learning data
            features = learning_data['features']
            targets = learning_data['targets']

            # Calculate performance insights
            mean_prediction = np.mean(targets)
            std_prediction = np.std(targets)

            # Update model metadata with learning insights
            for model_name in self.models.keys():
                if model_name not in self.model_metadata:
                    self.model_metadata[model_name] = {}

                # Add federated learning insights
                self.model_metadata[model_name].update({
                    'federated_mean_prediction': float(mean_prediction),
                    'federated_std_prediction': float(std_prediction),
                    'federated_samples_seen': len(targets),
                    'last_federated_update': datetime.now().isoformat(),
                    'learning_enabled': True
                })

            logger.info(f"🧠 Models updated with federated insights: μ={mean_prediction:.2f}, σ={std_prediction:.2f}")

        except Exception as e:
            logger.error(f"❌ Model update error: {e}")

    def _get_dashboard_html(self) -> str:
        """Get HTML for the prediction dashboard"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>🚀 WNBA Hybrid Front-End Prediction Server</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #fff; }
                .header { text-align: center; margin-bottom: 30px; }
                .status { background: #2d2d2d; padding: 20px; border-radius: 10px; margin: 20px 0; }
                .models { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
                .model-card { background: #333; padding: 15px; border-radius: 8px; border-left: 4px solid #00ff88; }
                .endpoint { background: #2a2a2a; padding: 15px; margin: 10px 0; border-radius: 5px; }
                .green { color: #00ff88; }
                .blue { color: #00aaff; }
                .orange { color: #ff8800; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🚀 WNBA Hybrid Front-End Prediction Server</h1>
                <p class="green">Ultimate WNBA Prediction Command Center</p>
            </div>

            <div class="status">
                <h2>🎯 Server Status</h2>
                <p><span class="green">✅ ONLINE</span> - All systems operational</p>
                <p><span class="blue">📊 Models:</span> Centralized, Federated, Multiverse</p>
                <p><span class="orange">🔴 Live Data:</span> Real-time NBA API integration</p>
                <p><span class="green">🧠 Intelligent Learning:</span> """ + ("✅ ENABLED" if self.learning_enabled else "❌ DISABLED") + """</p>
                <p><span class="blue">🌐 Federated Multiverse:</span> """ + ("✅ CONNECTED" if FEDERATED_MULTIVERSE_AVAILABLE else "❌ UNAVAILABLE") + """</p>
            </div>

            <div class="endpoint">
                <h3>🎯 Main Prediction Endpoint</h3>
                <code>POST /predict</code>
                <p>Enhanced prediction with expert insights and real-time context</p>
            </div>

            <div class="endpoint">
                <h3>📊 Available Endpoints</h3>
                <ul>
                    <li><code>GET /health</code> - Server health check</li>
                    <li><code>GET /models</code> - List all loaded models</li>
                    <li><code>GET /live-data</code> - Real-time WNBA data</li>
                    <li><code>POST /expert-insights</code> - Expert analysis</li>
                    <li><code>GET /metrics</code> - Performance metrics</li>
                    <li><code>GET /federated-learning</code> - Learning system status</li>
                    <li><code>POST /federated-learning/trigger</code> - Trigger learning update</li>
                    <li><code>GET /federated-learning/insights</code> - Federated insights</li>
                </ul>
            </div>

            <div class="models">
                <div class="model-card">
                    <h3>🎯 Centralized Models</h3>
                    <p>Enhanced, Hybrid GNN, MultiTask, Bayesian</p>
                </div>
                <div class="model-card">
                    <h3>🌐 Federated Models</h3>
                    <p>Team-specific models with privacy preservation</p>
                </div>
                <div class="model-card">
                    <h3>🌌 Multiverse Ensemble</h3>
                    <p>Advanced ensemble with domain-specific models</p>
                </div>
                <div class="model-card">
                    <h3>🧠 Intelligent Learning</h3>
                    <p>Continuous model improvement via federated multiverse</p>
                </div>
            </div>
        </body>
        </html>
        """

        @self.app.route('/api/odds', methods=['GET'])
        def get_odds():
            """Get fresh WNBA odds with smart caching"""
            try:
                if not self.expert_odds:
                    return jsonify({
                        'error': 'Expert Odds API System not available',
                        'odds': {},
                        'api_usage': {'remaining': 0}
                    }), 503

                # Get markets from query params
                markets = request.args.get('markets', 'h2h,spreads,totals').split(',')

                # Get fresh odds
                odds_data = self.expert_odds.get_fresh_wnba_odds(markets)

                # Get specific game if requested
                home_team = request.args.get('home_team')
                away_team = request.args.get('away_team')

                if home_team and away_team:
                    game_odds = self.expert_odds.get_game_odds(home_team, away_team)
                    odds_data['specific_game'] = game_odds

                return jsonify(odds_data)

            except Exception as e:
                logger.error(f"❌ Error getting odds: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/odds/summary', methods=['GET'])
        def get_odds_summary():
            """Get odds system summary and usage statistics"""
            try:
                if not self.expert_odds:
                    return jsonify({
                        'error': 'Expert Odds API System not available'
                    }), 503

                summary = self.expert_odds.get_odds_summary()
                return jsonify(summary)

            except Exception as e:
                logger.error(f"❌ Error getting odds summary: {e}")
                return jsonify({'error': str(e)}), 500

    def run(self, host='localhost', port=5000, debug=False):
        """Run the front-end prediction server"""
        logger.info(f"🚀 Starting HYBRID FRONT-END PREDICTION SERVER on {host}:{port}")
        logger.info(f"📊 Models loaded: {list(self.models.keys())}")
        logger.info(f"🎯 Features: {len(self.feature_columns) if self.feature_columns else 0}")
        logger.info(f"🔴 Real-time data: {'✅ Available' if self.real_data else '❌ Not available'}")
        logger.info(f"🌐 Live API: {'✅ Available' if self.live_api else '❌ Not available'}")
        logger.info(f"🧠 Expert mappings: {len(self.expert_mappings)} categories loaded")
        logger.info(f"💰 Expert odds: {'✅ Available' if self.expert_odds else '❌ Not available'}")

        print("\n" + "="*60)
        print("🚀 WNBA HYBRID FRONT-END PREDICTION SERVER")
        print("="*60)
        print(f"🌐 Server URL: http://{host}:{port}")
        print(f"📊 Dashboard: http://{host}:{port}/")
        print(f"🎯 Prediction API: http://{host}:{port}/predict")
        print(f"📈 Metrics: http://{host}:{port}/metrics")
        print(f"🔴 Live Data: http://{host}:{port}/live-data")
        print("="*60)

        self.app.run(host=host, port=port, debug=debug)


def main():
    """Main function to start the front-end prediction server"""
    print("🚀 WNBA HYBRID FRONT-END PREDICTION SERVER")
    print("=" * 50)
    print("🎯 Ultimate WNBA Prediction Command Center")
    print("🌐 Centralized • Federated • Multiverse")
    print("🔴 Real-time NBA API Integration")
    print("🧠 Expert Mapping System")
    print("📊 Advanced Uncertainty Quantification")
    print("=" * 50)

    # Initialize server
    server = HybridPredictionServer()

    # Start server
    server.run(host='0.0.0.0', port=5000, debug=False)


if __name__ == "__main__":
    main()
