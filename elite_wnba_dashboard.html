<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELITE WNBA PREDICTION DASHBOARD</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        :root {
            /* 2025 ESPN WNBA Broadcast Colors */
            --vampire-black: #050707;      /* Sophistication */
            --quartz: #4C4C4D;             /* Trust */
            --pearl: #EFE3C6;              /* Sincerity */
            --princeton-orange: #F57B20;   /* Energy & Empowerment */

            /* Derived colors for UI elements */
            --success-green: #4CAF50;
            --warning-amber: #FFC107;
            --error-red: #F44336;
            --accent-blue: #2196F3;
        }
        
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, var(--vampire-black) 0%, #000000 100%);
            color: var(--pearl);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow-x: hidden;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto 1fr 1fr;
            gap: 16px;
            padding: 20px;
            height: 100vh;
            grid-template-areas:
                "header header header"
                "player main live"
                "analytics analytics value";
        }
        
        .panel {
            background: rgba(76, 76, 77, 0.15);
            backdrop-filter: blur(12px);
            border-radius: 12px;
            border: 1px solid rgba(245, 123, 32, 0.3);
            padding: 20px;
            box-shadow: 0 4px 25px rgba(5, 7, 7, 0.6);
            overflow: hidden;
            position: relative;
            transition: all 0.3s ease;
        }

        .panel:hover {
            border-color: rgba(245, 123, 32, 0.6);
            box-shadow: 0 6px 30px rgba(245, 123, 32, 0.25);
            background: rgba(76, 76, 77, 0.25);
        }
        
        .header-panel {
            grid-area: header;
            background: linear-gradient(90deg, var(--vampire-black), var(--princeton-orange));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 30px;
            border: 1px solid var(--quartz);
        }
        
        .player-panel {
            grid-area: player;
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            overflow-y: auto;
        }
        
        .main-panel {
            grid-area: main;
            display: flex;
            flex-direction: column;
        }
        
        .live-panel {
            grid-area: live;
            display: flex;
            flex-direction: column;
        }
        
        .analytics-panel {
            grid-area: analytics;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }
        
        .value-panel {
            grid-area: value;
            overflow-y: auto;
        }
        
        .player-card {
            background: rgba(5, 7, 7, 0.7);
            border-radius: 8px;
            padding: 15px;
            border-left: 3px solid var(--princeton-orange);
            position: relative;
            transition: all 0.3s ease;
            border: 1px solid rgba(76, 76, 77, 0.4);
        }

        .player-card:hover {
            background: rgba(5, 7, 7, 0.9);
            border-left-color: var(--pearl);
            border-color: rgba(245, 123, 32, 0.6);
        }
        
        .player-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .team-tag {
            background: var(--princeton-orange);
            color: var(--vampire-black);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
            border: 1px solid var(--pearl);
        }
        
        .stat-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            align-items: center;
        }
        
        .stat-values {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .predicted {
            color: var(--princeton-orange);
            font-weight: bold;
        }

        .actual {
            color: var(--pearl);
            font-weight: bold;
        }
        
        .accuracy-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .accuracy-high { background: var(--success-green); }
        .accuracy-medium { background: var(--warning-orange); }
        .accuracy-low { background: var(--error-red); }
        
        .meter {
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 4px;
            position: relative;
        }
        
        .prediction-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--princeton-orange), #E55A00);
            transition: width 0.5s ease;
        }

        .actual-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--pearl), var(--quartz));
            position: absolute;
            top: 0;
            transition: width 0.5s ease;
            opacity: 0.9;
        }
        
        .value-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
            gap: 10px;
            padding: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .value-row:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        
        .value-row.high-value {
            background: rgba(76, 175, 80, 0.15);
            border-left: 3px solid var(--success-green);
        }
        
        .value-row.negative-value {
            background: rgba(244, 67, 54, 0.15);
            border-left: 3px solid var(--error-red);
        }
        
        .edge-positive {
            color: var(--success-green);
            font-weight: bold;
        }
        
        .edge-negative {
            color: var(--error-red);
            font-weight: bold;
        }
        
        .control-btn {
            background: rgba(76, 76, 77, 0.3);
            border: 1px solid var(--princeton-orange);
            color: var(--pearl);
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .control-btn:hover {
            background: rgba(245, 123, 32, 0.2);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(245, 123, 32, 0.4);
            border-color: var(--pearl);
        }

        .control-btn.active {
            background: var(--princeton-orange);
            color: var(--vampire-black);
            border-color: var(--pearl);
        }
        
        .play-event {
            padding: 8px 12px;
            margin: 4px 0;
            background: rgba(76, 76, 77, 0.2);
            border-radius: 6px;
            border-left: 3px solid var(--quartz);
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .play-event:hover {
            background: rgba(76, 76, 77, 0.4);
        }

        .play-event.highlight {
            border-left-color: var(--princeton-orange);
            background: rgba(245, 123, 32, 0.15);
            color: var(--pearl);
        }
        
        .adjustment-alert {
            background: rgba(245, 123, 32, 0.15);
            border: 1px solid var(--princeton-orange);
            border-radius: 8px;
            padding: 12px;
            margin-top: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        .status-live { background: var(--success-green); }
        .status-simulation { background: var(--warning-orange); }
        .status-offline { background: var(--error-red); }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        h1, h2, h3 {
            margin: 0;
            font-weight: 600;
        }
        
        h1 {
            font-size: 1.8rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        h2 {
            font-size: 1.4rem;
            margin-bottom: 15px;
            color: var(--princeton-orange);
        }
        
        h3 {
            font-size: 1.1rem;
        }
        
        canvas {
            width: 100%;
            height: 100%;
            border-radius: 8px;
        }
        
        .chart-container {
            position: relative;
            height: 100%;
            min-height: 200px;
        }
        
        .value-header {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
            gap: 10px;
            padding: 12px;
            background: rgba(76, 76, 77, 0.3);
            border-radius: 6px;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 0.9rem;
            color: var(--pearl);
            border: 1px solid var(--princeton-orange);
        }
        
        .confidence-meter {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 8px;
        }
        
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--error-red), var(--princeton-orange), var(--success-green));
            transition: width 0.5s ease;
        }
        
        @media (max-width: 1200px) {
            .dashboard {
                grid-template-columns: 1fr 1fr;
                grid-template-areas:
                    "header header"
                    "player main"
                    "live analytics"
                    "value value";
            }
        }
        
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
                grid-template-areas:
                    "header"
                    "player"
                    "main"
                    "live"
                    "analytics"
                    "value";
            }
            
            .analytics-panel {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="panel header-panel">
            <div>
                <h1>🏀 ELITE WNBA PREDICTION DASHBOARD</h1>
                <div class="status-indicator">
                    <div class="status-dot status-live"></div>
                    <span id="status-text">LIVE MODE ACTIVE</span>
                </div>
            </div>
            <div class="controls">
                <button class="control-btn active" id="realtime-toggle">LIVE MODE</button>
                <button class="control-btn" id="simulation-mode">SIMULATION</button>
                <button class="control-btn" id="value-finder">FIND VALUE</button>
                <button class="control-btn" id="refresh-data">REFRESH</button>
            </div>
        </div>

        <div class="panel player-panel">
            <div class="player-card" data-player="alyssa-thomas">
                <div class="player-header">
                    <h3>A'ja Wilson</h3>
                    <div class="team-tag">LV</div>
                </div>
                <div class="stat-row">
                    <span>Points:</span>
                    <div class="stat-values">
                        <span class="predicted">22.8</span>
                        <span>|</span>
                        <span class="actual">24</span>
                        <div class="accuracy-indicator accuracy-high"></div>
                    </div>
                </div>
                <div class="meter">
                    <div class="prediction-bar" style="width: 76%"></div>
                    <div class="actual-bar" style="width: 80%"></div>
                </div>
                <div class="confidence-meter">
                    <div class="confidence-fill" style="width: 85%"></div>
                </div>

                <div class="stat-row">
                    <span>Rebounds:</span>
                    <div class="stat-values">
                        <span class="predicted">10.2</span>
                        <span>|</span>
                        <span class="actual">11</span>
                        <div class="accuracy-indicator accuracy-high"></div>
                    </div>
                </div>
                <div class="meter">
                    <div class="prediction-bar" style="width: 68%"></div>
                    <div class="actual-bar" style="width: 73%"></div>
                </div>
                <div class="confidence-meter">
                    <div class="confidence-fill" style="width: 78%"></div>
                </div>
            </div>

            <div class="player-card" data-player="breanna-stewart">
                <div class="player-header">
                    <h3>Breanna Stewart</h3>
                    <div class="team-tag">NYL</div>
                </div>
                <div class="stat-row">
                    <span>Points:</span>
                    <div class="stat-values">
                        <span class="predicted">20.5</span>
                        <span>|</span>
                        <span class="actual">18</span>
                        <div class="accuracy-indicator accuracy-medium"></div>
                    </div>
                </div>
                <div class="meter">
                    <div class="prediction-bar" style="width: 68%"></div>
                    <div class="actual-bar" style="width: 60%"></div>
                </div>
                <div class="confidence-meter">
                    <div class="confidence-fill" style="width: 72%"></div>
                </div>

                <div class="stat-row">
                    <span>Assists:</span>
                    <div class="stat-values">
                        <span class="predicted">3.8</span>
                        <span>|</span>
                        <span class="actual">5</span>
                        <div class="accuracy-indicator accuracy-medium"></div>
                    </div>
                </div>
                <div class="meter">
                    <div class="prediction-bar" style="width: 38%"></div>
                    <div class="actual-bar" style="width: 50%"></div>
                </div>
                <div class="confidence-meter">
                    <div class="confidence-fill" style="width: 65%"></div>
                </div>
            </div>

            <div class="player-card" data-player="sabrina-ionescu">
                <div class="player-header">
                    <h3>Sabrina Ionescu</h3>
                    <div class="team-tag">NYL</div>
                </div>
                <div class="stat-row">
                    <span>3-Pointers:</span>
                    <div class="stat-values">
                        <span class="predicted">2.8</span>
                        <span>|</span>
                        <span class="actual">3</span>
                        <div class="accuracy-indicator accuracy-high"></div>
                    </div>
                </div>
                <div class="meter">
                    <div class="prediction-bar" style="width: 56%"></div>
                    <div class="actual-bar" style="width: 60%"></div>
                </div>
                <div class="confidence-meter">
                    <div class="confidence-fill" style="width: 82%"></div>
                </div>
            </div>
        </div>

        <div class="panel main-panel">
            <h2>🏟️ Live Game Visualization</h2>
            <canvas id="game-visualization"></canvas>
        </div>

        <div class="panel live-panel">
            <h2>📡 Live Game Feed</h2>
            <div id="play-by-play" style="flex: 1; overflow-y: auto;">
                <div class="play-event highlight">
                    <strong>Q3 8:24</strong> - A'ja Wilson scores 15th point (OVER 14.5 ✅)
                </div>
                <div class="play-event">
                    <strong>Q3 7:58</strong> - B. Stewart 3-pointer (assist S. Ionescu)
                </div>
                <div class="play-event">
                    <strong>Q3 7:32</strong> - Defensive rebound by Wilson
                </div>
                <div class="play-event">
                    <strong>Q3 7:15</strong> - Ionescu 3rd three-pointer made
                </div>
                <div class="play-event">
                    <strong>Q3 6:58</strong> - Stewart defensive rebound
                </div>
            </div>
            <div class="adjustment-alert">
                <span>⚡ Model adjustment: Pace increased +8%</span>
                <button class="control-btn">RECALCULATE</button>
            </div>
        </div>

        <div class="panel analytics-panel">
            <div class="chart-container">
                <h2>📉 Performance Trends</h2>
                <canvas id="performance-chart"></canvas>
            </div>
            <div class="chart-container">
                <h2>🎯 Win Probability</h2>
                <canvas id="win-probability-chart"></canvas>
            </div>
            <div class="chart-container">
                <h2>⚡ Fatigue Impact</h2>
                <canvas id="fatigue-chart"></canvas>
            </div>
        </div>

        <div class="panel value-panel">
            <h2>💰 Value Spot Finder</h2>
            <div class="value-header">
                <div>Player</div>
                <div>Prop</div>
                <div>Book Line</div>
                <div>Our Line</div>
                <div>Edge</div>
            </div>
            <div class="value-row high-value">
                <div>A'ja Wilson</div>
                <div>Points O/U</div>
                <div>22.5</div>
                <div>24.2</div>
                <div class="edge-positive">+7.6%</div>
            </div>
            <div class="value-row high-value">
                <div>S. Ionescu</div>
                <div>3-Pointers</div>
                <div>2.5</div>
                <div>3.1</div>
                <div class="edge-positive">+24%</div>
            </div>
            <div class="value-row">
                <div>B. Stewart</div>
                <div>Rebounds</div>
                <div>8.5</div>
                <div>8.2</div>
                <div class="edge-negative">-3.5%</div>
            </div>
            <div class="value-row negative-value">
                <div>J. Jones</div>
                <div>Points</div>
                <div>16.5</div>
                <div>14.8</div>
                <div class="edge-negative">-10.3%</div>
            </div>
            <div class="value-row high-value">
                <div>K. Plum</div>
                <div>Assists</div>
                <div>4.5</div>
                <div>5.2</div>
                <div class="edge-positive">+15.6%</div>
            </div>
        </div>
    </div>

    <script>
        // Enhanced Dashboard Controller
        class EliteWNBADashboard {
            constructor() {
                this.isLiveMode = true;
                this.updateInterval = null;
                this.charts = {};
                this.gameData = {
                    players: new Map(),
                    gameState: 'live',
                    quarter: 3,
                    timeRemaining: '8:24'
                };

                this.init();
            }

            init() {
                this.setupCanvas();
                this.initializeCharts();
                this.setupEventListeners();
                this.startLiveUpdates();

                console.log('🏀 Elite WNBA Dashboard initialized');
            }

            setupCanvas() {
                const canvas = document.getElementById('game-visualization');
                const ctx = canvas.getContext('2d');

                const resizeCanvas = () => {
                    const container = canvas.parentElement;
                    canvas.width = container.clientWidth;
                    canvas.height = container.clientHeight - 60;
                    this.renderCourt();
                };

                window.addEventListener('resize', resizeCanvas);
                resizeCanvas();
            }

            renderCourt() {
                const canvas = document.getElementById('game-visualization');
                const ctx = canvas.getContext('2d');

                if (!canvas.width) return;

                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Court background
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, 'rgba(239, 227, 198, 0.1)');
                gradient.addColorStop(1, 'rgba(76, 76, 77, 0.05)');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Court outline
                ctx.strokeStyle = 'rgba(239, 227, 198, 0.6)';
                ctx.lineWidth = 2;
                ctx.strokeRect(30, 30, canvas.width - 60, canvas.height - 60);

                // Half court line
                ctx.beginPath();
                ctx.moveTo(canvas.width / 2, 30);
                ctx.lineTo(canvas.width / 2, canvas.height - 30);
                ctx.stroke();

                // Center circle
                ctx.beginPath();
                ctx.arc(canvas.width / 2, canvas.height / 2, 40, 0, Math.PI * 2);
                ctx.stroke();

                // Three-point lines (simplified)
                ctx.beginPath();
                ctx.arc(canvas.width * 0.25, canvas.height / 2, 60, -Math.PI/2, Math.PI/2);
                ctx.stroke();

                ctx.beginPath();
                ctx.arc(canvas.width * 0.75, canvas.height / 2, 60, Math.PI/2, 3*Math.PI/2);
                ctx.stroke();

                // Player positions with enhanced visualization
                const players = [
                    { name: 'Wilson', x: canvas.width * 0.3, y: canvas.height * 0.4, team: 'LV', hot: true },
                    { name: 'Stewart', x: canvas.width * 0.7, y: canvas.height * 0.6, team: 'NYL', hot: false },
                    { name: 'Ionescu', x: canvas.width * 0.8, y: canvas.height * 0.3, team: 'NYL', hot: true },
                    { name: 'Plum', x: canvas.width * 0.2, y: canvas.height * 0.7, team: 'LV', hot: false }
                ];

                players.forEach(player => {
                    // Player circle with glow effect for hot players
                    if (player.hot) {
                        ctx.shadowColor = '#F57B20';
                        ctx.shadowBlur = 15;
                    }

                    ctx.fillStyle = player.team === 'LV' ? 'rgba(245, 123, 32, 0.9)' : 'rgba(76, 76, 77, 0.9)';
                    ctx.beginPath();
                    ctx.arc(player.x, player.y, 18, 0, Math.PI * 2);
                    ctx.fill();

                    // Reset shadow
                    ctx.shadowBlur = 0;

                    // Player name
                    ctx.fillStyle = '#EFE3C6';
                    ctx.font = 'bold 11px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(player.name, player.x, player.y + 30);

                    // Performance indicator
                    if (player.hot) {
                        ctx.fillStyle = '#F57B20';
                        ctx.font = 'bold 8px Arial';
                        ctx.fillText('🔥', player.x, player.y - 25);
                    }
                });
            }

            initializeCharts() {
                // Performance Trends Chart
                const perfCtx = document.getElementById('performance-chart').getContext('2d');
                this.charts.performance = new Chart(perfCtx, {
                    type: 'line',
                    data: {
                        labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                        datasets: [{
                            label: 'Prediction Accuracy',
                            data: [85, 88, 92, 89],
                            borderColor: '#F57B20',
                            backgroundColor: 'rgba(245, 123, 32, 0.15)',
                            tension: 0.4,
                            fill: true,
                            pointBackgroundColor: '#F57B20',
                            pointBorderColor: '#EFE3C6',
                            pointBorderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false }
                        },
                        scales: {
                            y: {
                                min: 70,
                                max: 100,
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#fff' }
                            },
                            x: {
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#fff' }
                            }
                        }
                    }
                });

                // Win Probability Chart
                const winCtx = document.getElementById('win-probability-chart').getContext('2d');
                this.charts.winProbability = new Chart(winCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Liberty', 'Aces'],
                        datasets: [{
                            data: [58, 42],
                            backgroundColor: ['rgba(76, 76, 77, 0.8)', 'rgba(245, 123, 32, 0.8)'],
                            borderWidth: 0,
                            hoverOffset: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        cutout: '70%',
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: { color: '#fff' }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return context.label + ': ' + context.parsed + '%';
                                    }
                                }
                            }
                        }
                    }
                });

                // Fatigue Impact Chart
                const fatigueCtx = document.getElementById('fatigue-chart').getContext('2d');
                this.charts.fatigue = new Chart(fatigueCtx, {
                    type: 'bar',
                    data: {
                        labels: ['Wilson', 'Stewart', 'Ionescu', 'Plum'],
                        datasets: [{
                            label: 'Energy Level',
                            data: [78, 65, 82, 71],
                            backgroundColor: [
                                'rgba(76, 175, 80, 0.8)',
                                'rgba(245, 123, 32, 0.8)',
                                'rgba(76, 175, 80, 0.8)',
                                'rgba(245, 123, 32, 0.8)'
                            ],
                            borderColor: [
                                'rgba(76, 175, 80, 1)',
                                'rgba(245, 123, 32, 1)',
                                'rgba(76, 175, 80, 1)',
                                'rgba(245, 123, 32, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false }
                        },
                        scales: {
                            y: {
                                min: 0,
                                max: 100,
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#fff' }
                            },
                            x: {
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#fff' }
                            }
                        }
                    }
                });
            }

            setupEventListeners() {
                // Control buttons
                document.getElementById('realtime-toggle').addEventListener('click', () => {
                    this.toggleLiveMode();
                });

                document.getElementById('simulation-mode').addEventListener('click', () => {
                    this.enterSimulationMode();
                });

                document.getElementById('value-finder').addEventListener('click', () => {
                    this.highlightValueBets();
                });

                document.getElementById('refresh-data').addEventListener('click', () => {
                    this.refreshAllData();
                });

                // Player card interactions
                document.querySelectorAll('.player-card').forEach(card => {
                    card.addEventListener('click', (e) => {
                        this.selectPlayer(card.dataset.player);
                    });
                });
            }

            toggleLiveMode() {
                this.isLiveMode = !this.isLiveMode;
                const btn = document.getElementById('realtime-toggle');
                const statusText = document.getElementById('status-text');
                const statusDot = document.querySelector('.status-dot');

                if (this.isLiveMode) {
                    btn.classList.add('active');
                    btn.textContent = 'LIVE MODE';
                    statusText.textContent = 'LIVE MODE ACTIVE';
                    statusDot.className = 'status-dot status-live';
                    this.startLiveUpdates();
                } else {
                    btn.classList.remove('active');
                    btn.textContent = 'PAUSED';
                    statusText.textContent = 'LIVE MODE PAUSED';
                    statusDot.className = 'status-dot status-offline';
                    this.stopLiveUpdates();
                }
            }

            enterSimulationMode() {
                this.isLiveMode = false;
                this.stopLiveUpdates();

                const statusText = document.getElementById('status-text');
                const statusDot = document.querySelector('.status-dot');

                statusText.textContent = 'SIMULATION MODE';
                statusDot.className = 'status-dot status-simulation';

                // Update button states
                document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
                document.getElementById('simulation-mode').classList.add('active');

                this.runSimulation();
            }

            highlightValueBets() {
                const valueRows = document.querySelectorAll('.value-row');
                valueRows.forEach(row => {
                    row.style.animation = 'none';
                    setTimeout(() => {
                        row.style.animation = 'pulse 1s ease-in-out';
                    }, 10);
                });

                // Flash the value panel
                const valuePanel = document.querySelector('.value-panel');
                valuePanel.style.borderColor = 'rgba(76, 175, 80, 0.8)';
                setTimeout(() => {
                    valuePanel.style.borderColor = 'rgba(200, 16, 46, 0.3)';
                }, 2000);
            }

            refreshAllData() {
                const btn = document.getElementById('refresh-data');
                btn.textContent = 'REFRESHING...';
                btn.disabled = true;

                // Simulate data refresh
                setTimeout(() => {
                    this.updatePlayerStats();
                    this.updateCharts();
                    this.updatePlayByPlay();

                    btn.textContent = 'REFRESH';
                    btn.disabled = false;
                }, 1500);
            }

            startLiveUpdates() {
                if (this.updateInterval) {
                    clearInterval(this.updateInterval);
                }

                this.updateInterval = setInterval(() => {
                    if (this.isLiveMode) {
                        this.updatePlayerStats();
                        this.updateCharts();
                        this.updatePlayByPlay();
                        this.renderCourt();
                    }
                }, 3000); // Update every 3 seconds
            }

            stopLiveUpdates() {
                if (this.updateInterval) {
                    clearInterval(this.updateInterval);
                    this.updateInterval = null;
                }
            }

            updatePlayerStats() {
                document.querySelectorAll('.actual').forEach(el => {
                    const current = parseFloat(el.textContent) || 0;
                    const change = (Math.random() - 0.5) * 0.5; // Small random changes
                    const newValue = Math.max(0, current + change);
                    el.textContent = newValue.toFixed(1);
                });

                // Update accuracy indicators
                document.querySelectorAll('.accuracy-indicator').forEach(indicator => {
                    const accuracy = Math.random();
                    indicator.className = 'accuracy-indicator ' +
                        (accuracy > 0.8 ? 'accuracy-high' :
                         accuracy > 0.6 ? 'accuracy-medium' : 'accuracy-low');
                });

                // Update confidence meters
                document.querySelectorAll('.confidence-fill').forEach(fill => {
                    const confidence = 60 + Math.random() * 30; // 60-90%
                    fill.style.width = confidence + '%';
                });
            }

            updateCharts() {
                // Update performance chart
                if (this.charts.performance) {
                    const newData = this.charts.performance.data.datasets[0].data.map(
                        val => Math.max(70, Math.min(100, val + (Math.random() - 0.5) * 3))
                    );
                    this.charts.performance.data.datasets[0].data = newData;
                    this.charts.performance.update('none');
                }

                // Update win probability
                if (this.charts.winProbability) {
                    const change = (Math.random() - 0.5) * 4;
                    const liberty = Math.max(20, Math.min(80, this.charts.winProbability.data.datasets[0].data[0] + change));
                    this.charts.winProbability.data.datasets[0].data = [liberty, 100 - liberty];
                    this.charts.winProbability.update('none');
                }

                // Update fatigue chart
                if (this.charts.fatigue) {
                    const newFatigue = this.charts.fatigue.data.datasets[0].data.map(
                        val => Math.max(40, Math.min(100, val + (Math.random() - 0.5) * 5))
                    );
                    this.charts.fatigue.data.datasets[0].data = newFatigue;
                    this.charts.fatigue.update('none');
                }
            }

            updatePlayByPlay() {
                const events = [
                    "Wilson scores inside (19th point)",
                    "Stewart defensive rebound",
                    "Ionescu 3-pointer attempt (missed)",
                    "Plum assist to Gray",
                    "Technical foul called",
                    "Timeout called by Liberty",
                    "Wilson reaches 20 points (OVER 19.5 ✅)",
                    "Stewart blocks shot attempt"
                ];

                const playList = document.getElementById('play-by-play');
                const randomEvent = events[Math.floor(Math.random() * events.length)];
                const minutes = Math.floor(Math.random() * 12);
                const seconds = Math.floor(Math.random() * 60);

                const newEvent = document.createElement('div');
                newEvent.className = 'play-event';
                if (randomEvent.includes('✅')) {
                    newEvent.classList.add('highlight');
                }
                newEvent.innerHTML = `<strong>Q3 ${minutes}:${seconds.toString().padStart(2, '0')}</strong> - ${randomEvent}`;

                playList.insertBefore(newEvent, playList.firstChild);

                // Keep only 8 most recent events
                while (playList.children.length > 8) {
                    playList.removeChild(playList.lastChild);
                }
            }

            runSimulation() {
                // Simulation mode - faster updates with projected outcomes
                let simStep = 0;
                const simInterval = setInterval(() => {
                    this.updatePlayerStats();
                    this.updateCharts();

                    simStep++;
                    if (simStep >= 10) { // Run simulation for 10 steps
                        clearInterval(simInterval);

                        // Show simulation results
                        const alert = document.querySelector('.adjustment-alert span');
                        alert.textContent = '🎯 Simulation complete: 73% win probability for Liberty';
                    }
                }, 500);
            }

            selectPlayer(playerId) {
                // Highlight selected player
                document.querySelectorAll('.player-card').forEach(card => {
                    card.style.borderLeftColor = '#C8102E';
                });

                const selectedCard = document.querySelector(`[data-player="${playerId}"]`);
                if (selectedCard) {
                    selectedCard.style.borderLeftColor = '#FFB81C';
                }

                console.log(`Selected player: ${playerId}`);
            }
        }

        // Initialize dashboard when page loads
        document.addEventListener('DOMContentLoaded', () => {
            window.dashboard = new EliteWNBADashboard();
        });

        // Performance monitoring
        window.addEventListener('load', () => {
            console.log('🏀 Elite WNBA Dashboard loaded successfully');
            console.log('📊 Performance metrics:', {
                loadTime: performance.now(),
                memory: performance.memory ? performance.memory.usedJSHeapSize : 'N/A'
            });
        });
    </script>
</body>
</html>
