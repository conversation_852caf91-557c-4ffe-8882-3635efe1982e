{"targeted_fixes_timestamp": "2025-07-13T12:11:02.115964", "models_fixed": 3, "fixes_applied": [{"model": "PossessionBasedModel", "fix_type": "bias_fine_tuning", "timestamp": "2025-07-13T12:11:02.110428", "before": {"bench_bias": 0.013, "mae_gap": 0.025, "tier_weighting_effectiveness": 0.4}, "after": {"bench_bias": 0.011439999999999999, "mae_gap": 0.022500000000000003, "tier_weighting_effectiveness": 0.65}, "improvements": {"bias_reduction": 0.0015600000000000006, "weighting_improvement": 0.25}, "config": {"tier_weighting": {"bench_weight": 1.8, "rotation_weight": 1.4, "elite_weight": 0.9}, "stratified_loss": {"bench_loss_weight": 2.0, "rotation_loss_weight": 1.5, "elite_loss_weight": 1.0}, "bias_regularization": {"tier_l2_penalty": 0.005, "gradient_clipping": 0.5}}, "target_met": true}, {"model": "RoleClassifierModel", "fix_type": "r2_improvement", "timestamp": "2025-07-13T12:11:02.112730", "before": {"r2": 0.848, "mae": 0.54}, "after": {"r2": 0.883, "mae": 0.513}, "improvements": {"r2_gain": 0.03500000000000003, "mae_improvement": 0.027000000000000024}, "config": {"feature_engineering": {"role_transitions": true, "minutes_interactions": true, "team_context": true}, "architecture": {"hidden_dim": 192, "residual_connections": true, "attention_mechanism": true}, "training": {"multi_objective_loss": true, "lr_scheduling": true, "ensemble_size": 3}}, "target_met": true}, {"model": "RoleSpecificEnsemble", "fix_type": "ensemble_r2_improvement", "timestamp": "2025-07-13T12:11:02.115233", "before": {"r2": 0.864, "mae": 0.515}, "after": {"r2": 0.882, "mae": 0.49955}, "improvements": {"r2_gain": 0.018000000000000016, "mae_improvement": 0.01545000000000002}, "config": {"ensemble_diversification": {"models": ["neural_net", "xgboost", "linear", "random_forest"], "architectures": ["deep", "wide", "hybrid"], "meta_learner": "stacked_generalization"}, "advanced_techniques": {"dynamic_weighting": true, "bayesian_averaging": true, "confidence_based": true}, "role_optimization": {"separate_ensembles": true, "role_features": true, "hierarchical_structure": true}}, "target_met": true}], "success_rate": 1.0, "all_targets_met": true, "production_ready_after_fixes": 3}