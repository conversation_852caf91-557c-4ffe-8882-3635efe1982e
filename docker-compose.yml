version: '3.8'

services:
  wnba-prediction:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=****************************************/wnba_db
    depends_on:
      - postgres
      - redis
    volumes:
      - ./data:/app/data
      - ./models:/app/models
    restart: unless-stopped

  federated-server:
    build:
      context: .
      dockerfile: Dockerfile.federated
    ports:
      - "8081:8080"
    environment:
      - ENVIRONMENT=production
      - FEDERATED_ROUNDS=50
    restart: unless-stopped

  monitoring-dashboard:
    build:
      context: .
      dockerfile: Dockerfile.dashboard
    ports:
      - "8083:8083"
    environment:
      - ENVIRONMENT=production
    restart: unless-stopped

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=wnba_db
      - POSTGRES_USER=wnba
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - wnba-prediction
      - federated-server
      - monitoring-dashboard

volumes:
  postgres_data:
  redis_data: