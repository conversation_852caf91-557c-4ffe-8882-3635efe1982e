2025-07-13 03:35:24,841 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 03:35:24,841 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 03:35:24,841 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 03:35:24,841 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 03:35:24,841 - __main__ - INFO - 🎮 Fantasy Training Integrator initialized
2025-07-13 03:35:24,842 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 03:35:24,842 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 03:35:24,843 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 03:35:24,843 - __main__ - INFO - 🎮 Fantasy System Integrator initialized
2025-07-13 03:35:24,844 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 03:35:24,844 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 03:35:24,844 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 03:35:24,844 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 03:35:24,845 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 03:35:24,845 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 03:35:24,845 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 03:35:24,845 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 03:35:24,845 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_033524
2025-07-13 03:35:24,845 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 03:35:24,845 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 03:35:24,845 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 03:35:24,845 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 03:35:29,964 - __main__ - INFO - WNBA data collection successful
2025-07-13 03:35:29,964 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 03:35:29,965 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 03:35:31,044 - __main__ - INFO - Historical injury data collection successful
2025-07-13 03:35:31,044 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 03:35:31,811 - __main__ - INFO - Odds data collection successful
2025-07-13 03:35:31,811 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 03:35:31,811 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 03:35:31,812 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 03:35:32,348 - __main__ - ERROR - ❌ Feature engineering failed: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 544, in <module>
    success = main()
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 500, in main
    print("\U0001f3c0 WNBA EXPERT DATASET CONSOLIDATION")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f3c0' in position 0: character maps to <undefined>

2025-07-13 03:35:32,349 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 03:35:32,349 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 03:35:32,349 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 03:35:32,349 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 03:35:32,349 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 03:35:32,349 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 03:35:32,350 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 03:35:32,350 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 03:35:32,350 - __main__ - INFO - 🧠 Training MultiTaskPlayerModel with expert config...
2025-07-13 03:35:32,350 - __main__ - INFO - ✅ Expert mapping applied to MultiTaskPlayerModel
2025-07-13 03:35:32,350 - __main__ - INFO - ✅ MultiTaskPlayerModel trained with 120 epochs
2025-07-13 03:35:32,350 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 03:35:32,351 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 03:35:32,351 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 03:35:32,351 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 03:35:32,351 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 03:35:32,351 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 03:35:32,352 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 03:35:32,352 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 120, 150, 100]
2025-07-13 03:35:32,352 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 03:35:32,352 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 03:35:32,352 - __main__ - INFO - 🌌 Training 10 multiverse models...
2025-07-13 03:35:32,352 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 03:35:32,353 - __main__ - INFO - 🎮 Training PossessionBasedModel with fantasy penalty scoring...
2025-07-13 03:35:32,353 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 03:35:32,353 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 03:35:32,353 - __main__ - INFO - 🎮 Training LineupChemistryModel with fantasy penalty scoring...
2025-07-13 03:35:32,353 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 03:35:32,354 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 03:35:32,354 - __main__ - INFO - 🎮 Training CumulativeFatigueModel with fantasy penalty scoring...
2025-07-13 03:35:32,354 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 03:35:32,354 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 03:35:32,354 - __main__ - INFO - 🎮 Training HighLeverageModel with fantasy penalty scoring...
2025-07-13 03:35:32,354 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 03:35:32,354 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 03:35:32,354 - __main__ - INFO - 🎮 Training TeamDynamicsModel with fantasy penalty scoring...
2025-07-13 03:35:32,354 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 03:35:32,355 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 03:35:32,355 - __main__ - INFO - 🎮 Training ContextualPerformanceModel with fantasy penalty scoring...
2025-07-13 03:35:32,355 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 03:35:32,355 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 03:35:32,355 - __main__ - INFO - 🎮 Training InjuryImpactModel with fantasy penalty scoring...
2025-07-13 03:35:32,355 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 03:35:32,356 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 03:35:32,356 - __main__ - INFO - 🎮 Training CoachingStyleModel with fantasy penalty scoring...
2025-07-13 03:35:32,357 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 03:35:32,357 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 03:35:32,358 - __main__ - INFO - 🎮 Training ArenaEffectModel with fantasy penalty scoring...
2025-07-13 03:35:32,358 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 03:35:32,358 - __main__ - INFO - 🌌 Training WeatherImpactModel...
2025-07-13 03:35:32,358 - __main__ - INFO - 🎮 Training WeatherImpactModel with fantasy penalty scoring...
2025-07-13 03:35:32,358 - __main__ - INFO - ✅ WeatherImpactModel training completed
2025-07-13 03:35:32,358 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 03:35:32,359 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 03:35:32,359 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 03:35:32,359 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 03:35:32,359 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 03:35:32,359 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ Fantasy system integration completed
2025-07-13 03:35:32,359 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 03:35:32,360 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (15 models)
2025-07-13 03:35:32,360 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 03:35:32,360 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 03:35:32,360 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 03:35:32,360 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 03:35:32,360 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022], Val [2023], Test [2024, 2025]
2025-07-13 03:35:38,147 - __main__ - ERROR - ❌ Enhanced federated learning error: Expert federated training failed: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 44, in <module>
    from src.models.modern_player_points_model import (
        PlayerPointsModel, FederatedPlayerModel, WNBADataModule
    )
  File "C:\Users\<USER>\Documents\wnba\src\models\modern_player_points_model.py", line 43, in <module>
    print("\u2705 FIXED Real Injury System imported - NO MOCK DATA")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>

2025-07-13 03:35:38,147 - __main__ - INFO - 🎮 Stage 6: Fantasy System Integration
2025-07-13 03:35:38,148 - __main__ - INFO - 🎮 Starting fantasy system integration...
2025-07-13 03:35:38,148 - __main__ - INFO - 🎮 Integrating dfs fantasy system...
2025-07-13 03:35:38,148 - __main__ - INFO - 🎮 Integrating season_long fantasy system...
2025-07-13 03:35:38,148 - __main__ - INFO - 🎮 Integrating best_ball fantasy system...
2025-07-13 03:35:38,148 - __main__ - INFO - ✅ Fantasy system integration successful
2025-07-13 03:35:38,148 - __main__ - INFO - 🔍 Stage 7: Quality Assurance & Testing
2025-07-13 03:35:38,148 - __main__ - INFO - 🔍 Starting ENHANCED comprehensive quality assurance...
2025-07-13 03:35:38,148 - __main__ - INFO - 🔍 Running test_complete_federated_multiverse_integration.py...
2025-07-13 03:35:40,066 - __main__ - WARNING - ⚠️ test_complete_federated_multiverse_integration.py FAILED
2025-07-13 03:35:40,066 - __main__ - INFO - 🔍 Running test_enhanced_model_last_7_days.py...
2025-07-13 03:35:45,547 - __main__ - WARNING - ⚠️ test_enhanced_model_last_7_days.py FAILED
2025-07-13 03:35:45,548 - __main__ - INFO - 🔍 Running test_expert_autopilot_implementation.py...
2025-07-13 03:35:47,539 - __main__ - WARNING - ⚠️ test_expert_autopilot_implementation.py FAILED
2025-07-13 03:35:47,540 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 03:35:47,540 - __main__ - WARNING - ⚠️ Quality assurance below threshold: 0.000
2025-07-13 03:35:47,540 - __main__ - INFO - 🚀 Stage 8: Deployment & Monitoring
2025-07-13 03:35:47,540 - __main__ - INFO - 🚀 Starting automated model deployment...
2025-07-13 03:35:47,540 - __main__ - WARNING - ⚠️ Skipping deployment - QA failed
2025-07-13 03:35:47,540 - __main__ - INFO - ✅ Pipeline run run_20250713_033524 completed successfully in 22.7s
2025-07-13 03:35:47,545 - __main__ - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_033524.json
2025-07-13 03:35:47,545 - __main__ - INFO - 🚀 Starting automated training scheduler...
2025-07-13 03:35:47,546 - __main__ - INFO - ✅ Automated scheduler started successfully
2025-07-13 03:42:17,264 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 03:42:17,265 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 03:42:17,265 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 03:42:17,265 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 03:42:17,266 - __main__ - INFO - 🎮 Fantasy Training Integrator initialized
2025-07-13 03:42:17,266 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 03:42:17,267 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 03:42:17,267 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 03:42:17,268 - __main__ - INFO - 🎮 Fantasy System Integrator initialized
2025-07-13 03:42:17,268 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 03:42:17,269 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 03:42:17,269 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 03:42:17,270 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 03:42:17,271 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 03:42:17,271 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 03:42:17,272 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 03:42:17,272 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 03:42:17,273 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_034217
2025-07-13 03:42:17,273 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 03:42:17,274 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 03:42:17,274 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 03:42:17,275 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 03:42:23,067 - __main__ - INFO - WNBA data collection successful
2025-07-13 03:42:23,068 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 03:42:23,068 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 03:42:24,269 - __main__ - INFO - Historical injury data collection successful
2025-07-13 03:42:24,269 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 03:42:24,814 - __main__ - INFO - Odds data collection successful
2025-07-13 03:42:24,814 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 03:42:24,814 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 03:42:24,815 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 03:42:25,475 - __main__ - ERROR - ❌ Feature engineering failed: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 544, in <module>
    success = main()
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 502, in main
    print("\U0001f4ca Current situation:")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4ca' in position 0: character maps to <undefined>

2025-07-13 03:42:25,475 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 03:42:25,475 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 03:42:25,476 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 03:42:25,476 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 03:42:25,476 - __main__ - INFO - 🧠 Training MultiTaskPlayerModel with expert config...
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert mapping applied to MultiTaskPlayerModel
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ MultiTaskPlayerModel trained with 120 epochs
2025-07-13 03:42:25,476 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 03:42:25,476 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 03:42:25,476 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 120, 150, 100]
2025-07-13 03:42:25,476 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 03:42:25,477 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 03:42:25,477 - __main__ - INFO - 🌌 Training 10 multiverse models...
2025-07-13 03:42:25,477 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 03:42:25,477 - __main__ - INFO - 🎮 Training PossessionBasedModel with fantasy penalty scoring...
2025-07-13 03:42:25,477 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 03:42:25,477 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 03:42:25,477 - __main__ - INFO - 🎮 Training LineupChemistryModel with fantasy penalty scoring...
2025-07-13 03:42:25,478 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 03:42:25,478 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 03:42:25,478 - __main__ - INFO - 🎮 Training CumulativeFatigueModel with fantasy penalty scoring...
2025-07-13 03:42:25,478 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 03:42:25,478 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 03:42:25,479 - __main__ - INFO - 🎮 Training HighLeverageModel with fantasy penalty scoring...
2025-07-13 03:42:25,479 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 03:42:25,479 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 03:42:25,479 - __main__ - INFO - 🎮 Training TeamDynamicsModel with fantasy penalty scoring...
2025-07-13 03:42:25,480 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 03:42:25,480 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 03:42:25,480 - __main__ - INFO - 🎮 Training ContextualPerformanceModel with fantasy penalty scoring...
2025-07-13 03:42:25,480 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 03:42:25,480 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 03:42:25,480 - __main__ - INFO - 🎮 Training InjuryImpactModel with fantasy penalty scoring...
2025-07-13 03:42:25,480 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 03:42:25,480 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 03:42:25,481 - __main__ - INFO - 🎮 Training CoachingStyleModel with fantasy penalty scoring...
2025-07-13 03:42:25,481 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 03:42:25,481 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 03:42:25,481 - __main__ - INFO - 🎮 Training ArenaEffectModel with fantasy penalty scoring...
2025-07-13 03:42:25,481 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 03:42:25,481 - __main__ - INFO - 🌌 Training WeatherImpactModel...
2025-07-13 03:42:25,481 - __main__ - INFO - 🎮 Training WeatherImpactModel with fantasy penalty scoring...
2025-07-13 03:42:25,481 - __main__ - INFO - ✅ WeatherImpactModel training completed
2025-07-13 03:42:25,481 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 03:42:25,481 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 03:42:25,482 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 03:42:25,482 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 03:42:25,482 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 03:42:25,482 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 03:42:25,482 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 03:42:25,482 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 03:42:25,482 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 03:42:25,482 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 03:42:25,483 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 03:42:25,483 - __main__ - INFO - ✅ Fantasy system integration completed
2025-07-13 03:42:25,483 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 03:42:25,483 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 03:42:25,483 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (15 models)
2025-07-13 03:42:25,484 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 03:42:25,484 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 03:42:25,484 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 03:42:25,485 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 03:42:25,486 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022], Val [2023], Test [2024, 2025]
2025-07-13 03:42:35,835 - __main__ - ERROR - ❌ Enhanced federated learning error: Expert federated training failed: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 44, in <module>
    from src.models.modern_player_points_model import (
        PlayerPointsModel, FederatedPlayerModel, WNBADataModule
    )
  File "C:\Users\<USER>\Documents\wnba\src\models\modern_player_points_model.py", line 2323, in <module>
    print("\u2705 torch_geometric available - GNN features enabled")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>

2025-07-13 03:42:35,836 - __main__ - INFO - 🎮 Stage 6: Fantasy System Integration
2025-07-13 03:42:35,837 - __main__ - INFO - 🎮 Starting fantasy system integration...
2025-07-13 03:42:35,837 - __main__ - INFO - 🎮 Integrating dfs fantasy system...
2025-07-13 03:42:35,838 - __main__ - INFO - 🎮 Integrating season_long fantasy system...
2025-07-13 03:42:35,839 - __main__ - INFO - 🎮 Integrating best_ball fantasy system...
2025-07-13 03:42:35,839 - __main__ - INFO - ✅ Fantasy system integration successful
2025-07-13 03:42:35,840 - __main__ - INFO - 🔍 Stage 7: Quality Assurance & Testing
2025-07-13 03:42:35,841 - __main__ - INFO - 🔍 Starting ENHANCED comprehensive quality assurance...
2025-07-13 03:42:35,841 - __main__ - INFO - 🔍 Running test_complete_federated_multiverse_integration.py...
2025-07-13 03:42:35,914 - __main__ - WARNING - ⚠️ test_complete_federated_multiverse_integration.py FAILED
2025-07-13 03:42:35,915 - __main__ - INFO - 🔍 Running test_enhanced_model_last_7_days.py...
2025-07-13 03:42:46,543 - __main__ - WARNING - ⚠️ test_enhanced_model_last_7_days.py FAILED
2025-07-13 03:42:46,544 - __main__ - INFO - 🔍 Running test_expert_autopilot_implementation.py...
2025-07-13 03:42:48,737 - __main__ - WARNING - ⚠️ test_expert_autopilot_implementation.py FAILED
2025-07-13 03:42:48,737 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 03:42:48,738 - __main__ - WARNING - ⚠️ Quality assurance below threshold: 0.000
2025-07-13 03:42:48,738 - __main__ - INFO - 🚀 Stage 8: Deployment & Monitoring
2025-07-13 03:42:48,738 - __main__ - INFO - 🚀 Starting automated model deployment...
2025-07-13 03:42:48,738 - __main__ - WARNING - ⚠️ Skipping deployment - QA failed
2025-07-13 03:42:48,739 - __main__ - INFO - ✅ Pipeline run run_20250713_034217 completed successfully in 31.5s
2025-07-13 03:42:48,744 - __main__ - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_034217.json
2025-07-13 03:42:48,745 - __main__ - INFO - 🚀 Starting automated training scheduler...
2025-07-13 03:42:48,745 - __main__ - INFO - ✅ Automated scheduler started successfully
