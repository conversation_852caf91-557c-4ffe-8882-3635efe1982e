2025-07-13 03:35:24,841 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 03:35:24,841 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 03:35:24,841 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 03:35:24,841 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 03:35:24,841 - __main__ - INFO - 🎮 Fantasy Training Integrator initialized
2025-07-13 03:35:24,842 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 03:35:24,842 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 03:35:24,843 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 03:35:24,843 - __main__ - INFO - 🎮 Fantasy System Integrator initialized
2025-07-13 03:35:24,844 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 03:35:24,844 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 03:35:24,844 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 03:35:24,844 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 03:35:24,845 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 03:35:24,845 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 03:35:24,845 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 03:35:24,845 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 03:35:24,845 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_033524
2025-07-13 03:35:24,845 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 03:35:24,845 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 03:35:24,845 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 03:35:24,845 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 03:35:29,964 - __main__ - INFO - WNBA data collection successful
2025-07-13 03:35:29,964 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 03:35:29,965 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 03:35:31,044 - __main__ - INFO - Historical injury data collection successful
2025-07-13 03:35:31,044 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 03:35:31,811 - __main__ - INFO - Odds data collection successful
2025-07-13 03:35:31,811 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 03:35:31,811 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 03:35:31,812 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 03:35:32,348 - __main__ - ERROR - ❌ Feature engineering failed: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 544, in <module>
    success = main()
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 500, in main
    print("\U0001f3c0 WNBA EXPERT DATASET CONSOLIDATION")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f3c0' in position 0: character maps to <undefined>

2025-07-13 03:35:32,349 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 03:35:32,349 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 03:35:32,349 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 03:35:32,349 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 03:35:32,349 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 03:35:32,349 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 03:35:32,350 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 03:35:32,350 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 03:35:32,350 - __main__ - INFO - 🧠 Training MultiTaskPlayerModel with expert config...
2025-07-13 03:35:32,350 - __main__ - INFO - ✅ Expert mapping applied to MultiTaskPlayerModel
2025-07-13 03:35:32,350 - __main__ - INFO - ✅ MultiTaskPlayerModel trained with 120 epochs
2025-07-13 03:35:32,350 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 03:35:32,351 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 03:35:32,351 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 03:35:32,351 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 03:35:32,351 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 03:35:32,351 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 03:35:32,352 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 03:35:32,352 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 120, 150, 100]
2025-07-13 03:35:32,352 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 03:35:32,352 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 03:35:32,352 - __main__ - INFO - 🌌 Training 10 multiverse models...
2025-07-13 03:35:32,352 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 03:35:32,353 - __main__ - INFO - 🎮 Training PossessionBasedModel with fantasy penalty scoring...
2025-07-13 03:35:32,353 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 03:35:32,353 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 03:35:32,353 - __main__ - INFO - 🎮 Training LineupChemistryModel with fantasy penalty scoring...
2025-07-13 03:35:32,353 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 03:35:32,354 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 03:35:32,354 - __main__ - INFO - 🎮 Training CumulativeFatigueModel with fantasy penalty scoring...
2025-07-13 03:35:32,354 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 03:35:32,354 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 03:35:32,354 - __main__ - INFO - 🎮 Training HighLeverageModel with fantasy penalty scoring...
2025-07-13 03:35:32,354 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 03:35:32,354 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 03:35:32,354 - __main__ - INFO - 🎮 Training TeamDynamicsModel with fantasy penalty scoring...
2025-07-13 03:35:32,354 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 03:35:32,355 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 03:35:32,355 - __main__ - INFO - 🎮 Training ContextualPerformanceModel with fantasy penalty scoring...
2025-07-13 03:35:32,355 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 03:35:32,355 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 03:35:32,355 - __main__ - INFO - 🎮 Training InjuryImpactModel with fantasy penalty scoring...
2025-07-13 03:35:32,355 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 03:35:32,356 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 03:35:32,356 - __main__ - INFO - 🎮 Training CoachingStyleModel with fantasy penalty scoring...
2025-07-13 03:35:32,357 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 03:35:32,357 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 03:35:32,358 - __main__ - INFO - 🎮 Training ArenaEffectModel with fantasy penalty scoring...
2025-07-13 03:35:32,358 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 03:35:32,358 - __main__ - INFO - 🌌 Training WeatherImpactModel...
2025-07-13 03:35:32,358 - __main__ - INFO - 🎮 Training WeatherImpactModel with fantasy penalty scoring...
2025-07-13 03:35:32,358 - __main__ - INFO - ✅ WeatherImpactModel training completed
2025-07-13 03:35:32,358 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 03:35:32,359 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 03:35:32,359 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 03:35:32,359 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 03:35:32,359 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 03:35:32,359 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ Fantasy system integration completed
2025-07-13 03:35:32,359 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 03:35:32,360 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (15 models)
2025-07-13 03:35:32,360 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 03:35:32,360 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 03:35:32,360 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 03:35:32,360 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 03:35:32,360 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022], Val [2023], Test [2024, 2025]
2025-07-13 03:35:38,147 - __main__ - ERROR - ❌ Enhanced federated learning error: Expert federated training failed: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 44, in <module>
    from src.models.modern_player_points_model import (
        PlayerPointsModel, FederatedPlayerModel, WNBADataModule
    )
  File "C:\Users\<USER>\Documents\wnba\src\models\modern_player_points_model.py", line 43, in <module>
    print("\u2705 FIXED Real Injury System imported - NO MOCK DATA")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>

2025-07-13 03:35:38,147 - __main__ - INFO - 🎮 Stage 6: Fantasy System Integration
2025-07-13 03:35:38,148 - __main__ - INFO - 🎮 Starting fantasy system integration...
2025-07-13 03:35:38,148 - __main__ - INFO - 🎮 Integrating dfs fantasy system...
2025-07-13 03:35:38,148 - __main__ - INFO - 🎮 Integrating season_long fantasy system...
2025-07-13 03:35:38,148 - __main__ - INFO - 🎮 Integrating best_ball fantasy system...
2025-07-13 03:35:38,148 - __main__ - INFO - ✅ Fantasy system integration successful
2025-07-13 03:35:38,148 - __main__ - INFO - 🔍 Stage 7: Quality Assurance & Testing
2025-07-13 03:35:38,148 - __main__ - INFO - 🔍 Starting ENHANCED comprehensive quality assurance...
2025-07-13 03:35:38,148 - __main__ - INFO - 🔍 Running test_complete_federated_multiverse_integration.py...
2025-07-13 03:35:40,066 - __main__ - WARNING - ⚠️ test_complete_federated_multiverse_integration.py FAILED
2025-07-13 03:35:40,066 - __main__ - INFO - 🔍 Running test_enhanced_model_last_7_days.py...
2025-07-13 03:35:45,547 - __main__ - WARNING - ⚠️ test_enhanced_model_last_7_days.py FAILED
2025-07-13 03:35:45,548 - __main__ - INFO - 🔍 Running test_expert_autopilot_implementation.py...
2025-07-13 03:35:47,539 - __main__ - WARNING - ⚠️ test_expert_autopilot_implementation.py FAILED
2025-07-13 03:35:47,540 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 03:35:47,540 - __main__ - WARNING - ⚠️ Quality assurance below threshold: 0.000
2025-07-13 03:35:47,540 - __main__ - INFO - 🚀 Stage 8: Deployment & Monitoring
2025-07-13 03:35:47,540 - __main__ - INFO - 🚀 Starting automated model deployment...
2025-07-13 03:35:47,540 - __main__ - WARNING - ⚠️ Skipping deployment - QA failed
2025-07-13 03:35:47,540 - __main__ - INFO - ✅ Pipeline run run_20250713_033524 completed successfully in 22.7s
2025-07-13 03:35:47,545 - __main__ - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_033524.json
2025-07-13 03:35:47,545 - __main__ - INFO - 🚀 Starting automated training scheduler...
2025-07-13 03:35:47,546 - __main__ - INFO - ✅ Automated scheduler started successfully
2025-07-13 03:42:17,264 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 03:42:17,265 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 03:42:17,265 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 03:42:17,265 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 03:42:17,266 - __main__ - INFO - 🎮 Fantasy Training Integrator initialized
2025-07-13 03:42:17,266 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 03:42:17,267 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 03:42:17,267 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 03:42:17,268 - __main__ - INFO - 🎮 Fantasy System Integrator initialized
2025-07-13 03:42:17,268 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 03:42:17,269 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 03:42:17,269 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 03:42:17,270 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 03:42:17,271 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 03:42:17,271 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 03:42:17,272 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 03:42:17,272 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 03:42:17,273 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_034217
2025-07-13 03:42:17,273 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 03:42:17,274 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 03:42:17,274 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 03:42:17,275 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 03:42:23,067 - __main__ - INFO - WNBA data collection successful
2025-07-13 03:42:23,068 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 03:42:23,068 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 03:42:24,269 - __main__ - INFO - Historical injury data collection successful
2025-07-13 03:42:24,269 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 03:42:24,814 - __main__ - INFO - Odds data collection successful
2025-07-13 03:42:24,814 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 03:42:24,814 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 03:42:24,815 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 03:42:25,475 - __main__ - ERROR - ❌ Feature engineering failed: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 544, in <module>
    success = main()
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 502, in main
    print("\U0001f4ca Current situation:")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4ca' in position 0: character maps to <undefined>

2025-07-13 03:42:25,475 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 03:42:25,475 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 03:42:25,476 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 03:42:25,476 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 03:42:25,476 - __main__ - INFO - 🧠 Training MultiTaskPlayerModel with expert config...
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert mapping applied to MultiTaskPlayerModel
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ MultiTaskPlayerModel trained with 120 epochs
2025-07-13 03:42:25,476 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 03:42:25,476 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 03:42:25,476 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 120, 150, 100]
2025-07-13 03:42:25,476 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 03:42:25,477 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 03:42:25,477 - __main__ - INFO - 🌌 Training 10 multiverse models...
2025-07-13 03:42:25,477 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 03:42:25,477 - __main__ - INFO - 🎮 Training PossessionBasedModel with fantasy penalty scoring...
2025-07-13 03:42:25,477 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 03:42:25,477 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 03:42:25,477 - __main__ - INFO - 🎮 Training LineupChemistryModel with fantasy penalty scoring...
2025-07-13 03:42:25,478 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 03:42:25,478 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 03:42:25,478 - __main__ - INFO - 🎮 Training CumulativeFatigueModel with fantasy penalty scoring...
2025-07-13 03:42:25,478 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 03:42:25,478 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 03:42:25,479 - __main__ - INFO - 🎮 Training HighLeverageModel with fantasy penalty scoring...
2025-07-13 03:42:25,479 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 03:42:25,479 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 03:42:25,479 - __main__ - INFO - 🎮 Training TeamDynamicsModel with fantasy penalty scoring...
2025-07-13 03:42:25,480 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 03:42:25,480 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 03:42:25,480 - __main__ - INFO - 🎮 Training ContextualPerformanceModel with fantasy penalty scoring...
2025-07-13 03:42:25,480 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 03:42:25,480 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 03:42:25,480 - __main__ - INFO - 🎮 Training InjuryImpactModel with fantasy penalty scoring...
2025-07-13 03:42:25,480 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 03:42:25,480 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 03:42:25,481 - __main__ - INFO - 🎮 Training CoachingStyleModel with fantasy penalty scoring...
2025-07-13 03:42:25,481 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 03:42:25,481 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 03:42:25,481 - __main__ - INFO - 🎮 Training ArenaEffectModel with fantasy penalty scoring...
2025-07-13 03:42:25,481 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 03:42:25,481 - __main__ - INFO - 🌌 Training WeatherImpactModel...
2025-07-13 03:42:25,481 - __main__ - INFO - 🎮 Training WeatherImpactModel with fantasy penalty scoring...
2025-07-13 03:42:25,481 - __main__ - INFO - ✅ WeatherImpactModel training completed
2025-07-13 03:42:25,481 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 03:42:25,481 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 03:42:25,482 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 03:42:25,482 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 03:42:25,482 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 03:42:25,482 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 03:42:25,482 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 03:42:25,482 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 03:42:25,482 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 03:42:25,482 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 03:42:25,483 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 03:42:25,483 - __main__ - INFO - ✅ Fantasy system integration completed
2025-07-13 03:42:25,483 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 03:42:25,483 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 03:42:25,483 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (15 models)
2025-07-13 03:42:25,484 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 03:42:25,484 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 03:42:25,484 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 03:42:25,485 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 03:42:25,486 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022], Val [2023], Test [2024, 2025]
2025-07-13 03:42:35,835 - __main__ - ERROR - ❌ Enhanced federated learning error: Expert federated training failed: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 44, in <module>
    from src.models.modern_player_points_model import (
        PlayerPointsModel, FederatedPlayerModel, WNBADataModule
    )
  File "C:\Users\<USER>\Documents\wnba\src\models\modern_player_points_model.py", line 2323, in <module>
    print("\u2705 torch_geometric available - GNN features enabled")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>

2025-07-13 03:42:35,836 - __main__ - INFO - 🎮 Stage 6: Fantasy System Integration
2025-07-13 03:42:35,837 - __main__ - INFO - 🎮 Starting fantasy system integration...
2025-07-13 03:42:35,837 - __main__ - INFO - 🎮 Integrating dfs fantasy system...
2025-07-13 03:42:35,838 - __main__ - INFO - 🎮 Integrating season_long fantasy system...
2025-07-13 03:42:35,839 - __main__ - INFO - 🎮 Integrating best_ball fantasy system...
2025-07-13 03:42:35,839 - __main__ - INFO - ✅ Fantasy system integration successful
2025-07-13 03:42:35,840 - __main__ - INFO - 🔍 Stage 7: Quality Assurance & Testing
2025-07-13 03:42:35,841 - __main__ - INFO - 🔍 Starting ENHANCED comprehensive quality assurance...
2025-07-13 03:42:35,841 - __main__ - INFO - 🔍 Running test_complete_federated_multiverse_integration.py...
2025-07-13 03:42:35,914 - __main__ - WARNING - ⚠️ test_complete_federated_multiverse_integration.py FAILED
2025-07-13 03:42:35,915 - __main__ - INFO - 🔍 Running test_enhanced_model_last_7_days.py...
2025-07-13 03:42:46,543 - __main__ - WARNING - ⚠️ test_enhanced_model_last_7_days.py FAILED
2025-07-13 03:42:46,544 - __main__ - INFO - 🔍 Running test_expert_autopilot_implementation.py...
2025-07-13 03:42:48,737 - __main__ - WARNING - ⚠️ test_expert_autopilot_implementation.py FAILED
2025-07-13 03:42:48,737 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 03:42:48,738 - __main__ - WARNING - ⚠️ Quality assurance below threshold: 0.000
2025-07-13 03:42:48,738 - __main__ - INFO - 🚀 Stage 8: Deployment & Monitoring
2025-07-13 03:42:48,738 - __main__ - INFO - 🚀 Starting automated model deployment...
2025-07-13 03:42:48,738 - __main__ - WARNING - ⚠️ Skipping deployment - QA failed
2025-07-13 03:42:48,739 - __main__ - INFO - ✅ Pipeline run run_20250713_034217 completed successfully in 31.5s
2025-07-13 03:42:48,744 - __main__ - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_034217.json
2025-07-13 03:42:48,745 - __main__ - INFO - 🚀 Starting automated training scheduler...
2025-07-13 03:42:48,745 - __main__ - INFO - ✅ Automated scheduler started successfully
2025-07-13 05:01:44,762 - __main__ - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 05:01:44,763 - __main__ - ERROR - ❌ CONFIGURATION ERROR: Found weather models for indoor sport: ['WeatherImpactModel']
2025-07-13 05:01:44,764 - __main__ - CRITICAL - A critical error occurred in main execution: Found weather models for indoor sport: ['WeatherImpactModel']
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\automated_training_pipeline.py", line 1091, in main
    pipeline = AutomatedTrainingPipeline()
  File "C:\Users\<USER>\Documents\wnba\automated_training_pipeline.py", line 809, in __init__
    self._validate_configuration()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\wnba\automated_training_pipeline.py", line 857, in _validate_configuration
    assert len(weather_models) == 0, f"Found weather models for indoor sport: {weather_models}"
           ^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: Found weather models for indoor sport: ['WeatherImpactModel']
2025-07-13 05:09:39,160 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 05:09:39,160 - automated_training_pipeline - ERROR - ❌ CONFIGURATION ERROR: Found weather models for indoor sport: ['WeatherImpactModel']
2025-07-13 05:12:08,717 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 05:12:08,717 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 05:12:08,717 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - 🔧 Initializing pipeline components...
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - 📊 Data Collection Component initialized
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - 🚀 Deployment Manager initialized
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - ✅ All pipeline components initialized successfully
2025-07-13 05:14:00,408 - __main__ - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 05:14:00,410 - __main__ - INFO - ✅ Pipeline configuration validation passed
2025-07-13 05:14:00,410 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 05:14:00,412 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 05:14:00,413 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 05:14:00,413 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 05:14:00,413 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 05:14:00,414 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 05:14:00,415 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 05:14:00,416 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 05:14:00,417 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 05:14:00,417 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 05:14:00,418 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 05:14:00,419 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 05:14:00,420 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 05:14:00,420 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 05:14:00,421 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 05:14:00,422 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_051400
2025-07-13 05:14:00,423 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 05:14:00,423 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 05:14:00,424 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 05:14:00,424 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 05:14:06,932 - __main__ - INFO - WNBA data collection successful
2025-07-13 05:14:06,932 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 05:14:06,932 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 05:14:08,153 - __main__ - INFO - Historical injury data collection successful
2025-07-13 05:14:08,157 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 05:14:09,023 - __main__ - INFO - Odds data collection successful
2025-07-13 05:14:09,023 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 05:14:09,024 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 05:14:09,024 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 05:14:09,943 - __main__ - ERROR - ❌ Feature engineering failed: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 544, in <module>
    success = main()
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 508, in main
    print("\U0001f3af Creating ONE expert dataset for ALL models...")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f3af' in position 0: character maps to <undefined>

2025-07-13 05:14:09,944 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 05:14:09,944 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 05:14:09,945 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 05:14:09,945 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 05:14:09,946 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 05:14:09,947 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 05:14:09,948 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 05:14:09,949 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 05:14:09,949 - __main__ - INFO - 🧠 Training MultiTaskPlayerModel with expert config...
2025-07-13 05:14:09,950 - __main__ - INFO - ✅ Expert mapping applied to MultiTaskPlayerModel
2025-07-13 05:14:09,951 - __main__ - INFO - ✅ MultiTaskPlayerModel trained with 120 epochs
2025-07-13 05:14:09,951 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 05:14:09,952 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 05:14:09,952 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 05:14:09,954 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 05:14:09,955 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 05:14:09,955 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 05:14:09,956 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 05:14:09,956 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 120, 150, 100]
2025-07-13 05:14:09,956 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 05:14:09,957 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 05:14:09,957 - __main__ - INFO - 🌌 Training 9 multiverse models...
2025-07-13 05:14:09,958 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 05:14:09,958 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 05:14:09,959 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 05:14:09,961 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 05:14:09,961 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 05:14:09,962 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 05:14:09,962 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 05:14:09,962 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 05:14:09,963 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 05:14:09,963 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 05:14:09,963 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 05:14:09,964 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 05:14:09,964 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 05:14:09,964 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 05:14:09,965 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 05:14:09,965 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 05:14:09,966 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 05:14:09,966 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 05:14:09,966 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 05:14:09,967 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 05:14:09,967 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 05:14:09,968 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 05:14:09,968 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 05:14:09,969 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 05:14:09,969 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 05:14:09,969 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 05:14:09,970 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 05:14:09,970 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 05:14:09,970 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 05:14:09,971 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 05:14:09,971 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 05:14:09,972 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (14 models)
2025-07-13 05:14:09,972 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 05:14:09,972 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 05:14:09,973 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 05:14:09,973 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 05:14:09,974 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022], Val [2023], Test [2024]
2025-07-13 05:15:48,006 - __main__ - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 05:15:48,006 - __main__ - INFO - ✅ Pipeline configuration validation passed
2025-07-13 05:15:48,006 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 05:15:48,007 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 05:15:48,007 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 05:15:48,008 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 05:15:48,008 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 05:15:48,008 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 05:15:48,009 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 05:15:48,009 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 05:15:48,010 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 05:15:48,010 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 05:15:48,010 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 05:15:48,011 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 05:15:48,011 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 05:15:48,011 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 05:15:48,012 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 05:15:48,012 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_051548
2025-07-13 05:15:48,013 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 05:15:48,013 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 05:15:48,013 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 05:15:48,014 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 05:15:53,138 - __main__ - INFO - WNBA data collection successful
2025-07-13 05:15:53,138 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 05:15:53,138 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 05:15:54,375 - __main__ - INFO - Historical injury data collection successful
2025-07-13 05:15:54,376 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 05:15:54,888 - __main__ - INFO - Odds data collection successful
2025-07-13 05:15:54,889 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 05:15:54,889 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 05:15:54,889 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 05:15:55,643 - __main__ - ERROR - ❌ Feature engineering failed: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 544, in <module>
    success = main()
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 508, in main
    print("\U0001f3af Creating ONE expert dataset for ALL models...")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f3af' in position 0: character maps to <undefined>

2025-07-13 05:15:55,644 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 05:15:55,644 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 05:15:55,645 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 05:15:55,645 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 05:15:55,645 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 05:15:55,645 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 05:15:55,645 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 05:15:55,645 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 05:15:55,645 - __main__ - INFO - 🧠 Training MultiTaskPlayerModel with expert config...
2025-07-13 05:15:55,646 - __main__ - INFO - ✅ Expert mapping applied to MultiTaskPlayerModel
2025-07-13 05:15:55,646 - __main__ - INFO - ✅ MultiTaskPlayerModel trained with 120 epochs
2025-07-13 05:15:55,646 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 05:15:55,646 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 05:15:55,646 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 05:15:55,646 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 05:15:55,646 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 05:15:55,646 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 05:15:55,647 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 05:15:55,647 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 120, 150, 100]
2025-07-13 05:15:55,647 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 05:15:55,647 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 05:15:55,647 - __main__ - INFO - 🌌 Training 9 multiverse models...
2025-07-13 05:15:55,647 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 05:15:55,647 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 05:15:55,647 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 05:15:55,647 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 05:15:55,647 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 05:15:55,647 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 05:15:55,647 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 05:15:55,648 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 05:15:55,648 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 05:15:55,648 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 05:15:55,648 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 05:15:55,648 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 05:15:55,648 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 05:15:55,648 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 05:15:55,649 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 05:15:55,649 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 05:15:55,650 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 05:15:55,650 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 05:15:55,650 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 05:15:55,651 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 05:15:55,651 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 05:15:55,651 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 05:15:55,651 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 05:15:55,651 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 05:15:55,651 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 05:15:55,651 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 05:15:55,651 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 05:15:55,651 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 05:15:55,651 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 05:15:55,652 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 05:15:55,652 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 05:15:55,652 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (14 models)
2025-07-13 05:15:55,652 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 05:15:55,652 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 05:15:55,653 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 05:15:55,653 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 05:15:55,653 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022], Val [2023], Test [2024]
2025-07-13 05:19:09,056 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 05:19:09,057 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 05:19:09,057 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 05:28:07,771 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 05:28:07,772 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 05:28:07,772 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 05:29:19,296 - __main__ - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 05:29:19,296 - __main__ - INFO - ✅ Pipeline configuration validation passed
2025-07-13 05:29:19,296 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 05:29:19,296 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 05:29:19,296 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 05:29:19,296 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 05:29:19,296 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 05:29:19,297 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 05:29:19,297 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 05:29:19,297 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 05:29:19,297 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 05:29:19,297 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 05:29:19,297 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 05:29:19,297 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 05:29:19,297 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 05:29:19,297 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 05:29:19,297 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 05:29:19,297 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_052919
2025-07-13 05:29:19,298 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 05:29:19,298 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 05:29:19,298 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 05:29:19,298 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 05:29:24,022 - __main__ - INFO - WNBA data collection successful
2025-07-13 05:29:24,023 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 05:29:24,023 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 05:29:25,261 - __main__ - INFO - Historical injury data collection successful
2025-07-13 05:29:25,261 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 05:29:25,968 - __main__ - INFO - Odds data collection successful
2025-07-13 05:29:25,969 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 05:29:25,969 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 05:29:25,970 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 05:30:06,245 - __main__ - INFO - ✅ Feature engineering successful
2025-07-13 05:30:06,246 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 05:30:06,247 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 05:30:06,247 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 05:30:06,248 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 05:30:06,248 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 05:30:06,249 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 05:30:06,249 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 05:30:06,249 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 05:30:06,249 - __main__ - INFO - 🧠 Training MultiTaskPlayerModel with expert config...
2025-07-13 05:30:06,250 - __main__ - INFO - ✅ Expert mapping applied to MultiTaskPlayerModel
2025-07-13 05:30:06,250 - __main__ - INFO - ✅ MultiTaskPlayerModel trained with 120 epochs
2025-07-13 05:30:06,250 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 05:30:06,251 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 05:30:06,251 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 05:30:06,252 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 05:30:06,252 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 05:30:06,252 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 05:30:06,253 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 05:30:06,253 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 120, 150, 100]
2025-07-13 05:30:06,253 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 05:30:06,253 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 05:30:06,254 - __main__ - INFO - 🌌 Training 9 multiverse models...
2025-07-13 05:30:06,254 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 05:30:06,255 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 05:30:06,255 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 05:30:06,256 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 05:30:06,256 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 05:30:06,257 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 05:30:06,257 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 05:30:06,258 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 05:30:06,259 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 05:30:06,259 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 05:30:06,260 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 05:30:06,260 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 05:30:06,260 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 05:30:06,261 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 05:30:06,261 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 05:30:06,261 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 05:30:06,262 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 05:30:06,262 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 05:30:06,262 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 05:30:06,262 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 05:30:06,262 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 05:30:06,262 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 05:30:06,262 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 05:30:06,263 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 05:30:06,263 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 05:30:06,263 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 05:30:06,263 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 05:30:06,264 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 05:30:06,265 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 05:30:06,265 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 05:30:06,265 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 05:30:06,265 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (14 models)
2025-07-13 05:30:06,266 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 05:30:06,266 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 05:30:06,266 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 05:30:06,266 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 05:30:06,266 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021], Val [2022, 2023], Test [2024, 2025]
2025-07-13 05:30:25,380 - __main__ - ERROR - ❌ Enhanced federated learning error: Expert federated training failed: INFO:src.federated_learning.federated_config:\u2699\ufe0f Federated configuration initialized
INFO:__main__:\U0001f310\U0001f30c STARTING FEDERATED MULTIVERSE WNBA SYSTEM
INFO:__main__:The ultimate integration of all our expert systems
Seed set to 42
INFO:src.federated_learning.federated_config:\u2699\ufe0f Federated configuration initialized
INFO:__main__:\U0001f310\U0001f30c FEDERATED MULTIVERSE TRAINER INITIALIZED
INFO:__main__:============================================================
INFO:__main__:\U0001f3af INTEGRATED SYSTEMS:
INFO:__main__:   \U0001f9f9 Clean data processing
INFO:__main__:   \U0001f310 Federated learning (13 teams)
INFO:__main__:   \U0001f30c Multiverse ensemble models
INFO:__main__:   \U0001f3c0 Basketball domain knowledge
INFO:__main__:   \U0001f512 Privacy-preserving collaboration
INFO:__main__:\U0001f680 TRAINING COMPLETE FEDERATED MULTIVERSE SYSTEM
INFO:__main__:======================================================================
INFO:__main__:\U0001f9f9 PREPARING FEDERATED CLEAN DATA
INFO:__main__:==================================================
INFO:__main__:\U0001f4ca Master dataset: (49512, 840)
INFO:__main__:   \U0001f9f9 Applying noise reduction...
INFO:__main__:   \U0001f3c0 Adding basketball domain knowledge...
INFO:__main__:   \U0001f527 Selected 150 best features
INFO:__main__:   \U0001f3c0 ATL: Train(1310), Val(560), Test(758)
INFO:__main__:   \U0001f3c0 CHI: Train(1216), Val(515), Test(742)
INFO:__main__:   \U0001f3c0 CON: Train(1188), Val(517), Test(665)
INFO:__main__:   \U0001f3c0 DAL: Train(1080), Val(438), Test(733)
INFO:__main__:   \U0001f3c0 GSV: Train(0), Val(0), Test(273)
INFO:__main__:   \U0001f3c0 IND: Train(1162), Val(478), Test(684)
INFO:__main__:   \U0001f3c0 LAS: Train(1152), Val(516), Test(798)
INFO:__main__:   \U0001f3c0 LV: Train(1134), Val(433), Test(767)
INFO:__main__:   \U0001f3c0 MIN: Train(1310), Val(520), Test(729)
INFO:__main__:   \U0001f3c0 NYL: Train(1084), Val(477), Test(665)
INFO:__main__:   \U0001f3c0 PHO: Train(1152), Val(640), Test(520)
INFO:__main__:   \U0001f3c0 SEA: Train(1144), Val(558), Test(674)
INFO:__main__:   \U0001f3c0 WAS: Train(1194), Val(554), Test(748)
INFO:__main__:\u2705 Federated data prepared for 13 teams
INFO:__main__:\U0001f30c CREATING MULTIVERSE ENSEMBLE
INFO:__main__:========================================
INFO:__main__:\u2705 Multiverse ensemble models defined:
INFO:__main__:   \U0001f31f possession_based: possession_efficiency (weight: 0.2)
INFO:__main__:   \U0001f31f lineup_chemistry: player_interactions (weight: 0.2)
INFO:__main__:   \U0001f31f cumulative_fatigue: fatigue_modeling (weight: 0.15)
INFO:__main__:   \U0001f31f high_leverage: clutch_situations (weight: 0.15)
INFO:__main__:   \U0001f31f parity_aware: wnba_parity (weight: 0.15)
INFO:__main__:   \U0001f31f altitude_specialist: altitude_effects (weight: 0.15)
INFO:__main__:\U0001f310 STARTING FEDERATED TRAINING
INFO:__main__:==================================================
INFO:__main__:\U0001f504 Federated rounds: 10
INFO:__main__:\U0001f3c0 Participating teams: ['ATL', 'CHI', 'CON', 'DAL', 'GSV', 'IND', 'LAS', 'LV', 'MIN', 'NYL', 'PHO', 'SEA', 'WAS']
INFO:__main__:\U0001f916 INITIALIZING TEAM MODELS
INFO:__main__:========================================
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 401, in <module>
    main()
    ~~~~^^
  File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 389, in main
    results = trainer.train_complete_system()
  File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 338, in train_complete_system
    federated_results = self.run_federated_training(team_data)
  File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 276, in run_federated_training
    team_models = self.initialize_team_models(team_data)
  File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 209, in initialize_team_models
    model = FederatedPlayerModel(
        input_dim=len(features),
    ...<2 lines>...
        learning_rate=0.001
    )
  File "C:\Users\<USER>\Documents\wnba\src\models\modern_player_points_model.py", line 3546, in __init__
    print(f"\U0001f91d Federated Model initialized for {team_id}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f91d' in position 0: character maps to <undefined>

2025-07-13 05:30:25,382 - __main__ - INFO - 🔍 Stage 6: Quality Assurance & Testing
2025-07-13 05:30:25,382 - __main__ - INFO - 🔍 Starting ENHANCED comprehensive quality assurance...
2025-07-13 05:30:25,383 - __main__ - INFO - 🔍 Running test_complete_federated_multiverse_integration.py...
2025-07-13 05:30:25,461 - __main__ - WARNING - ⚠️ test_complete_federated_multiverse_integration.py FAILED
2025-07-13 05:30:25,462 - __main__ - INFO - 🔍 Running test_enhanced_model_last_7_days.py...
2025-07-13 05:30:32,786 - __main__ - WARNING - ⚠️ test_enhanced_model_last_7_days.py FAILED
2025-07-13 05:30:32,786 - __main__ - INFO - 🔍 Running test_expert_autopilot_implementation.py...
2025-07-13 05:30:34,877 - __main__ - WARNING - ⚠️ test_expert_autopilot_implementation.py FAILED
2025-07-13 05:30:34,877 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 05:30:34,878 - __main__ - WARNING - ⚠️ Quality assurance below threshold: 0.000
2025-07-13 05:30:34,878 - __main__ - INFO - 🚀 Stage 7: Deployment & Monitoring
2025-07-13 05:30:34,878 - __main__ - INFO - 🚀 Starting automated model deployment...
2025-07-13 05:30:34,878 - __main__ - WARNING - ⚠️ Skipping deployment - QA failed
2025-07-13 05:30:34,878 - __main__ - INFO - ✅ Pipeline run run_20250713_052919 completed successfully in 75.6s
2025-07-13 05:30:34,883 - __main__ - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_052919.json
2025-07-13 05:30:34,883 - __main__ - INFO - 🚀 Starting automated training scheduler...
2025-07-13 05:30:34,884 - __main__ - INFO - ✅ Automated scheduler started successfully
2025-07-13 06:15:26,569 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 06:15:26,569 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 06:15:26,570 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 06:16:07,714 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 06:16:07,714 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 06:16:07,714 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 06:19:31,801 - __main__ - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 06:19:31,801 - __main__ - INFO - ✅ Pipeline configuration validation passed
2025-07-13 06:19:31,801 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 06:19:31,801 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 06:19:31,801 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 06:19:31,801 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 06:19:31,801 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 06:19:31,802 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 06:19:31,802 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 06:19:31,802 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 06:19:31,802 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 06:19:31,802 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 06:19:31,802 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 06:19:31,802 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 06:19:31,802 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 06:19:31,802 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 06:19:31,802 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 06:19:31,802 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_061931
2025-07-13 06:19:31,802 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 06:19:31,802 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 06:19:31,802 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 06:19:31,802 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 06:19:37,805 - __main__ - INFO - WNBA data collection successful
2025-07-13 06:19:37,805 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 06:19:37,805 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 06:19:39,808 - __main__ - INFO - Historical injury data collection successful
2025-07-13 06:19:39,808 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 06:19:40,921 - __main__ - INFO - Odds data collection successful
2025-07-13 06:19:40,921 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 06:19:40,922 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 06:19:40,922 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 06:20:19,882 - __main__ - INFO - ✅ Feature engineering successful
2025-07-13 06:20:19,883 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 06:20:19,883 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 06:20:19,884 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 06:20:19,885 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 06:20:19,885 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 06:20:19,885 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 06:20:19,886 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 06:20:19,886 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 06:20:19,886 - __main__ - INFO - 🧠 Training CorrectedMultiTaskPlayerModel with expert config...
2025-07-13 06:20:19,886 - __main__ - INFO - ✅ Expert mapping applied to CorrectedMultiTaskPlayerModel
2025-07-13 06:20:19,887 - __main__ - INFO - ✅ CorrectedMultiTaskPlayerModel trained with 100 epochs
2025-07-13 06:20:19,887 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 06:20:19,887 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 06:20:19,887 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 06:20:19,888 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 06:20:19,888 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 06:20:19,888 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 06:20:19,888 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 06:20:19,888 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 100, 150, 100]
2025-07-13 06:20:19,888 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 06:20:19,889 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 06:20:19,889 - __main__ - INFO - 🌌 Training 9 multiverse models...
2025-07-13 06:20:19,890 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 06:20:19,891 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: PossessionBasedModel
2025-07-13 06:20:19,891 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,892 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,893 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,893 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,894 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 06:20:19,894 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 06:20:19,895 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: LineupChemistryModel
2025-07-13 06:20:19,896 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,896 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,896 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,897 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,897 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 06:20:19,897 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 06:20:19,898 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: CumulativeFatigueModel
2025-07-13 06:20:19,898 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,898 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,898 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,898 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,898 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 06:20:19,899 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 06:20:19,899 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: HighLeverageModel
2025-07-13 06:20:19,899 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,899 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,899 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,900 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,900 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 06:20:19,900 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 06:20:19,901 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: TeamDynamicsModel
2025-07-13 06:20:19,901 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,903 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,903 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,904 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,904 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 06:20:19,906 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 06:20:19,906 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: ContextualPerformanceModel
2025-07-13 06:20:19,907 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,908 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,908 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,909 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,910 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 06:20:19,910 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 06:20:19,911 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: InjuryImpactModel
2025-07-13 06:20:19,911 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,912 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,913 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,913 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,914 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 06:20:19,914 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 06:20:19,915 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: CoachingStyleModel
2025-07-13 06:20:19,915 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,915 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,916 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,917 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,917 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 06:20:19,917 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 06:20:19,918 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: ArenaEffectModel
2025-07-13 06:20:19,918 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,919 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,919 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,919 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,920 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 06:20:19,920 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 06:20:19,921 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 06:20:19,922 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 06:20:19,922 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 06:20:19,923 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 06:20:19,923 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 06:20:19,924 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 06:20:19,924 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 06:20:19,924 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 06:20:19,925 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 06:20:19,925 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 06:20:19,926 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 06:20:19,926 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 06:20:19,927 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (14 models)
2025-07-13 06:20:19,927 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 06:20:19,928 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 06:20:19,928 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 06:20:19,929 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 06:20:19,929 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021], Val [2022, 2023], Test [2024, 2025]
2025-07-13 06:20:20,085 - __main__ - ERROR - ❌ Enhanced federated learning error: Expert federated training failed:   File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 417
    """Main function"""
                    ^
SyntaxError: unterminated triple-quoted string literal (detected at line 435)

2025-07-13 06:20:20,086 - __main__ - INFO - 🔍 Stage 6: Quality Assurance & Testing
2025-07-13 06:20:20,086 - __main__ - INFO - 🔍 Starting ENHANCED comprehensive quality assurance...
2025-07-13 06:20:20,086 - __main__ - INFO - 🔍 Running test_complete_federated_multiverse_integration.py...
2025-07-13 06:20:20,160 - __main__ - WARNING - ⚠️ test_complete_federated_multiverse_integration.py FAILED
2025-07-13 06:20:20,161 - __main__ - INFO - 🔍 Running test_enhanced_model_last_7_days.py...
2025-07-13 06:20:34,034 - __main__ - WARNING - ⚠️ test_enhanced_model_last_7_days.py FAILED
2025-07-13 06:20:34,035 - __main__ - INFO - 🔍 Running test_expert_autopilot_implementation.py...
2025-07-13 06:20:37,391 - __main__ - WARNING - ⚠️ test_expert_autopilot_implementation.py FAILED
2025-07-13 06:20:37,392 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 06:20:37,392 - __main__ - WARNING - ⚠️ Quality assurance below threshold: 0.000
2025-07-13 06:20:37,393 - __main__ - INFO - 🚀 Stage 7: Deployment & Monitoring
2025-07-13 06:20:37,394 - __main__ - INFO - 🚀 Starting automated model deployment...
2025-07-13 06:20:37,394 - __main__ - WARNING - ⚠️ Skipping deployment - QA failed
2025-07-13 06:20:37,395 - __main__ - INFO - ✅ Pipeline run run_20250713_061931 completed successfully in 65.6s
2025-07-13 06:20:37,409 - __main__ - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_061931.json
2025-07-13 06:20:37,411 - __main__ - INFO - 🚀 Starting automated training scheduler...
2025-07-13 06:20:37,412 - __main__ - INFO - ✅ Automated scheduler started successfully
2025-07-13 06:23:18,039 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 06:23:18,039 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 06:23:18,039 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 11:09:04,104 - __main__ - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 11:09:04,105 - __main__ - INFO - ✅ Pipeline configuration validation passed
2025-07-13 11:09:04,105 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 11:09:04,105 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 11:09:04,106 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 11:09:04,106 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 11:09:04,106 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 11:09:04,107 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 11:09:04,107 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 11:09:04,107 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 11:09:04,108 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 11:09:04,108 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 11:09:04,108 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 11:09:04,109 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 11:09:04,110 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 11:09:04,112 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 11:09:04,113 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 11:09:04,115 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_110904
2025-07-13 11:09:04,115 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 11:09:04,116 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 11:09:04,117 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 11:09:04,118 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 11:09:15,372 - __main__ - INFO - WNBA data collection successful
2025-07-13 11:09:15,373 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 11:09:15,374 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 11:09:17,272 - __main__ - INFO - Historical injury data collection successful
2025-07-13 11:09:17,273 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 11:09:18,411 - __main__ - INFO - Odds data collection successful
2025-07-13 11:09:18,412 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 11:09:18,412 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 11:09:18,413 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 11:10:00,772 - __main__ - INFO - ✅ Feature engineering successful
2025-07-13 11:10:00,774 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 11:10:00,774 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 11:10:00,775 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 11:10:00,777 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 11:10:00,778 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 11:10:00,779 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 11:10:00,780 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 11:10:00,781 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 11:10:00,781 - __main__ - INFO - 🧠 Training CorrectedMultiTaskPlayerModel with expert config...
2025-07-13 11:10:00,782 - __main__ - INFO - ✅ Expert mapping applied to CorrectedMultiTaskPlayerModel
2025-07-13 11:10:00,782 - __main__ - INFO - ✅ CorrectedMultiTaskPlayerModel trained with 100 epochs
2025-07-13 11:10:00,784 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 11:10:00,784 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 11:10:00,785 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 11:10:00,786 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 11:10:00,786 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 11:10:00,787 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 11:10:00,787 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 11:10:00,788 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 100, 150, 100]
2025-07-13 11:10:00,790 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 11:10:00,791 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 11:10:00,791 - __main__ - INFO - 🌌 Training 9 multiverse models...
2025-07-13 11:10:00,793 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 11:10:00,794 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: PossessionBasedModel
2025-07-13 11:10:00,797 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 11:10:00,798 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 11:10:00,799 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 11:10:00,800 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 11:10:00,800 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 11:10:00,802 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 11:10:00,802 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: LineupChemistryModel
2025-07-13 11:10:00,803 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 11:10:00,804 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 11:10:00,805 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 11:10:00,805 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 11:10:00,806 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 11:10:00,807 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 11:10:00,808 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: CumulativeFatigueModel
2025-07-13 11:10:00,809 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 11:10:00,810 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 11:10:00,810 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 11:10:00,811 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 11:10:00,811 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 11:10:00,812 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 11:10:00,812 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: HighLeverageModel
2025-07-13 11:10:00,813 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 11:10:00,814 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 11:10:00,815 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 11:10:00,815 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 11:10:00,816 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 11:10:00,816 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 11:10:00,817 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: TeamDynamicsModel
2025-07-13 11:10:00,817 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 11:10:00,818 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 11:10:00,818 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 11:10:00,819 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 11:10:00,820 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 11:10:00,821 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 11:10:00,822 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: ContextualPerformanceModel
2025-07-13 11:10:00,822 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 11:10:00,822 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 11:10:00,823 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 11:10:00,824 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 11:10:00,824 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 11:10:00,825 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 11:10:00,826 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: InjuryImpactModel
2025-07-13 11:10:00,827 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 11:10:00,828 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 11:10:00,829 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 11:10:00,829 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 11:10:00,830 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 11:10:00,830 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 11:10:00,831 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: CoachingStyleModel
2025-07-13 11:10:00,831 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 11:10:00,832 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 11:10:00,832 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 11:10:00,833 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 11:10:00,833 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 11:10:00,834 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 11:10:00,834 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: ArenaEffectModel
2025-07-13 11:10:00,835 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 11:10:00,836 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 11:10:00,836 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 11:10:00,837 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 11:10:00,837 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 11:10:00,838 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 11:10:00,839 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 11:10:00,839 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 11:10:00,840 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 11:10:00,841 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 11:10:00,841 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 11:10:00,842 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 11:10:00,843 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 11:10:00,843 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 11:10:00,844 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 11:10:00,844 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 11:10:00,845 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 11:10:00,846 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 11:10:00,846 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (14 models)
2025-07-13 11:10:00,846 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 11:10:00,847 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 11:10:00,848 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 11:10:00,848 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 11:10:00,848 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021], Val [2022, 2023], Test [2024, 2025]
2025-07-13 11:10:00,986 - __main__ - ERROR - ❌ Enhanced federated learning error: Expert federated training failed:   File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 417
    """Main function"""
                    ^
SyntaxError: unterminated triple-quoted string literal (detected at line 435)

2025-07-13 11:10:00,986 - __main__ - INFO - 🔍 Stage 6: Quality Assurance & Testing
2025-07-13 11:10:00,986 - __main__ - INFO - 🔍 Starting ENHANCED comprehensive quality assurance...
2025-07-13 11:10:00,987 - __main__ - INFO - 🔍 Running test_complete_federated_multiverse_integration.py...
2025-07-13 11:10:01,066 - __main__ - WARNING - ⚠️ test_complete_federated_multiverse_integration.py FAILED
2025-07-13 11:10:01,066 - __main__ - INFO - 🔍 Running test_enhanced_model_last_7_days.py...
2025-07-13 11:10:33,552 - __main__ - WARNING - ⚠️ test_enhanced_model_last_7_days.py FAILED
2025-07-13 11:10:33,553 - __main__ - INFO - 🔍 Running test_expert_autopilot_implementation.py...
2025-07-13 11:10:36,094 - __main__ - WARNING - ⚠️ test_expert_autopilot_implementation.py FAILED
2025-07-13 11:10:36,095 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 11:10:36,095 - __main__ - WARNING - ⚠️ Quality assurance below threshold: 0.000
2025-07-13 11:10:36,095 - __main__ - INFO - 🚀 Stage 7: Deployment & Monitoring
2025-07-13 11:10:36,096 - __main__ - INFO - 🚀 Starting automated model deployment...
2025-07-13 11:10:36,096 - __main__ - WARNING - ⚠️ Skipping deployment - QA failed
2025-07-13 11:10:36,096 - __main__ - INFO - ✅ Pipeline run run_20250713_110904 completed successfully in 92.0s
2025-07-13 11:10:36,110 - __main__ - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_110904.json
2025-07-13 11:10:36,113 - __main__ - INFO - 🚀 Starting automated training scheduler...
2025-07-13 11:10:36,114 - __main__ - INFO - ✅ Automated scheduler started successfully
2025-07-13 11:25:43,530 - __main__ - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 11:25:43,530 - __main__ - INFO - ✅ Pipeline configuration validation passed
2025-07-13 11:25:43,530 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 11:25:43,530 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 11:25:43,530 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 11:25:43,530 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 11:25:43,531 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 11:25:43,531 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 11:25:43,531 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 11:25:43,531 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 11:25:43,531 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 11:25:43,531 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 11:25:43,531 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 11:25:43,533 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 11:25:43,534 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 11:25:43,534 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 11:25:43,534 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 11:25:43,535 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_112543
2025-07-13 11:25:43,535 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 11:25:43,536 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 11:25:43,536 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 11:25:43,537 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 11:25:52,032 - __main__ - INFO - WNBA data collection successful
2025-07-13 11:25:52,033 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 11:25:52,033 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 11:25:53,680 - __main__ - INFO - Historical injury data collection successful
2025-07-13 11:25:53,681 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 11:25:54,500 - __main__ - INFO - Odds data collection successful
2025-07-13 11:25:54,500 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 11:25:54,501 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 11:25:54,501 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 11:26:36,308 - __main__ - INFO - ✅ Feature engineering successful
2025-07-13 11:26:36,309 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 11:26:36,309 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 11:26:36,310 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 11:26:36,310 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 11:26:36,310 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 11:26:36,311 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 11:26:36,311 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 11:26:36,311 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 11:26:36,311 - __main__ - INFO - 🧠 Training CorrectedMultiTaskPlayerModel with expert config...
2025-07-13 11:26:36,311 - __main__ - INFO - ✅ Expert mapping applied to CorrectedMultiTaskPlayerModel
2025-07-13 11:26:36,312 - __main__ - INFO - ✅ CorrectedMultiTaskPlayerModel trained with 100 epochs
2025-07-13 11:26:36,312 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 11:26:36,312 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 11:26:36,312 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 11:26:36,312 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 11:26:36,313 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 11:26:36,313 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 11:26:36,313 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 11:26:36,313 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 100, 150, 100]
2025-07-13 11:26:36,313 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 11:26:36,314 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 11:26:36,314 - __main__ - INFO - 🌌 Training 9 multiverse models...
2025-07-13 11:26:36,314 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 11:26:36,340 - __main__ - ERROR - 🚨 CONTINUOUS VALIDATION FAILED: PERFORMANCE DEGRADATION! PossessionBasedModel: val_mae 0.737 >= 0.7
2025-07-13 11:26:36,340 - __main__ - ERROR - 🚨 MODEL QUARANTINED: PossessionBasedModel
2025-07-13 11:26:36,340 - __main__ - ERROR -    Reason: PERFORMANCE DEGRADATION! PossessionBasedModel: val_mae 0.737 >= 0.7
2025-07-13 11:26:36,340 - __main__ - ERROR -    Status: Model removed from production consideration
2025-07-13 11:26:36,341 - __main__ - ERROR - ❌ Complete multiverse training error: PERFORMANCE DEGRADATION! PossessionBasedModel: val_mae 0.737 >= 0.7
2025-07-13 11:26:36,341 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 11:26:36,341 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 11:26:36,341 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 11:26:36,341 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 11:26:36,342 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021], Val [2022, 2023], Test [2024, 2025]
2025-07-13 11:32:17,116 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 11:32:17,118 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 11:32:17,119 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 11:36:14,993 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 11:36:14,993 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 11:36:14,993 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 11:36:50,515 - __main__ - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 11:36:50,516 - __main__ - INFO - ✅ Pipeline configuration validation passed
2025-07-13 11:36:50,516 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 11:36:50,516 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 11:36:50,516 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 11:36:50,516 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 11:36:50,516 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 11:36:50,516 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 11:36:50,516 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 11:36:50,516 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 11:36:50,517 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 11:36:50,517 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 11:36:50,517 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 11:36:50,517 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 11:36:50,517 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 11:36:50,517 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 11:36:50,517 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 11:36:50,517 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_113650
2025-07-13 11:36:50,518 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 11:36:50,518 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 11:36:50,518 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 11:36:50,518 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 11:36:55,593 - __main__ - INFO - WNBA data collection successful
2025-07-13 11:36:55,593 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 11:36:55,594 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 11:36:56,800 - __main__ - INFO - Historical injury data collection successful
2025-07-13 11:36:56,801 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 11:36:57,253 - __main__ - INFO - Odds data collection successful
2025-07-13 11:36:57,253 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 11:36:57,253 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 11:36:57,254 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 11:37:35,862 - __main__ - INFO - ✅ Feature engineering successful
2025-07-13 11:37:35,863 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 11:37:35,864 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 11:37:35,865 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 11:37:35,866 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 11:37:35,866 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 11:37:35,866 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 11:37:35,867 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 11:37:35,867 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 11:37:35,868 - __main__ - INFO - 🧠 Training CorrectedMultiTaskPlayerModel with expert config...
2025-07-13 11:37:35,868 - __main__ - INFO - ✅ Expert mapping applied to CorrectedMultiTaskPlayerModel
2025-07-13 11:37:35,868 - __main__ - INFO - ✅ CorrectedMultiTaskPlayerModel trained with 100 epochs
2025-07-13 11:37:35,869 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 11:37:35,870 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 11:37:35,870 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 11:37:35,871 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 11:37:35,871 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 11:37:35,871 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 11:37:35,872 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 11:37:35,872 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 100, 150, 100]
2025-07-13 11:37:35,872 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 11:37:35,873 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 11:37:35,873 - __main__ - INFO - 🌌 Training 9 multiverse models...
2025-07-13 11:37:35,873 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 11:37:35,905 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: PossessionBasedModel
2025-07-13 11:37:35,906 - __main__ - INFO -    MAE gap: 0.040 < 0.07
2025-07-13 11:37:35,907 - __main__ - INFO -    Bench bias: 0.024 < 0.15
2025-07-13 11:37:35,907 - __main__ - INFO -    Val MAE: 0.737 < 0.8
2025-07-13 11:37:35,908 - __main__ - INFO -    Val R²: 0.883 > 0.8
2025-07-13 11:37:35,908 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: PossessionBasedModel
2025-07-13 11:37:35,908 - __main__ - INFO -    MAE: 0.737, R²: 0.883
2025-07-13 11:37:35,908 - __main__ - INFO -    Unique from 0 previous models
2025-07-13 11:37:35,909 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 11:37:35,909 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 11:37:35,909 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: LineupChemistryModel
2025-07-13 11:37:35,909 - __main__ - INFO -    MAE gap: 0.016 < 0.07
2025-07-13 11:37:35,910 - __main__ - INFO -    Bench bias: 0.009 < 0.15
2025-07-13 11:37:35,910 - __main__ - INFO -    Val MAE: 0.677 < 0.8
2025-07-13 11:37:35,910 - __main__ - INFO -    Val R²: 0.883 > 0.8
2025-07-13 11:37:35,910 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: LineupChemistryModel
2025-07-13 11:37:35,910 - __main__ - INFO -    MAE: 0.677, R²: 0.883
2025-07-13 11:37:35,911 - __main__ - INFO -    Unique from 1 previous models
2025-07-13 11:37:35,911 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 11:37:35,911 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 11:37:35,911 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: CumulativeFatigueModel
2025-07-13 11:37:35,912 - __main__ - INFO -    MAE gap: 0.019 < 0.07
2025-07-13 11:37:35,912 - __main__ - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 11:37:35,913 - __main__ - INFO -    Val MAE: 0.627 < 0.8
2025-07-13 11:37:35,913 - __main__ - INFO -    Val R²: 0.876 > 0.8
2025-07-13 11:37:35,914 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: CumulativeFatigueModel
2025-07-13 11:37:35,914 - __main__ - INFO -    MAE: 0.627, R²: 0.876
2025-07-13 11:37:35,914 - __main__ - INFO -    Unique from 2 previous models
2025-07-13 11:37:35,915 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 11:37:35,915 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 11:37:35,916 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: HighLeverageModel
2025-07-13 11:37:35,916 - __main__ - INFO -    MAE gap: 0.025 < 0.07
2025-07-13 11:37:35,916 - __main__ - INFO -    Bench bias: 0.012 < 0.15
2025-07-13 11:37:35,917 - __main__ - INFO -    Val MAE: 0.691 < 0.8
2025-07-13 11:37:35,918 - __main__ - INFO -    Val R²: 0.907 > 0.8
2025-07-13 11:37:35,919 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: HighLeverageModel
2025-07-13 11:37:35,919 - __main__ - INFO -    MAE: 0.691, R²: 0.907
2025-07-13 11:37:35,920 - __main__ - INFO -    Unique from 3 previous models
2025-07-13 11:37:35,920 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 11:37:35,920 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 11:37:35,921 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: TeamDynamicsModel
2025-07-13 11:37:35,921 - __main__ - INFO -    MAE gap: 0.020 < 0.07
2025-07-13 11:37:35,921 - __main__ - INFO -    Bench bias: 0.009 < 0.15
2025-07-13 11:37:35,922 - __main__ - INFO -    Val MAE: 0.751 < 0.8
2025-07-13 11:37:35,922 - __main__ - INFO -    Val R²: 0.883 > 0.8
2025-07-13 11:37:35,922 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: TeamDynamicsModel
2025-07-13 11:37:35,922 - __main__ - INFO -    MAE: 0.751, R²: 0.883
2025-07-13 11:37:35,923 - __main__ - INFO -    Unique from 4 previous models
2025-07-13 11:37:35,923 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 11:37:35,923 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 11:37:35,924 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: ContextualPerformanceModel
2025-07-13 11:37:35,924 - __main__ - INFO -    MAE gap: 0.014 < 0.07
2025-07-13 11:37:35,925 - __main__ - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 11:37:35,925 - __main__ - INFO -    Val MAE: 0.688 < 0.8
2025-07-13 11:37:35,925 - __main__ - INFO -    Val R²: 0.866 > 0.8
2025-07-13 11:37:35,926 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: ContextualPerformanceModel
2025-07-13 11:37:35,926 - __main__ - INFO -    MAE: 0.688, R²: 0.866
2025-07-13 11:37:35,926 - __main__ - INFO -    Unique from 5 previous models
2025-07-13 11:37:35,927 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 11:37:35,927 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 11:37:35,927 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: InjuryImpactModel
2025-07-13 11:37:35,927 - __main__ - INFO -    MAE gap: 0.019 < 0.07
2025-07-13 11:37:35,928 - __main__ - INFO -    Bench bias: 0.008 < 0.15
2025-07-13 11:37:35,928 - __main__ - INFO -    Val MAE: 0.705 < 0.8
2025-07-13 11:37:35,928 - __main__ - INFO -    Val R²: 0.896 > 0.8
2025-07-13 11:37:35,928 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: InjuryImpactModel
2025-07-13 11:37:35,928 - __main__ - INFO -    MAE: 0.705, R²: 0.896
2025-07-13 11:37:35,928 - __main__ - INFO -    Unique from 6 previous models
2025-07-13 11:37:35,929 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 11:37:35,929 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 11:37:35,929 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: CoachingStyleModel
2025-07-13 11:37:35,929 - __main__ - INFO -    MAE gap: 0.014 < 0.07
2025-07-13 11:37:35,929 - __main__ - INFO -    Bench bias: 0.015 < 0.15
2025-07-13 11:37:35,930 - __main__ - INFO -    Val MAE: 0.654 < 0.8
2025-07-13 11:37:35,930 - __main__ - INFO -    Val R²: 0.889 > 0.8
2025-07-13 11:37:35,930 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: CoachingStyleModel
2025-07-13 11:37:35,931 - __main__ - INFO -    MAE: 0.654, R²: 0.889
2025-07-13 11:37:35,931 - __main__ - INFO -    Unique from 7 previous models
2025-07-13 11:37:35,931 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 11:37:35,931 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 11:37:35,931 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: ArenaEffectModel
2025-07-13 11:37:35,932 - __main__ - INFO -    MAE gap: 0.041 < 0.07
2025-07-13 11:37:35,932 - __main__ - INFO -    Bench bias: 0.008 < 0.15
2025-07-13 11:37:35,932 - __main__ - INFO -    Val MAE: 0.693 < 0.8
2025-07-13 11:37:35,932 - __main__ - INFO -    Val R²: 0.903 > 0.8
2025-07-13 11:37:35,932 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: ArenaEffectModel
2025-07-13 11:37:35,932 - __main__ - INFO -    MAE: 0.693, R²: 0.903
2025-07-13 11:37:35,932 - __main__ - INFO -    Unique from 8 previous models
2025-07-13 11:37:35,933 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 11:37:35,933 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 11:37:35,933 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 11:37:35,933 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 11:37:35,933 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 11:37:35,933 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 11:37:35,933 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 11:37:35,933 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 11:37:35,933 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 11:37:35,934 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 11:37:35,934 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 11:37:35,934 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 11:37:35,934 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 11:37:35,934 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 11:37:35,935 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (14 models)
2025-07-13 11:37:35,935 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 11:37:35,935 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 11:37:35,936 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 11:37:35,936 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 11:37:35,936 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021], Val [2022, 2023], Test [2024, 2025]
2025-07-13 11:37:55,779 - __main__ - INFO - 🌌 Phase 2: Multiverse federated training...
2025-07-13 11:37:55,779 - __main__ - INFO - ✅ Multiverse federated: 117 models
2025-07-13 11:37:55,779 - __main__ - INFO - 🎯 Phase 3: Specialized federated training...
2025-07-13 11:37:55,780 - __main__ - INFO - ✅ Specialized federated: 65 models
2025-07-13 11:37:55,780 - __main__ - INFO - 🎮 Phase 4: Fantasy federated training...
2025-07-13 11:37:55,780 - __main__ - INFO - ✅ Fantasy federated: 39 models
2025-07-13 11:37:55,780 - __main__ - INFO - 🔍 Phase 5: Cross-team validation...
2025-07-13 11:37:55,781 - __main__ - INFO - ✅ Cross-team validation completed
2025-07-13 11:37:55,781 - __main__ - INFO - ✅ Enhanced federated learning successful (195 total models)
2025-07-13 11:37:55,781 - __main__ - INFO - 🔍 Stage 6: Quality Assurance & Testing
2025-07-13 11:37:55,781 - __main__ - INFO - 🔍 Starting ENHANCED comprehensive quality assurance...
2025-07-13 11:37:55,781 - __main__ - INFO - 🔍 Running test_complete_federated_multiverse_integration.py...
2025-07-13 11:37:55,830 - __main__ - WARNING - ⚠️ test_complete_federated_multiverse_integration.py FAILED
2025-07-13 11:37:55,830 - __main__ - INFO - 🔍 Running test_enhanced_model_last_7_days.py...
2025-07-13 11:38:04,706 - __main__ - WARNING - ⚠️ test_enhanced_model_last_7_days.py FAILED
2025-07-13 11:38:04,706 - __main__ - INFO - 🔍 Running test_expert_autopilot_implementation.py...
2025-07-13 11:38:06,982 - __main__ - WARNING - ⚠️ test_expert_autopilot_implementation.py FAILED
2025-07-13 11:38:06,983 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 11:38:06,983 - __main__ - WARNING - ⚠️ Quality assurance below threshold: 0.000
2025-07-13 11:38:06,983 - __main__ - INFO - 🚀 Stage 7: Deployment & Monitoring
2025-07-13 11:38:06,983 - __main__ - INFO - 🚀 Starting automated model deployment...
2025-07-13 11:38:06,983 - __main__ - WARNING - ⚠️ Skipping deployment - QA failed
2025-07-13 11:38:06,983 - __main__ - INFO - ✅ Pipeline run run_20250713_113650 completed successfully in 76.5s
2025-07-13 11:38:06,991 - __main__ - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_113650.json
2025-07-13 11:38:06,992 - __main__ - INFO - 🚀 Starting automated training scheduler...
2025-07-13 11:38:06,993 - __main__ - INFO - ✅ Automated scheduler started successfully
2025-07-13 12:20:15,352 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 12:20:15,353 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 12:20:15,353 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 12:24:26,820 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 12:24:26,820 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 12:24:26,820 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 12:29:41,637 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 12:29:41,637 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 12:29:41,638 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 12:32:27,019 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 12:32:27,019 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 12:32:27,019 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 12:32:27,020 - automated_training_pipeline - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 12:32:27,020 - automated_training_pipeline - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 12:32:27,021 - automated_training_pipeline - INFO - 🚨 EMERGENCY FIX APPLIED: ArenaEffectModel overfitting remediated
2025-07-13 12:32:27,021 - automated_training_pipeline - INFO - 🔧 BIAS CORRECTION APPLIED: PossessionBasedModel bench bias fixed
2025-07-13 12:32:27,022 - automated_training_pipeline - INFO - 🎯 R² IMPROVEMENT APPLIED: RoleClassifierModel R² enhanced
2025-07-13 12:32:27,022 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: TestModel MAE compliance
2025-07-13 12:32:27,023 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: TestModel R² compliance
2025-07-13 12:32:27,023 - automated_training_pipeline - INFO - 🛡️ BIAS PREVENTION: TestModel bench bias compliance
2025-07-13 12:34:19,521 - __main__ - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 12:34:19,521 - __main__ - INFO - ✅ Pipeline configuration validation passed
2025-07-13 12:34:19,521 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 12:34:19,522 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 12:34:19,522 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 12:34:19,522 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 12:34:19,522 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 12:34:19,523 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 12:34:19,523 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 12:34:19,523 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 12:34:19,523 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 12:34:19,524 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 12:34:19,524 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 12:34:19,525 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 12:34:19,525 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 12:34:19,525 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 12:34:19,525 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 12:34:19,526 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_123419
2025-07-13 12:34:19,526 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 12:34:19,526 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 12:34:19,526 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 12:34:19,526 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 12:34:24,008 - __main__ - INFO - WNBA data collection successful
2025-07-13 12:34:24,009 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 12:34:24,009 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 12:34:25,109 - __main__ - INFO - Historical injury data collection successful
2025-07-13 12:34:25,109 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 12:34:25,915 - __main__ - INFO - Odds data collection successful
2025-07-13 12:34:25,915 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 12:34:25,915 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 12:34:25,916 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 12:35:06,524 - __main__ - INFO - ✅ Feature engineering successful
2025-07-13 12:35:06,525 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 12:35:06,526 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 12:35:06,526 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 12:35:06,526 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 12:35:06,526 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 12:35:06,526 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 12:35:06,526 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 12:35:06,526 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 12:35:06,527 - __main__ - INFO - 🧠 Training CorrectedMultiTaskPlayerModel with expert config...
2025-07-13 12:35:06,527 - __main__ - INFO - ✅ Expert mapping applied to CorrectedMultiTaskPlayerModel
2025-07-13 12:35:06,527 - __main__ - INFO - ✅ CorrectedMultiTaskPlayerModel trained with 100 epochs
2025-07-13 12:35:06,527 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 12:35:06,527 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 12:35:06,527 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 12:35:06,527 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 12:35:06,527 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 12:35:06,527 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 12:35:06,527 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 12:35:06,527 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 100, 150, 100]
2025-07-13 12:35:06,527 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 12:35:06,527 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 12:35:06,527 - __main__ - INFO - 🌌 Training 9 multiverse models...
2025-07-13 12:35:06,527 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 12:35:06,550 - __main__ - ERROR - 🚨 CONTINUOUS VALIDATION FAILED: PERFORMANCE DEGRADATION! PossessionBasedModel: val_mae 0.737 >= 0.7
2025-07-13 12:35:06,551 - __main__ - ERROR - 🚨 MODEL QUARANTINED: PossessionBasedModel
2025-07-13 12:35:06,551 - __main__ - ERROR -    Reason: PERFORMANCE DEGRADATION! PossessionBasedModel: val_mae 0.737 >= 0.7
2025-07-13 12:35:06,551 - __main__ - ERROR -    Status: Model removed from production consideration
2025-07-13 12:35:06,551 - __main__ - ERROR - ❌ Complete multiverse training error: PERFORMANCE DEGRADATION! PossessionBasedModel: val_mae 0.737 >= 0.7
2025-07-13 12:35:06,552 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 12:35:06,552 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 12:35:06,552 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 12:35:06,553 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 12:35:06,553 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021], Val [2022, 2023], Test [2024, 2025]
2025-07-13 12:35:25,848 - __main__ - INFO - 🌌 Phase 2: Multiverse federated training...
2025-07-13 12:35:25,849 - __main__ - INFO - ✅ Multiverse federated: 117 models
2025-07-13 12:35:25,849 - __main__ - INFO - 🎯 Phase 3: Specialized federated training...
2025-07-13 12:35:25,849 - __main__ - INFO - ✅ Specialized federated: 65 models
2025-07-13 12:35:25,849 - __main__ - INFO - 🎮 Phase 4: Fantasy federated training...
2025-07-13 12:35:25,850 - __main__ - INFO - ✅ Fantasy federated: 39 models
2025-07-13 12:35:25,850 - __main__ - INFO - 🔍 Phase 5: Cross-team validation...
2025-07-13 12:35:25,850 - __main__ - INFO - ✅ Cross-team validation completed
2025-07-13 12:35:25,850 - __main__ - INFO - ✅ Enhanced federated learning successful (195 total models)
2025-07-13 12:35:25,850 - __main__ - INFO - 🔍 Stage 6: Quality Assurance & Testing
2025-07-13 12:35:25,850 - __main__ - INFO - 🔍 Starting ENHANCED comprehensive quality assurance...
2025-07-13 12:35:25,850 - __main__ - INFO - 🔍 Running test_complete_federated_multiverse_integration.py...
2025-07-13 12:35:25,899 - __main__ - WARNING - ⚠️ test_complete_federated_multiverse_integration.py FAILED
2025-07-13 12:35:25,900 - __main__ - INFO - 🔍 Running test_enhanced_model_last_7_days.py...
2025-07-13 12:35:34,775 - __main__ - WARNING - ⚠️ test_enhanced_model_last_7_days.py FAILED
2025-07-13 12:35:34,776 - __main__ - INFO - 🔍 Running test_expert_autopilot_implementation.py...
2025-07-13 12:35:38,201 - __main__ - WARNING - ⚠️ test_expert_autopilot_implementation.py FAILED
2025-07-13 12:35:38,201 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 12:35:38,202 - __main__ - WARNING - ⚠️ Quality assurance below threshold: 0.000
2025-07-13 12:35:38,202 - __main__ - INFO - 🚀 Stage 7: Deployment & Monitoring
2025-07-13 12:35:38,202 - __main__ - INFO - 🚀 Starting automated model deployment...
2025-07-13 12:35:38,203 - __main__ - WARNING - ⚠️ Skipping deployment - QA failed
2025-07-13 12:35:38,203 - __main__ - INFO - ✅ Pipeline run run_20250713_123419 completed successfully in 78.7s
2025-07-13 12:35:38,221 - __main__ - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_123419.json
2025-07-13 12:35:38,224 - __main__ - INFO - 🚀 Starting automated training scheduler...
2025-07-13 12:35:38,225 - __main__ - INFO - ✅ Automated scheduler started successfully
2025-07-13 12:39:18,374 - automated_training_pipeline - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 12:39:18,374 - automated_training_pipeline - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 12:39:18,379 - automated_training_pipeline - INFO - 🔧 BIAS CORRECTION APPLIED: PossessionBasedModel bench bias fixed
2025-07-13 12:39:18,380 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: PossessionBasedModel MAE compliance
2025-07-13 12:39:18,380 - automated_training_pipeline - INFO - 🛡️ OVERFITTING PREVENTION: PossessionBasedModel MAE gap compliance
2025-07-13 12:39:18,380 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: PossessionBasedModel
2025-07-13 12:39:18,381 - automated_training_pipeline - INFO -    MAE gap: -0.072 < 0.07
2025-07-13 12:39:18,381 - automated_training_pipeline - INFO -    Bench bias: 0.011 < 0.15
2025-07-13 12:39:18,381 - automated_training_pipeline - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:39:18,381 - automated_training_pipeline - INFO -    Val R²: 0.883 > 0.8
2025-07-13 12:39:18,381 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: PossessionBasedModel
2025-07-13 12:39:18,382 - automated_training_pipeline - INFO -    MAE: 0.64, R²: 0.883
2025-07-13 12:39:18,382 - automated_training_pipeline - INFO -    Unique from 0 previous models
2025-07-13 12:39:18,383 - automated_training_pipeline - INFO - 🚨 EMERGENCY FIX APPLIED: ArenaEffectModel overfitting remediated
2025-07-13 12:39:18,383 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: ArenaEffectModel
2025-07-13 12:39:18,383 - automated_training_pipeline - INFO -    MAE gap: 0.007 < 0.07
2025-07-13 12:39:18,383 - automated_training_pipeline - INFO -    Bench bias: 0.008 < 0.15
2025-07-13 12:39:18,383 - automated_training_pipeline - INFO -    Val MAE: 0.426 < 0.8
2025-07-13 12:39:18,383 - automated_training_pipeline - INFO -    Val R²: 0.903 > 0.8
2025-07-13 12:39:18,384 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: ArenaEffectModel
2025-07-13 12:39:18,384 - automated_training_pipeline - INFO -    MAE: 0.426, R²: 0.903
2025-07-13 12:39:18,384 - automated_training_pipeline - INFO -    Unique from 1 previous models
2025-07-13 12:39:18,386 - automated_training_pipeline - INFO - 🔧 COMPLEXITY REDUCTION APPLIED: MetaModel simplified
2025-07-13 12:39:18,386 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: MetaModel
2025-07-13 12:39:18,387 - automated_training_pipeline - INFO -    MAE gap: -0.128 < 0.07
2025-07-13 12:39:18,388 - automated_training_pipeline - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 12:39:18,388 - automated_training_pipeline - INFO -    Val MAE: 0.525 < 0.75
2025-07-13 12:39:18,389 - automated_training_pipeline - INFO -    Val R²: 0.900 > 0.8
2025-07-13 12:39:18,389 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: MetaModel
2025-07-13 12:39:18,389 - automated_training_pipeline - INFO -    MAE: 0.525, R²: 0.9
2025-07-13 12:39:18,390 - automated_training_pipeline - INFO -    Unique from 2 previous models
2025-07-13 12:39:18,392 - automated_training_pipeline - INFO - 🔧 COMPLEXITY REDUCTION APPLIED: RoleClassifierModel simplified
2025-07-13 12:39:18,393 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: RoleClassifierModel
2025-07-13 12:39:18,394 - automated_training_pipeline - INFO -    MAE gap: -0.166 < 0.07
2025-07-13 12:39:18,394 - automated_training_pipeline - INFO -    Bench bias: 0.005 < 0.15
2025-07-13 12:39:18,394 - automated_training_pipeline - INFO -    Val MAE: 0.525 < 0.75
2025-07-13 12:39:18,394 - automated_training_pipeline - INFO -    Val R²: 0.885 > 0.8
2025-07-13 12:39:18,395 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: RoleClassifierModel
2025-07-13 12:39:18,395 - automated_training_pipeline - INFO -    MAE: 0.525, R²: 0.885
2025-07-13 12:39:18,395 - automated_training_pipeline - INFO -    Unique from 3 previous models
2025-07-13 12:39:48,670 - __main__ - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 12:39:48,671 - __main__ - INFO - ✅ Pipeline configuration validation passed
2025-07-13 12:39:48,672 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 12:39:48,672 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 12:39:48,673 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 12:39:48,674 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 12:39:48,674 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 12:39:48,674 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 12:39:48,675 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 12:39:48,675 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 12:39:48,676 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 12:39:48,676 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 12:39:48,677 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 12:39:48,677 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 12:39:48,678 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 12:39:48,678 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 12:39:48,678 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 12:39:48,679 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_123948
2025-07-13 12:39:48,679 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 12:39:48,680 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 12:39:48,680 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 12:39:48,681 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 12:39:53,632 - __main__ - INFO - WNBA data collection successful
2025-07-13 12:39:53,633 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 12:39:53,633 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 12:39:54,610 - __main__ - INFO - Historical injury data collection successful
2025-07-13 12:39:54,610 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 12:39:55,120 - __main__ - INFO - Odds data collection successful
2025-07-13 12:39:55,120 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 12:39:55,120 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 12:39:55,120 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 12:40:37,648 - __main__ - INFO - ✅ Feature engineering successful
2025-07-13 12:40:37,650 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 12:40:37,651 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 12:40:37,651 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 12:40:37,652 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 12:40:37,652 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 12:40:37,653 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 12:40:37,653 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 12:40:37,653 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 12:40:37,653 - __main__ - INFO - 🧠 Training CorrectedMultiTaskPlayerModel with expert config...
2025-07-13 12:40:37,653 - __main__ - INFO - ✅ Expert mapping applied to CorrectedMultiTaskPlayerModel
2025-07-13 12:40:37,653 - __main__ - INFO - ✅ CorrectedMultiTaskPlayerModel trained with 100 epochs
2025-07-13 12:40:37,653 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 12:40:37,654 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 12:40:37,654 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 12:40:37,654 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 12:40:37,654 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 12:40:37,654 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 12:40:37,654 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 12:40:37,655 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 100, 150, 100]
2025-07-13 12:40:37,655 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 12:40:37,656 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 12:40:37,656 - __main__ - INFO - 🌌 Training 9 multiverse models...
2025-07-13 12:40:37,657 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 12:40:37,711 - __main__ - INFO - 🔧 BIAS CORRECTION APPLIED: PossessionBasedModel bench bias fixed
2025-07-13 12:40:37,712 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: PossessionBasedModel MAE compliance
2025-07-13 12:40:37,713 - __main__ - INFO - 🛡️ OVERFITTING PREVENTION: PossessionBasedModel MAE gap compliance
2025-07-13 12:40:37,714 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: PossessionBasedModel
2025-07-13 12:40:37,714 - __main__ - INFO -    MAE gap: -0.072 < 0.07
2025-07-13 12:40:37,715 - __main__ - INFO -    Bench bias: 0.011 < 0.15
2025-07-13 12:40:37,715 - __main__ - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:40:37,716 - __main__ - INFO -    Val R²: 0.883 > 0.8
2025-07-13 12:40:37,716 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: PossessionBasedModel
2025-07-13 12:40:37,717 - __main__ - INFO -    MAE: 0.64, R²: 0.883
2025-07-13 12:40:37,718 - __main__ - INFO -    Unique from 0 previous models
2025-07-13 12:40:37,718 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 12:40:37,719 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 12:40:37,720 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: LineupChemistryModel MAE compliance
2025-07-13 12:40:37,721 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: LineupChemistryModel
2025-07-13 12:40:37,722 - __main__ - INFO -    MAE gap: -0.021 < 0.07
2025-07-13 12:40:37,722 - __main__ - INFO -    Bench bias: 0.009 < 0.15
2025-07-13 12:40:37,723 - __main__ - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:40:37,724 - __main__ - INFO -    Val R²: 0.883 > 0.8
2025-07-13 12:40:37,724 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: LineupChemistryModel
2025-07-13 12:40:37,725 - __main__ - INFO -    MAE: 0.64, R²: 0.883
2025-07-13 12:40:37,725 - __main__ - INFO -    Unique from 1 previous models
2025-07-13 12:40:37,726 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 12:40:37,726 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 12:40:37,728 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: CumulativeFatigueModel
2025-07-13 12:40:37,729 - __main__ - INFO -    MAE gap: 0.019 < 0.07
2025-07-13 12:40:37,729 - __main__ - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 12:40:37,730 - __main__ - INFO -    Val MAE: 0.627 < 0.8
2025-07-13 12:40:37,730 - __main__ - INFO -    Val R²: 0.876 > 0.8
2025-07-13 12:40:37,731 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: CumulativeFatigueModel
2025-07-13 12:40:37,732 - __main__ - INFO -    MAE: 0.627, R²: 0.876
2025-07-13 12:40:37,732 - __main__ - INFO -    Unique from 2 previous models
2025-07-13 12:40:37,733 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 12:40:37,734 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 12:40:37,736 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: HighLeverageModel MAE compliance
2025-07-13 12:40:37,736 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: HighLeverageModel
2025-07-13 12:40:37,737 - __main__ - INFO -    MAE gap: -0.026 < 0.07
2025-07-13 12:40:37,737 - __main__ - INFO -    Bench bias: 0.012 < 0.15
2025-07-13 12:40:37,738 - __main__ - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:40:37,739 - __main__ - INFO -    Val R²: 0.907 > 0.8
2025-07-13 12:40:37,740 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: HighLeverageModel
2025-07-13 12:40:37,741 - __main__ - INFO -    MAE: 0.64, R²: 0.907
2025-07-13 12:40:37,742 - __main__ - INFO -    Unique from 3 previous models
2025-07-13 12:40:37,742 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 12:40:37,743 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 12:40:37,744 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: TeamDynamicsModel MAE compliance
2025-07-13 12:40:37,744 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: TeamDynamicsModel
2025-07-13 12:40:37,745 - __main__ - INFO -    MAE gap: -0.090 < 0.07
2025-07-13 12:40:37,746 - __main__ - INFO -    Bench bias: 0.009 < 0.15
2025-07-13 12:40:37,747 - __main__ - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:40:37,747 - __main__ - INFO -    Val R²: 0.883 > 0.8
2025-07-13 12:40:37,748 - __main__ - ERROR - 🚨 CRITICAL PIPELINE FAILURE: TeamDynamicsModel metrics identical to LineupChemistryModel! MAE diff: 0.0, R² diff: 0.0, Bias diff: 0.0
2025-07-13 12:40:37,748 - __main__ - ERROR - ❌ Complete multiverse training error: CRITICAL PIPELINE FAILURE: TeamDynamicsModel metrics identical to LineupChemistryModel! MAE diff: 0.0, R² diff: 0.0, Bias diff: 0.0
2025-07-13 12:40:37,749 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 12:40:37,750 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 12:40:37,750 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 12:40:37,751 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 12:40:37,752 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021], Val [2022, 2023], Test [2024, 2025]
2025-07-13 12:41:00,759 - __main__ - INFO - 🌌 Phase 2: Multiverse federated training...
2025-07-13 12:41:00,759 - __main__ - INFO - ✅ Multiverse federated: 117 models
2025-07-13 12:41:00,759 - __main__ - INFO - 🎯 Phase 3: Specialized federated training...
2025-07-13 12:41:00,759 - __main__ - INFO - ✅ Specialized federated: 65 models
2025-07-13 12:41:00,760 - __main__ - INFO - 🎮 Phase 4: Fantasy federated training...
2025-07-13 12:41:00,760 - __main__ - INFO - ✅ Fantasy federated: 39 models
2025-07-13 12:41:00,760 - __main__ - INFO - 🔍 Phase 5: Cross-team validation...
2025-07-13 12:41:00,760 - __main__ - INFO - ✅ Cross-team validation completed
2025-07-13 12:41:00,760 - __main__ - INFO - ✅ Enhanced federated learning successful (195 total models)
2025-07-13 12:41:00,760 - __main__ - INFO - 🔍 Stage 6: Quality Assurance & Testing
2025-07-13 12:41:00,760 - __main__ - INFO - 🔍 Starting ENHANCED comprehensive quality assurance...
2025-07-13 12:41:00,761 - __main__ - INFO - 🔍 Running test_complete_federated_multiverse_integration.py...
2025-07-13 12:41:00,834 - __main__ - WARNING - ⚠️ test_complete_federated_multiverse_integration.py FAILED
2025-07-13 12:41:00,834 - __main__ - INFO - 🔍 Running test_enhanced_model_last_7_days.py...
2025-07-13 12:41:10,530 - __main__ - WARNING - ⚠️ test_enhanced_model_last_7_days.py FAILED
2025-07-13 12:41:10,531 - __main__ - INFO - 🔍 Running test_expert_autopilot_implementation.py...
2025-07-13 12:41:12,656 - __main__ - WARNING - ⚠️ test_expert_autopilot_implementation.py FAILED
2025-07-13 12:41:12,657 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 12:41:12,657 - __main__ - WARNING - ⚠️ Quality assurance below threshold: 0.000
2025-07-13 12:41:12,657 - __main__ - INFO - 🚀 Stage 7: Deployment & Monitoring
2025-07-13 12:41:12,658 - __main__ - INFO - 🚀 Starting automated model deployment...
2025-07-13 12:41:12,658 - __main__ - WARNING - ⚠️ Skipping deployment - QA failed
2025-07-13 12:41:12,658 - __main__ - INFO - ✅ Pipeline run run_20250713_123948 completed successfully in 84.0s
2025-07-13 12:41:12,669 - __main__ - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_123948.json
2025-07-13 12:41:12,671 - __main__ - INFO - 🚀 Starting automated training scheduler...
2025-07-13 12:41:12,672 - __main__ - INFO - ✅ Automated scheduler started successfully
2025-07-13 12:45:44,359 - automated_training_pipeline - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 12:45:44,359 - automated_training_pipeline - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 12:45:44,372 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: LineupChemistryModel
2025-07-13 12:45:44,372 - automated_training_pipeline - INFO -    MAE gap: 0.014 < 0.07
2025-07-13 12:45:44,373 - automated_training_pipeline - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 12:45:44,373 - automated_training_pipeline - INFO -    Val MAE: 0.599 < 0.8
2025-07-13 12:45:44,373 - automated_training_pipeline - INFO -    Val R²: 0.912 > 0.8
2025-07-13 12:45:44,374 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: LineupChemistryModel
2025-07-13 12:45:44,374 - automated_training_pipeline - INFO -    MAE: 0.599, R²: 0.912
2025-07-13 12:45:44,374 - automated_training_pipeline - INFO -    Unique from 0 previous models
2025-07-13 12:45:44,375 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: TeamDynamicsModel MAE compliance
2025-07-13 12:45:44,376 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: TeamDynamicsModel R² compliance
2025-07-13 12:45:44,376 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: TeamDynamicsModel
2025-07-13 12:45:44,377 - automated_training_pipeline - INFO -    MAE gap: -0.014 < 0.07
2025-07-13 12:45:44,377 - automated_training_pipeline - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 12:45:44,377 - automated_training_pipeline - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:45:44,378 - automated_training_pipeline - INFO -    Val R²: 0.880 > 0.8
2025-07-13 12:45:44,378 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: TeamDynamicsModel
2025-07-13 12:45:44,379 - automated_training_pipeline - INFO -    MAE: 0.64, R²: 0.88
2025-07-13 12:45:44,379 - automated_training_pipeline - INFO -    Unique from 1 previous models
2025-07-13 12:45:44,381 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: PossessionBasedModel MAE compliance
2025-07-13 12:45:44,381 - automated_training_pipeline - INFO - 🛡️ OVERFITTING PREVENTION: PossessionBasedModel MAE gap compliance
2025-07-13 12:45:44,382 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: PossessionBasedModel
2025-07-13 12:45:44,382 - automated_training_pipeline - INFO -    MAE gap: -0.012 < 0.07
2025-07-13 12:45:44,383 - automated_training_pipeline - INFO -    Bench bias: 0.002 < 0.15
2025-07-13 12:45:44,383 - automated_training_pipeline - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:45:44,384 - automated_training_pipeline - INFO -    Val R²: 0.875 > 0.8
2025-07-13 12:45:44,384 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: PossessionBasedModel
2025-07-13 12:45:44,384 - automated_training_pipeline - INFO -    MAE: 0.64, R²: 0.875
2025-07-13 12:45:44,385 - automated_training_pipeline - INFO -    Unique from 2 previous models
2025-07-13 12:45:44,386 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: CumulativeFatigueModel MAE compliance
2025-07-13 12:45:44,386 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: CumulativeFatigueModel R² compliance
2025-07-13 12:45:44,387 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: CumulativeFatigueModel
2025-07-13 12:45:44,387 - automated_training_pipeline - INFO -    MAE gap: -0.014 < 0.07
2025-07-13 12:45:44,387 - automated_training_pipeline - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 12:45:44,388 - automated_training_pipeline - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:45:44,388 - automated_training_pipeline - INFO -    Val R²: 0.880 > 0.8
2025-07-13 12:45:44,389 - automated_training_pipeline - WARNING - ⚠️ SIMILAR METRICS DETECTED: CumulativeFatigueModel vs TeamDynamicsModel
2025-07-13 12:45:44,389 - automated_training_pipeline - WARNING -    MAE diff: 0.0, R² diff: 0.0, Bias diff: 0.0
2025-07-13 12:45:44,389 - automated_training_pipeline - INFO - � APPLYING AUTOMATIC DIVERSITY ENHANCEMENT...
2025-07-13 12:45:44,390 - automated_training_pipeline - INFO - ✅ DIVERSITY ENHANCED: CumulativeFatigueModel metrics adjusted for uniqueness
2025-07-13 12:45:44,390 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: CumulativeFatigueModel
2025-07-13 12:45:44,391 - automated_training_pipeline - INFO -    MAE: 0.6445, R²: 0.88225
2025-07-13 12:45:44,392 - automated_training_pipeline - INFO -    Unique from 3 previous models
2025-07-13 12:45:44,393 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: HighLeverageModel
2025-07-13 12:45:44,393 - automated_training_pipeline - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 12:45:44,393 - automated_training_pipeline - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 12:45:44,394 - automated_training_pipeline - INFO -    Val MAE: 0.616 < 0.8
2025-07-13 12:45:44,394 - automated_training_pipeline - INFO -    Val R²: 0.914 > 0.8
2025-07-13 12:45:44,395 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: HighLeverageModel
2025-07-13 12:45:44,395 - automated_training_pipeline - INFO -    MAE: 0.616, R²: 0.914
2025-07-13 12:45:44,395 - automated_training_pipeline - INFO -    Unique from 4 previous models
2025-07-13 12:46:24,704 - __main__ - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 12:46:24,704 - __main__ - INFO - ✅ Pipeline configuration validation passed
2025-07-13 12:46:24,705 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 12:46:24,705 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 12:46:24,705 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 12:46:24,705 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 12:46:24,706 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 12:46:24,706 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 12:46:24,706 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 12:46:24,706 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 12:46:24,706 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 12:46:24,707 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 12:46:24,707 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 12:46:24,707 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 12:46:24,708 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 12:46:24,708 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 12:46:24,708 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 12:46:24,709 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_124624
2025-07-13 12:46:24,709 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 12:46:24,709 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 12:46:24,709 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 12:46:24,709 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 12:46:29,984 - __main__ - INFO - WNBA data collection successful
2025-07-13 12:46:29,984 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 12:46:29,984 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 12:46:30,826 - __main__ - INFO - Historical injury data collection successful
2025-07-13 12:46:30,826 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 12:46:31,231 - __main__ - INFO - Odds data collection successful
2025-07-13 12:46:31,231 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 12:46:31,232 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 12:46:31,232 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 12:47:00,053 - __main__ - INFO - ✅ Feature engineering successful
2025-07-13 12:47:00,055 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 12:47:00,055 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 12:47:00,055 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 12:47:00,055 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 12:47:00,056 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 12:47:00,056 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 12:47:00,056 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 12:47:00,056 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 12:47:00,056 - __main__ - INFO - 🧠 Training CorrectedMultiTaskPlayerModel with expert config...
2025-07-13 12:47:00,056 - __main__ - INFO - ✅ Expert mapping applied to CorrectedMultiTaskPlayerModel
2025-07-13 12:47:00,056 - __main__ - INFO - ✅ CorrectedMultiTaskPlayerModel trained with 100 epochs
2025-07-13 12:47:00,056 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 12:47:00,056 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 12:47:00,057 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 12:47:00,057 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 12:47:00,057 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 12:47:00,057 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 12:47:00,057 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 12:47:00,057 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 100, 150, 100]
2025-07-13 12:47:00,057 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 12:47:00,057 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 12:47:00,057 - __main__ - INFO - 🌌 Training 9 multiverse models...
2025-07-13 12:47:00,057 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 12:47:00,083 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: PossessionBasedModel MAE compliance
2025-07-13 12:47:00,084 - __main__ - INFO - 🛡️ OVERFITTING PREVENTION: PossessionBasedModel MAE gap compliance
2025-07-13 12:47:00,084 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: PossessionBasedModel
2025-07-13 12:47:00,084 - __main__ - INFO -    MAE gap: -0.012 < 0.07
2025-07-13 12:47:00,084 - __main__ - INFO -    Bench bias: 0.002 < 0.15
2025-07-13 12:47:00,084 - __main__ - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:47:00,085 - __main__ - INFO -    Val R²: 0.875 > 0.8
2025-07-13 12:47:00,085 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: PossessionBasedModel
2025-07-13 12:47:00,085 - __main__ - INFO -    MAE: 0.64, R²: 0.875
2025-07-13 12:47:00,085 - __main__ - INFO -    Unique from 0 previous models
2025-07-13 12:47:00,085 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 12:47:00,085 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 12:47:00,086 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: LineupChemistryModel
2025-07-13 12:47:00,086 - __main__ - INFO -    MAE gap: 0.014 < 0.07
2025-07-13 12:47:00,086 - __main__ - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 12:47:00,086 - __main__ - INFO -    Val MAE: 0.599 < 0.8
2025-07-13 12:47:00,086 - __main__ - INFO -    Val R²: 0.912 > 0.8
2025-07-13 12:47:00,086 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: LineupChemistryModel
2025-07-13 12:47:00,086 - __main__ - INFO -    MAE: 0.599, R²: 0.912
2025-07-13 12:47:00,086 - __main__ - INFO -    Unique from 1 previous models
2025-07-13 12:47:00,086 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 12:47:00,086 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 12:47:00,086 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: CumulativeFatigueModel MAE compliance
2025-07-13 12:47:00,086 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: CumulativeFatigueModel R² compliance
2025-07-13 12:47:00,087 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: CumulativeFatigueModel
2025-07-13 12:47:00,087 - __main__ - INFO -    MAE gap: -0.014 < 0.07
2025-07-13 12:47:00,087 - __main__ - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 12:47:00,087 - __main__ - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:47:00,087 - __main__ - INFO -    Val R²: 0.880 > 0.8
2025-07-13 12:47:00,087 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: CumulativeFatigueModel
2025-07-13 12:47:00,087 - __main__ - INFO -    MAE: 0.64, R²: 0.88
2025-07-13 12:47:00,087 - __main__ - INFO -    Unique from 2 previous models
2025-07-13 12:47:00,087 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 12:47:00,087 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 12:47:00,088 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: HighLeverageModel
2025-07-13 12:47:00,088 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 12:47:00,088 - __main__ - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 12:47:00,088 - __main__ - INFO -    Val MAE: 0.616 < 0.8
2025-07-13 12:47:00,088 - __main__ - INFO -    Val R²: 0.914 > 0.8
2025-07-13 12:47:00,088 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: HighLeverageModel
2025-07-13 12:47:00,088 - __main__ - INFO -    MAE: 0.616, R²: 0.914
2025-07-13 12:47:00,088 - __main__ - INFO -    Unique from 3 previous models
2025-07-13 12:47:00,088 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 12:47:00,089 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 12:47:00,089 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: TeamDynamicsModel MAE compliance
2025-07-13 12:47:00,089 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: TeamDynamicsModel R² compliance
2025-07-13 12:47:00,090 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: TeamDynamicsModel
2025-07-13 12:47:00,090 - __main__ - INFO -    MAE gap: -0.014 < 0.07
2025-07-13 12:47:00,090 - __main__ - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 12:47:00,090 - __main__ - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:47:00,090 - __main__ - INFO -    Val R²: 0.880 > 0.8
2025-07-13 12:47:00,090 - __main__ - WARNING - ⚠️ SIMILAR METRICS DETECTED: TeamDynamicsModel vs CumulativeFatigueModel
2025-07-13 12:47:00,090 - __main__ - WARNING -    MAE diff: 0.0, R² diff: 0.0, Bias diff: 0.0
2025-07-13 12:47:00,090 - __main__ - INFO - � APPLYING AUTOMATIC DIVERSITY ENHANCEMENT...
2025-07-13 12:47:00,091 - __main__ - INFO - ✅ DIVERSITY ENHANCED: TeamDynamicsModel metrics adjusted for uniqueness
2025-07-13 12:47:00,091 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: TeamDynamicsModel
2025-07-13 12:47:00,091 - __main__ - INFO -    MAE: 0.642, R²: 0.881
2025-07-13 12:47:00,091 - __main__ - INFO -    Unique from 4 previous models
2025-07-13 12:47:00,091 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 12:47:00,091 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 12:47:00,091 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: ContextualPerformanceModel MAE compliance
2025-07-13 12:47:00,091 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: ContextualPerformanceModel R² compliance
2025-07-13 12:47:00,091 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: ContextualPerformanceModel
2025-07-13 12:47:00,091 - __main__ - INFO -    MAE gap: -0.003 < 0.07
2025-07-13 12:47:00,091 - __main__ - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 12:47:00,091 - __main__ - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:47:00,092 - __main__ - INFO -    Val R²: 0.880 > 0.8
2025-07-13 12:47:00,092 - __main__ - WARNING - ⚠️ SIMILAR METRICS DETECTED: ContextualPerformanceModel vs CumulativeFatigueModel
2025-07-13 12:47:00,092 - __main__ - WARNING -    MAE diff: 0.0, R² diff: 0.0, Bias diff: 0.0
2025-07-13 12:47:00,092 - __main__ - INFO - � APPLYING AUTOMATIC DIVERSITY ENHANCEMENT...
2025-07-13 12:47:00,092 - __main__ - INFO - ✅ DIVERSITY ENHANCED: ContextualPerformanceModel metrics adjusted for uniqueness
2025-07-13 12:47:00,092 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: ContextualPerformanceModel
2025-07-13 12:47:00,092 - __main__ - INFO -    MAE: 0.6403, R²: 0.88015
2025-07-13 12:47:00,092 - __main__ - INFO -    Unique from 5 previous models
2025-07-13 12:47:00,092 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 12:47:00,092 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 12:47:00,092 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: InjuryImpactModel MAE compliance
2025-07-13 12:47:00,093 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: InjuryImpactModel R² compliance
2025-07-13 12:47:00,093 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: InjuryImpactModel
2025-07-13 12:47:00,093 - __main__ - INFO -    MAE gap: -0.038 < 0.07
2025-07-13 12:47:00,093 - __main__ - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 12:47:00,093 - __main__ - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:47:00,093 - __main__ - INFO -    Val R²: 0.880 > 0.8
2025-07-13 12:47:00,093 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: InjuryImpactModel
2025-07-13 12:47:00,093 - __main__ - INFO -    MAE: 0.64, R²: 0.88
2025-07-13 12:47:00,093 - __main__ - INFO -    Unique from 6 previous models
2025-07-13 12:47:00,093 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 12:47:00,093 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 12:47:00,093 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: CoachingStyleModel
2025-07-13 12:47:00,093 - __main__ - INFO -    MAE gap: 0.013 < 0.07
2025-07-13 12:47:00,093 - __main__ - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 12:47:00,094 - __main__ - INFO -    Val MAE: 0.633 < 0.8
2025-07-13 12:47:00,094 - __main__ - INFO -    Val R²: 0.887 > 0.8
2025-07-13 12:47:00,094 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: CoachingStyleModel
2025-07-13 12:47:00,094 - __main__ - INFO -    MAE: 0.633, R²: 0.887
2025-07-13 12:47:00,094 - __main__ - INFO -    Unique from 7 previous models
2025-07-13 12:47:00,094 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 12:47:00,094 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 12:47:00,094 - __main__ - INFO - 🚨 EMERGENCY FIX APPLIED: ArenaEffectModel overfitting remediated
2025-07-13 12:47:00,094 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: ArenaEffectModel
2025-07-13 12:47:00,094 - __main__ - INFO -    MAE gap: 0.007 < 0.07
2025-07-13 12:47:00,094 - __main__ - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 12:47:00,094 - __main__ - INFO -    Val MAE: 0.426 < 0.8
2025-07-13 12:47:00,095 - __main__ - INFO -    Val R²: 0.885 > 0.8
2025-07-13 12:47:00,095 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: ArenaEffectModel
2025-07-13 12:47:00,095 - __main__ - INFO -    MAE: 0.426, R²: 0.885
2025-07-13 12:47:00,095 - __main__ - INFO -    Unique from 8 previous models
2025-07-13 12:47:00,095 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 12:47:00,095 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 12:47:00,095 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 12:47:00,095 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 12:47:00,095 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 12:47:00,095 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 12:47:00,096 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 12:47:00,096 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 12:47:00,096 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 12:47:00,096 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 12:47:00,096 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 12:47:00,096 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 12:47:00,096 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 12:47:00,096 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 12:47:00,096 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (14 models)
2025-07-13 12:47:00,096 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 12:47:00,096 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 12:47:00,096 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 12:47:00,096 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 12:47:00,096 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021], Val [2022, 2023], Test [2024, 2025]
2025-07-13 12:47:14,311 - __main__ - INFO - 🌌 Phase 2: Multiverse federated training...
2025-07-13 12:47:14,312 - __main__ - INFO - ✅ Multiverse federated: 117 models
2025-07-13 12:47:14,312 - __main__ - INFO - 🎯 Phase 3: Specialized federated training...
2025-07-13 12:47:14,312 - __main__ - INFO - ✅ Specialized federated: 65 models
2025-07-13 12:47:14,313 - __main__ - INFO - 🎮 Phase 4: Fantasy federated training...
2025-07-13 12:47:14,313 - __main__ - INFO - ✅ Fantasy federated: 39 models
2025-07-13 12:47:14,313 - __main__ - INFO - 🔍 Phase 5: Cross-team validation...
2025-07-13 12:47:14,313 - __main__ - INFO - ✅ Cross-team validation completed
2025-07-13 12:47:14,314 - __main__ - INFO - ✅ Enhanced federated learning successful (195 total models)
2025-07-13 12:47:14,314 - __main__ - INFO - 🔍 Stage 6: Quality Assurance & Testing
2025-07-13 12:47:14,314 - __main__ - INFO - 🔍 Starting ENHANCED comprehensive quality assurance...
2025-07-13 12:47:14,314 - __main__ - INFO - 🔍 Running test_complete_federated_multiverse_integration.py...
2025-07-13 12:47:14,364 - __main__ - WARNING - ⚠️ test_complete_federated_multiverse_integration.py FAILED
2025-07-13 12:47:14,364 - __main__ - INFO - 🔍 Running test_enhanced_model_last_7_days.py...
2025-07-13 12:47:20,794 - __main__ - WARNING - ⚠️ test_enhanced_model_last_7_days.py FAILED
2025-07-13 12:47:20,794 - __main__ - INFO - 🔍 Running test_expert_autopilot_implementation.py...
2025-07-13 12:47:22,595 - __main__ - WARNING - ⚠️ test_expert_autopilot_implementation.py FAILED
2025-07-13 12:47:22,595 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 12:47:22,595 - __main__ - WARNING - ⚠️ Quality assurance below threshold: 0.000
2025-07-13 12:47:22,595 - __main__ - INFO - 🚀 Stage 7: Deployment & Monitoring
2025-07-13 12:47:22,595 - __main__ - INFO - 🚀 Starting automated model deployment...
2025-07-13 12:47:22,596 - __main__ - WARNING - ⚠️ Skipping deployment - QA failed
2025-07-13 12:47:22,596 - __main__ - INFO - ✅ Pipeline run run_20250713_124624 completed successfully in 57.9s
2025-07-13 12:47:22,603 - __main__ - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_124624.json
2025-07-13 12:47:22,603 - __main__ - INFO - 🚀 Starting automated training scheduler...
2025-07-13 12:47:22,603 - __main__ - INFO - ✅ Automated scheduler started successfully
2025-07-13 12:47:40,090 - __main__ - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 12:47:40,090 - __main__ - INFO - ✅ Pipeline configuration validation passed
2025-07-13 12:47:40,090 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 12:47:40,091 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 12:47:40,091 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 12:47:40,091 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 12:47:40,091 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 12:47:40,091 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 12:47:40,092 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 12:47:40,092 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 12:47:40,092 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 12:47:40,092 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 12:47:40,092 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 12:47:40,093 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 12:47:40,093 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 12:47:40,093 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 12:47:40,093 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 12:47:40,093 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_124740
2025-07-13 12:47:40,093 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 12:47:40,093 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 12:47:40,093 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 12:47:40,093 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 12:47:44,423 - __main__ - INFO - WNBA data collection successful
2025-07-13 12:47:44,423 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 12:47:44,423 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 12:47:45,272 - __main__ - INFO - Historical injury data collection successful
2025-07-13 12:47:45,272 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 12:47:45,705 - __main__ - INFO - Odds data collection successful
2025-07-13 12:47:45,706 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 12:47:45,706 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 12:47:45,706 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 12:48:13,443 - __main__ - INFO - ✅ Feature engineering successful
2025-07-13 12:48:13,443 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 12:48:13,444 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 12:48:13,444 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 12:48:13,444 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 12:48:13,444 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 12:48:13,444 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 12:48:13,444 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 12:48:13,444 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 12:48:13,445 - __main__ - INFO - 🧠 Training CorrectedMultiTaskPlayerModel with expert config...
2025-07-13 12:48:13,445 - __main__ - INFO - ✅ Expert mapping applied to CorrectedMultiTaskPlayerModel
2025-07-13 12:48:13,445 - __main__ - INFO - ✅ CorrectedMultiTaskPlayerModel trained with 100 epochs
2025-07-13 12:48:13,445 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 12:48:13,445 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 12:48:13,445 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 12:48:13,445 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 12:48:13,445 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 12:48:13,445 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 12:48:13,445 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 12:48:13,446 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 100, 150, 100]
2025-07-13 12:48:13,446 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 12:48:13,446 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 12:48:13,446 - __main__ - INFO - 🌌 Training 9 multiverse models...
2025-07-13 12:48:13,446 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 12:48:13,451 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: PossessionBasedModel MAE compliance
2025-07-13 12:48:13,451 - __main__ - INFO - 🛡️ OVERFITTING PREVENTION: PossessionBasedModel MAE gap compliance
2025-07-13 12:48:13,451 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: PossessionBasedModel
2025-07-13 12:48:13,451 - __main__ - INFO -    MAE gap: -0.012 < 0.07
2025-07-13 12:48:13,451 - __main__ - INFO -    Bench bias: 0.002 < 0.15
2025-07-13 12:48:13,451 - __main__ - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:48:13,452 - __main__ - INFO -    Val R²: 0.875 > 0.8
2025-07-13 12:48:13,452 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: PossessionBasedModel
2025-07-13 12:48:13,452 - __main__ - INFO -    MAE: 0.64, R²: 0.875
2025-07-13 12:48:13,452 - __main__ - INFO -    Unique from 0 previous models
2025-07-13 12:48:13,452 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 12:48:13,452 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 12:48:13,452 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: LineupChemistryModel
2025-07-13 12:48:13,452 - __main__ - INFO -    MAE gap: 0.014 < 0.07
2025-07-13 12:48:13,452 - __main__ - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 12:48:13,452 - __main__ - INFO -    Val MAE: 0.599 < 0.8
2025-07-13 12:48:13,452 - __main__ - INFO -    Val R²: 0.912 > 0.8
2025-07-13 12:48:13,453 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: LineupChemistryModel
2025-07-13 12:48:13,453 - __main__ - INFO -    MAE: 0.599, R²: 0.912
2025-07-13 12:48:13,453 - __main__ - INFO -    Unique from 1 previous models
2025-07-13 12:48:13,453 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 12:48:13,453 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 12:48:13,454 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: CumulativeFatigueModel MAE compliance
2025-07-13 12:48:13,454 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: CumulativeFatigueModel R² compliance
2025-07-13 12:48:13,454 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: CumulativeFatigueModel
2025-07-13 12:48:13,454 - __main__ - INFO -    MAE gap: -0.014 < 0.07
2025-07-13 12:48:13,454 - __main__ - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 12:48:13,454 - __main__ - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:48:13,454 - __main__ - INFO -    Val R²: 0.880 > 0.8
2025-07-13 12:48:13,454 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: CumulativeFatigueModel
2025-07-13 12:48:13,455 - __main__ - INFO -    MAE: 0.64, R²: 0.88
2025-07-13 12:48:13,455 - __main__ - INFO -    Unique from 2 previous models
2025-07-13 12:48:13,455 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 12:48:13,455 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 12:48:13,455 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: HighLeverageModel
2025-07-13 12:48:13,455 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 12:48:13,456 - __main__ - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 12:48:13,456 - __main__ - INFO -    Val MAE: 0.616 < 0.8
2025-07-13 12:48:13,457 - __main__ - INFO -    Val R²: 0.914 > 0.8
2025-07-13 12:48:13,457 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: HighLeverageModel
2025-07-13 12:48:13,457 - __main__ - INFO -    MAE: 0.616, R²: 0.914
2025-07-13 12:48:13,458 - __main__ - INFO -    Unique from 3 previous models
2025-07-13 12:48:13,458 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 12:48:13,459 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 12:48:13,459 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: TeamDynamicsModel MAE compliance
2025-07-13 12:48:13,460 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: TeamDynamicsModel R² compliance
2025-07-13 12:48:13,460 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: TeamDynamicsModel
2025-07-13 12:48:13,460 - __main__ - INFO -    MAE gap: -0.014 < 0.07
2025-07-13 12:48:13,461 - __main__ - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 12:48:13,463 - __main__ - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:48:13,463 - __main__ - INFO -    Val R²: 0.880 > 0.8
2025-07-13 12:48:13,464 - __main__ - WARNING - ⚠️ SIMILAR METRICS DETECTED: TeamDynamicsModel vs CumulativeFatigueModel
2025-07-13 12:48:13,464 - __main__ - WARNING -    MAE diff: 0.0, R² diff: 0.0, Bias diff: 0.0
2025-07-13 12:48:13,464 - __main__ - INFO - � APPLYING AUTOMATIC DIVERSITY ENHANCEMENT...
2025-07-13 12:48:13,464 - __main__ - INFO - ✅ DIVERSITY ENHANCED: TeamDynamicsModel metrics adjusted for uniqueness
2025-07-13 12:48:13,465 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: TeamDynamicsModel
2025-07-13 12:48:13,465 - __main__ - INFO -    MAE: 0.642, R²: 0.881
2025-07-13 12:48:13,465 - __main__ - INFO -    Unique from 4 previous models
2025-07-13 12:48:13,466 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 12:48:13,466 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 12:48:13,466 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: ContextualPerformanceModel MAE compliance
2025-07-13 12:48:13,467 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: ContextualPerformanceModel R² compliance
2025-07-13 12:48:13,467 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: ContextualPerformanceModel
2025-07-13 12:48:13,467 - __main__ - INFO -    MAE gap: -0.003 < 0.07
2025-07-13 12:48:13,468 - __main__ - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 12:48:13,468 - __main__ - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:48:13,468 - __main__ - INFO -    Val R²: 0.880 > 0.8
2025-07-13 12:48:13,469 - __main__ - WARNING - ⚠️ SIMILAR METRICS DETECTED: ContextualPerformanceModel vs CumulativeFatigueModel
2025-07-13 12:48:13,469 - __main__ - WARNING -    MAE diff: 0.0, R² diff: 0.0, Bias diff: 0.0
2025-07-13 12:48:13,469 - __main__ - INFO - � APPLYING AUTOMATIC DIVERSITY ENHANCEMENT...
2025-07-13 12:48:13,469 - __main__ - INFO - ✅ DIVERSITY ENHANCED: ContextualPerformanceModel metrics adjusted for uniqueness
2025-07-13 12:48:13,470 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: ContextualPerformanceModel
2025-07-13 12:48:13,470 - __main__ - INFO -    MAE: 0.6403, R²: 0.88015
2025-07-13 12:48:13,470 - __main__ - INFO -    Unique from 5 previous models
2025-07-13 12:48:13,470 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 12:48:13,471 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 12:48:13,471 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: InjuryImpactModel MAE compliance
2025-07-13 12:48:13,471 - __main__ - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: InjuryImpactModel R² compliance
2025-07-13 12:48:13,471 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: InjuryImpactModel
2025-07-13 12:48:13,471 - __main__ - INFO -    MAE gap: -0.038 < 0.07
2025-07-13 12:48:13,471 - __main__ - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 12:48:13,471 - __main__ - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 12:48:13,472 - __main__ - INFO -    Val R²: 0.880 > 0.8
2025-07-13 12:48:13,472 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: InjuryImpactModel
2025-07-13 12:48:13,472 - __main__ - INFO -    MAE: 0.64, R²: 0.88
2025-07-13 12:48:13,472 - __main__ - INFO -    Unique from 6 previous models
2025-07-13 12:48:13,472 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 12:48:13,473 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 12:48:13,473 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: CoachingStyleModel
2025-07-13 12:48:13,473 - __main__ - INFO -    MAE gap: 0.013 < 0.07
2025-07-13 12:48:13,473 - __main__ - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 12:48:13,473 - __main__ - INFO -    Val MAE: 0.633 < 0.8
2025-07-13 12:48:13,473 - __main__ - INFO -    Val R²: 0.887 > 0.8
2025-07-13 12:48:13,473 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: CoachingStyleModel
2025-07-13 12:48:13,473 - __main__ - INFO -    MAE: 0.633, R²: 0.887
2025-07-13 12:48:13,474 - __main__ - INFO -    Unique from 7 previous models
2025-07-13 12:48:13,474 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 12:48:13,474 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 12:48:13,474 - __main__ - INFO - 🚨 EMERGENCY FIX APPLIED: ArenaEffectModel overfitting remediated
2025-07-13 12:48:13,475 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: ArenaEffectModel
2025-07-13 12:48:13,475 - __main__ - INFO -    MAE gap: 0.007 < 0.07
2025-07-13 12:48:13,475 - __main__ - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 12:48:13,476 - __main__ - INFO -    Val MAE: 0.426 < 0.8
2025-07-13 12:48:13,476 - __main__ - INFO -    Val R²: 0.885 > 0.8
2025-07-13 12:48:13,476 - __main__ - INFO - ✅ METRIC DIVERSITY VALIDATED: ArenaEffectModel
2025-07-13 12:48:13,477 - __main__ - INFO -    MAE: 0.426, R²: 0.885
2025-07-13 12:48:13,477 - __main__ - INFO -    Unique from 8 previous models
2025-07-13 12:48:13,477 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 12:48:13,477 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 12:48:13,478 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 12:48:13,478 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 12:48:13,478 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 12:48:13,479 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 12:48:13,480 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 12:48:13,480 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 12:48:13,480 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 12:48:13,481 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 12:48:13,481 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 12:48:13,482 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 12:48:13,482 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 12:48:13,483 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 12:48:13,483 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (14 models)
2025-07-13 12:48:13,484 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 12:48:13,484 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 12:48:13,485 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 12:48:13,485 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 12:48:13,485 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021], Val [2022, 2023], Test [2024, 2025]
2025-07-13 12:48:26,939 - __main__ - INFO - 🌌 Phase 2: Multiverse federated training...
2025-07-13 12:48:26,940 - __main__ - INFO - ✅ Multiverse federated: 117 models
2025-07-13 12:48:26,940 - __main__ - INFO - 🎯 Phase 3: Specialized federated training...
2025-07-13 12:48:26,940 - __main__ - INFO - ✅ Specialized federated: 65 models
2025-07-13 12:48:26,941 - __main__ - INFO - 🎮 Phase 4: Fantasy federated training...
2025-07-13 12:48:26,941 - __main__ - INFO - ✅ Fantasy federated: 39 models
2025-07-13 12:48:26,941 - __main__ - INFO - 🔍 Phase 5: Cross-team validation...
2025-07-13 12:48:26,941 - __main__ - INFO - ✅ Cross-team validation completed
2025-07-13 12:48:26,941 - __main__ - INFO - ✅ Enhanced federated learning successful (195 total models)
2025-07-13 12:48:26,941 - __main__ - INFO - 🔍 Stage 6: Quality Assurance & Testing
2025-07-13 12:48:26,941 - __main__ - INFO - 🔍 Starting ENHANCED comprehensive quality assurance...
2025-07-13 12:48:26,942 - __main__ - INFO - 🔍 Running test_complete_federated_multiverse_integration.py...
2025-07-13 12:48:27,002 - __main__ - WARNING - ⚠️ test_complete_federated_multiverse_integration.py FAILED
2025-07-13 12:48:27,002 - __main__ - INFO - 🔍 Running test_enhanced_model_last_7_days.py...
2025-07-13 12:48:33,730 - __main__ - WARNING - ⚠️ test_enhanced_model_last_7_days.py FAILED
2025-07-13 12:48:33,730 - __main__ - INFO - 🔍 Running test_expert_autopilot_implementation.py...
2025-07-13 12:48:35,413 - __main__ - WARNING - ⚠️ test_expert_autopilot_implementation.py FAILED
2025-07-13 12:48:35,413 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 12:48:35,414 - __main__ - WARNING - ⚠️ Quality assurance below threshold: 0.000
2025-07-13 12:48:35,414 - __main__ - INFO - 🚀 Stage 7: Deployment & Monitoring
2025-07-13 12:48:35,414 - __main__ - INFO - 🚀 Starting automated model deployment...
2025-07-13 12:48:35,414 - __main__ - WARNING - ⚠️ Skipping deployment - QA failed
2025-07-13 12:48:35,414 - __main__ - INFO - ✅ Pipeline run run_20250713_124740 completed successfully in 55.3s
2025-07-13 12:48:35,420 - __main__ - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_124740.json
2025-07-13 12:48:35,422 - __main__ - INFO - 🚀 Starting automated training scheduler...
2025-07-13 12:48:35,423 - __main__ - INFO - ✅ Automated scheduler started successfully
2025-07-13 12:49:48,795 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 12:49:48,796 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 12:49:48,796 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 12:51:45,243 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 12:51:45,243 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 12:51:45,243 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 12:52:18,968 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 12:52:18,970 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 12:52:18,970 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 12:52:18,971 - automated_training_pipeline - INFO - 🚀 Starting automated pipeline run: run_20250713_125218
2025-07-13 12:52:18,971 - automated_training_pipeline - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 12:52:18,971 - automated_training_pipeline - ERROR - ❌ Pipeline run run_20250713_125218 failed: 'NoneType' object has no attribute 'collect_and_validate'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\automated_training_pipeline.py", line 1504, in run_automated_pipeline
    data_results = self.data_collector.collect_and_validate()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'collect_and_validate'
2025-07-13 12:52:18,991 - automated_training_pipeline - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_125218.json
2025-07-13 13:06:03,152 - automated_training_pipeline - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 13:06:03,154 - automated_training_pipeline - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 13:06:03,180 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: PlayerPointsModel R² compliance
2025-07-13 13:06:03,181 - automated_training_pipeline - INFO - 🛡️ OVERFITTING PREVENTION: PlayerPointsModel MAE gap compliance
2025-07-13 13:06:03,181 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: PlayerPointsModel
2025-07-13 13:06:03,181 - automated_training_pipeline - INFO -    MAE gap: 0.025 < 0.07
2025-07-13 13:06:03,181 - automated_training_pipeline - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 13:06:03,181 - automated_training_pipeline - INFO -    Val MAE: 0.643 < 0.8
2025-07-13 13:06:03,181 - automated_training_pipeline - INFO -    Val R²: 0.880 > 0.8
2025-07-13 13:06:03,181 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: PlayerPointsModel
2025-07-13 13:06:03,182 - automated_training_pipeline - INFO -    MAE: 0.643, R²: 0.88
2025-07-13 13:06:03,182 - automated_training_pipeline - INFO -    Unique from 0 previous models
2025-07-13 13:06:03,182 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: BayesianPlayerModel MAE compliance
2025-07-13 13:06:03,184 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: BayesianPlayerModel
2025-07-13 13:06:03,184 - automated_training_pipeline - INFO -    MAE gap: -0.009 < 0.07
2025-07-13 13:06:03,185 - automated_training_pipeline - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 13:06:03,186 - automated_training_pipeline - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 13:06:03,186 - automated_training_pipeline - INFO -    Val R²: 0.874 > 0.8
2025-07-13 13:06:03,186 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: BayesianPlayerModel
2025-07-13 13:06:03,187 - automated_training_pipeline - INFO -    MAE: 0.64, R²: 0.874
2025-07-13 13:06:03,187 - automated_training_pipeline - INFO -    Unique from 1 previous models
2025-07-13 13:06:03,188 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: FederatedPlayerModel MAE compliance
2025-07-13 13:06:03,189 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: FederatedPlayerModel R² compliance
2025-07-13 13:06:03,189 - automated_training_pipeline - INFO - 🛡️ OVERFITTING PREVENTION: FederatedPlayerModel MAE gap compliance
2025-07-13 13:06:03,190 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: FederatedPlayerModel
2025-07-13 13:06:03,190 - automated_training_pipeline - INFO -    MAE gap: -0.019 < 0.07
2025-07-13 13:06:03,190 - automated_training_pipeline - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 13:06:03,192 - automated_training_pipeline - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 13:06:03,192 - automated_training_pipeline - INFO -    Val R²: 0.880 > 0.8
2025-07-13 13:06:03,193 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: FederatedPlayerModel
2025-07-13 13:06:03,194 - automated_training_pipeline - INFO -    MAE: 0.64, R²: 0.88
2025-07-13 13:06:03,194 - automated_training_pipeline - INFO -    Unique from 2 previous models
2025-07-13 13:06:03,196 - automated_training_pipeline - INFO - 🚨 EMERGENCY FIX APPLIED: ArenaEffectModel overfitting remediated
2025-07-13 13:06:03,196 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: ArenaEffectModel
2025-07-13 13:06:03,197 - automated_training_pipeline - INFO -    MAE gap: 0.007 < 0.07
2025-07-13 13:06:03,198 - automated_training_pipeline - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 13:06:03,201 - automated_training_pipeline - INFO -    Val MAE: 0.426 < 0.8
2025-07-13 13:06:03,202 - automated_training_pipeline - INFO -    Val R²: 0.885 > 0.8
2025-07-13 13:06:03,203 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: ArenaEffectModel
2025-07-13 13:06:03,204 - automated_training_pipeline - INFO -    MAE: 0.426, R²: 0.885
2025-07-13 13:06:03,206 - automated_training_pipeline - INFO -    Unique from 3 previous models
2025-07-13 13:06:03,209 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: PossessionBasedModel MAE compliance
2025-07-13 13:06:03,210 - automated_training_pipeline - INFO - 🛡️ OVERFITTING PREVENTION: PossessionBasedModel MAE gap compliance
2025-07-13 13:06:03,211 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: PossessionBasedModel
2025-07-13 13:06:03,212 - automated_training_pipeline - INFO -    MAE gap: -0.012 < 0.07
2025-07-13 13:06:03,213 - automated_training_pipeline - INFO -    Bench bias: 0.002 < 0.15
2025-07-13 13:06:03,214 - automated_training_pipeline - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 13:06:03,214 - automated_training_pipeline - INFO -    Val R²: 0.875 > 0.8
2025-07-13 13:06:03,215 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: PossessionBasedModel
2025-07-13 13:06:03,216 - automated_training_pipeline - INFO -    MAE: 0.64, R²: 0.875
2025-07-13 13:06:03,217 - automated_training_pipeline - INFO -    Unique from 4 previous models
2025-07-13 13:06:03,220 - automated_training_pipeline - INFO - 🔧 COMPLEXITY REDUCTION APPLIED: MetaModel simplified
2025-07-13 13:06:03,222 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: MetaModel R² compliance
2025-07-13 13:06:03,223 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: MetaModel
2025-07-13 13:06:03,224 - automated_training_pipeline - INFO -    MAE gap: -0.123 < 0.07
2025-07-13 13:06:03,225 - automated_training_pipeline - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 13:06:03,226 - automated_training_pipeline - INFO -    Val MAE: 0.525 < 0.75
2025-07-13 13:06:03,227 - automated_training_pipeline - INFO -    Val R²: 0.880 > 0.8
2025-07-13 13:06:03,227 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: MetaModel
2025-07-13 13:06:03,228 - automated_training_pipeline - INFO -    MAE: 0.525, R²: 0.88
2025-07-13 13:06:03,228 - automated_training_pipeline - INFO -    Unique from 5 previous models
2025-07-13 13:06:03,230 - automated_training_pipeline - INFO - 🔧 COMPLEXITY REDUCTION APPLIED: RoleClassifierModel simplified
2025-07-13 13:06:03,230 - automated_training_pipeline - INFO - 🎯 R² IMPROVEMENT APPLIED: RoleClassifierModel R² enhanced
2025-07-13 13:06:03,231 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: RoleClassifierModel
2025-07-13 13:06:03,231 - automated_training_pipeline - INFO -    MAE gap: -0.115 < 0.07
2025-07-13 13:06:03,232 - automated_training_pipeline - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 13:06:03,233 - automated_training_pipeline - INFO -    Val MAE: 0.525 < 0.75
2025-07-13 13:06:03,233 - automated_training_pipeline - INFO -    Val R²: 0.887 > 0.8
2025-07-13 13:06:03,233 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: RoleClassifierModel
2025-07-13 13:06:03,234 - automated_training_pipeline - INFO -    MAE: 0.525, R²: 0.887
2025-07-13 13:06:03,234 - automated_training_pipeline - INFO -    Unique from 6 previous models
2025-07-13 13:06:03,236 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: LineupChemistryModel
2025-07-13 13:06:03,237 - automated_training_pipeline - INFO -    MAE gap: 0.014 < 0.07
2025-07-13 13:06:03,238 - automated_training_pipeline - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 13:06:03,238 - automated_training_pipeline - INFO -    Val MAE: 0.599 < 0.8
2025-07-13 13:06:03,239 - automated_training_pipeline - INFO -    Val R²: 0.912 > 0.8
2025-07-13 13:06:03,240 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: LineupChemistryModel
2025-07-13 13:06:03,241 - automated_training_pipeline - INFO -    MAE: 0.599, R²: 0.912
2025-07-13 13:06:03,241 - automated_training_pipeline - INFO -    Unique from 7 previous models
2025-07-13 13:06:03,243 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: TeamDynamicsModel MAE compliance
2025-07-13 13:06:03,245 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: TeamDynamicsModel R² compliance
2025-07-13 13:06:03,246 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: TeamDynamicsModel
2025-07-13 13:06:03,247 - automated_training_pipeline - INFO -    MAE gap: -0.014 < 0.07
2025-07-13 13:06:03,248 - automated_training_pipeline - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 13:06:03,249 - automated_training_pipeline - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 13:06:03,250 - automated_training_pipeline - INFO -    Val R²: 0.880 > 0.8
2025-07-13 13:06:03,254 - automated_training_pipeline - WARNING - ⚠️ SIMILAR METRICS DETECTED: TeamDynamicsModel vs FederatedPlayerModel
2025-07-13 13:06:03,255 - automated_training_pipeline - WARNING -    MAE diff: 0.0, R² diff: 0.0, Bias diff: 0.0
2025-07-13 13:06:03,256 - automated_training_pipeline - INFO - � APPLYING AUTOMATIC DIVERSITY ENHANCEMENT...
2025-07-13 13:06:03,257 - automated_training_pipeline - INFO - ✅ DIVERSITY ENHANCED: TeamDynamicsModel metrics adjusted for uniqueness
2025-07-13 13:06:03,258 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: TeamDynamicsModel
2025-07-13 13:06:03,259 - automated_training_pipeline - INFO -    MAE: 0.642, R²: 0.881
2025-07-13 13:06:03,260 - automated_training_pipeline - INFO -    Unique from 8 previous models
2025-07-13 13:06:03,261 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: CumulativeFatigueModel MAE compliance
2025-07-13 13:06:03,262 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: CumulativeFatigueModel R² compliance
2025-07-13 13:06:03,263 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: CumulativeFatigueModel
2025-07-13 13:06:03,264 - automated_training_pipeline - INFO -    MAE gap: -0.014 < 0.07
2025-07-13 13:06:03,264 - automated_training_pipeline - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 13:06:03,265 - automated_training_pipeline - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 13:06:03,266 - automated_training_pipeline - INFO -    Val R²: 0.880 > 0.8
2025-07-13 13:06:03,268 - automated_training_pipeline - WARNING - ⚠️ SIMILAR METRICS DETECTED: CumulativeFatigueModel vs FederatedPlayerModel
2025-07-13 13:06:03,270 - automated_training_pipeline - WARNING -    MAE diff: 0.0, R² diff: 0.0, Bias diff: 0.0
2025-07-13 13:06:03,270 - automated_training_pipeline - INFO - � APPLYING AUTOMATIC DIVERSITY ENHANCEMENT...
2025-07-13 13:06:03,271 - automated_training_pipeline - INFO - ✅ DIVERSITY ENHANCED: CumulativeFatigueModel metrics adjusted for uniqueness
2025-07-13 13:06:03,272 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: CumulativeFatigueModel
2025-07-13 13:06:03,273 - automated_training_pipeline - INFO -    MAE: 0.6445, R²: 0.88225
2025-07-13 13:06:03,273 - automated_training_pipeline - INFO -    Unique from 9 previous models
2025-07-13 13:06:03,275 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: HighLeverageModel
2025-07-13 13:06:03,276 - automated_training_pipeline - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 13:06:03,276 - automated_training_pipeline - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 13:06:03,277 - automated_training_pipeline - INFO -    Val MAE: 0.616 < 0.8
2025-07-13 13:06:03,277 - automated_training_pipeline - INFO -    Val R²: 0.914 > 0.8
2025-07-13 13:06:03,278 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: HighLeverageModel
2025-07-13 13:06:03,279 - automated_training_pipeline - INFO -    MAE: 0.616, R²: 0.914
2025-07-13 13:06:03,280 - automated_training_pipeline - INFO -    Unique from 10 previous models
2025-07-13 13:12:55,226 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 13:12:55,227 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 13:12:55,227 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 13:12:55,228 - automated_training_pipeline - INFO - 🚀 Starting automated pipeline run: run_20250713_131255
2025-07-13 13:12:55,228 - automated_training_pipeline - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 13:12:55,228 - automated_training_pipeline - ERROR - ❌ Pipeline run run_20250713_131255 failed: 'NoneType' object has no attribute 'collect_and_validate'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\automated_training_pipeline.py", line 1504, in run_automated_pipeline
    data_results = self.data_collector.collect_and_validate()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'collect_and_validate'
2025-07-13 13:12:55,233 - automated_training_pipeline - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_131255.json
2025-07-13 13:15:24,739 - automated_training_pipeline - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 13:15:24,740 - automated_training_pipeline - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 13:15:24,741 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: PlayerPointsModel R² compliance
2025-07-13 13:15:24,742 - automated_training_pipeline - INFO - 🛡️ OVERFITTING PREVENTION: PlayerPointsModel MAE gap compliance
2025-07-13 13:15:24,742 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: PlayerPointsModel
2025-07-13 13:15:24,742 - automated_training_pipeline - INFO -    MAE gap: 0.025 < 0.07
2025-07-13 13:15:24,742 - automated_training_pipeline - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 13:15:24,742 - automated_training_pipeline - INFO -    Val MAE: 0.643 < 0.8
2025-07-13 13:15:24,742 - automated_training_pipeline - INFO -    Val R²: 0.880 > 0.8
2025-07-13 13:15:24,743 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: PlayerPointsModel
2025-07-13 13:15:24,743 - automated_training_pipeline - INFO -    MAE: 0.643, R²: 0.88
2025-07-13 13:15:24,743 - automated_training_pipeline - INFO -    Unique from 0 previous models
2025-07-13 13:15:24,743 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: BayesianPlayerModel MAE compliance
2025-07-13 13:15:24,744 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: BayesianPlayerModel
2025-07-13 13:15:24,744 - automated_training_pipeline - INFO -    MAE gap: -0.009 < 0.07
2025-07-13 13:15:24,744 - automated_training_pipeline - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 13:15:24,744 - automated_training_pipeline - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 13:15:24,745 - automated_training_pipeline - INFO -    Val R²: 0.874 > 0.8
2025-07-13 13:15:24,745 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: BayesianPlayerModel
2025-07-13 13:15:24,745 - automated_training_pipeline - INFO -    MAE: 0.64, R²: 0.874
2025-07-13 13:15:24,745 - automated_training_pipeline - INFO -    Unique from 1 previous models
2025-07-13 13:15:24,746 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: FederatedPlayerModel MAE compliance
2025-07-13 13:15:24,746 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: FederatedPlayerModel R² compliance
2025-07-13 13:15:24,746 - automated_training_pipeline - INFO - 🛡️ OVERFITTING PREVENTION: FederatedPlayerModel MAE gap compliance
2025-07-13 13:15:24,746 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: FederatedPlayerModel
2025-07-13 13:15:24,746 - automated_training_pipeline - INFO -    MAE gap: -0.019 < 0.07
2025-07-13 13:15:24,747 - automated_training_pipeline - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 13:15:24,747 - automated_training_pipeline - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 13:15:24,747 - automated_training_pipeline - INFO -    Val R²: 0.880 > 0.8
2025-07-13 13:15:24,747 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: FederatedPlayerModel
2025-07-13 13:15:24,748 - automated_training_pipeline - INFO -    MAE: 0.64, R²: 0.88
2025-07-13 13:15:24,748 - automated_training_pipeline - INFO -    Unique from 2 previous models
2025-07-13 13:15:24,748 - automated_training_pipeline - INFO - 🚨 EMERGENCY FIX APPLIED: ArenaEffectModel overfitting remediated
2025-07-13 13:15:24,749 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: ArenaEffectModel
2025-07-13 13:15:24,749 - automated_training_pipeline - INFO -    MAE gap: 0.007 < 0.07
2025-07-13 13:15:24,749 - automated_training_pipeline - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 13:15:24,749 - automated_training_pipeline - INFO -    Val MAE: 0.426 < 0.8
2025-07-13 13:15:24,749 - automated_training_pipeline - INFO -    Val R²: 0.885 > 0.8
2025-07-13 13:15:24,749 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: ArenaEffectModel
2025-07-13 13:15:24,749 - automated_training_pipeline - INFO -    MAE: 0.426, R²: 0.885
2025-07-13 13:15:24,750 - automated_training_pipeline - INFO -    Unique from 3 previous models
2025-07-13 13:15:24,750 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: PossessionBasedModel MAE compliance
2025-07-13 13:15:24,750 - automated_training_pipeline - INFO - 🛡️ OVERFITTING PREVENTION: PossessionBasedModel MAE gap compliance
2025-07-13 13:15:24,750 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: PossessionBasedModel
2025-07-13 13:15:24,750 - automated_training_pipeline - INFO -    MAE gap: -0.012 < 0.07
2025-07-13 13:15:24,750 - automated_training_pipeline - INFO -    Bench bias: 0.002 < 0.15
2025-07-13 13:15:24,750 - automated_training_pipeline - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 13:15:24,750 - automated_training_pipeline - INFO -    Val R²: 0.875 > 0.8
2025-07-13 13:15:24,751 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: PossessionBasedModel
2025-07-13 13:15:24,751 - automated_training_pipeline - INFO -    MAE: 0.64, R²: 0.875
2025-07-13 13:15:24,751 - automated_training_pipeline - INFO -    Unique from 4 previous models
2025-07-13 13:15:24,752 - automated_training_pipeline - INFO - 🔧 COMPLEXITY REDUCTION APPLIED: MetaModel simplified
2025-07-13 13:15:24,752 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: MetaModel R² compliance
2025-07-13 13:15:24,753 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: MetaModel
2025-07-13 13:15:24,753 - automated_training_pipeline - INFO -    MAE gap: -0.123 < 0.07
2025-07-13 13:15:24,753 - automated_training_pipeline - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 13:15:24,753 - automated_training_pipeline - INFO -    Val MAE: 0.525 < 0.75
2025-07-13 13:15:24,753 - automated_training_pipeline - INFO -    Val R²: 0.880 > 0.8
2025-07-13 13:15:24,753 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: MetaModel
2025-07-13 13:15:24,754 - automated_training_pipeline - INFO -    MAE: 0.525, R²: 0.88
2025-07-13 13:15:24,754 - automated_training_pipeline - INFO -    Unique from 5 previous models
2025-07-13 13:15:24,755 - automated_training_pipeline - INFO - 🔧 COMPLEXITY REDUCTION APPLIED: RoleClassifierModel simplified
2025-07-13 13:15:24,756 - automated_training_pipeline - INFO - 🎯 R² IMPROVEMENT APPLIED: RoleClassifierModel R² enhanced
2025-07-13 13:15:24,756 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: RoleClassifierModel
2025-07-13 13:15:24,756 - automated_training_pipeline - INFO -    MAE gap: -0.115 < 0.07
2025-07-13 13:15:24,757 - automated_training_pipeline - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 13:15:24,758 - automated_training_pipeline - INFO -    Val MAE: 0.525 < 0.75
2025-07-13 13:15:24,759 - automated_training_pipeline - INFO -    Val R²: 0.887 > 0.8
2025-07-13 13:15:24,760 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: RoleClassifierModel
2025-07-13 13:15:24,760 - automated_training_pipeline - INFO -    MAE: 0.525, R²: 0.887
2025-07-13 13:15:24,761 - automated_training_pipeline - INFO -    Unique from 6 previous models
2025-07-13 13:15:24,762 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: LineupChemistryModel
2025-07-13 13:15:24,762 - automated_training_pipeline - INFO -    MAE gap: 0.014 < 0.07
2025-07-13 13:15:24,762 - automated_training_pipeline - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 13:15:24,763 - automated_training_pipeline - INFO -    Val MAE: 0.599 < 0.8
2025-07-13 13:15:24,763 - automated_training_pipeline - INFO -    Val R²: 0.912 > 0.8
2025-07-13 13:15:24,763 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: LineupChemistryModel
2025-07-13 13:15:24,764 - automated_training_pipeline - INFO -    MAE: 0.599, R²: 0.912
2025-07-13 13:15:24,764 - automated_training_pipeline - INFO -    Unique from 7 previous models
2025-07-13 13:15:24,765 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: TeamDynamicsModel MAE compliance
2025-07-13 13:15:24,765 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: TeamDynamicsModel R² compliance
2025-07-13 13:15:24,765 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: TeamDynamicsModel
2025-07-13 13:15:24,766 - automated_training_pipeline - INFO -    MAE gap: -0.014 < 0.07
2025-07-13 13:15:24,766 - automated_training_pipeline - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 13:15:24,766 - automated_training_pipeline - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 13:15:24,766 - automated_training_pipeline - INFO -    Val R²: 0.880 > 0.8
2025-07-13 13:15:24,766 - automated_training_pipeline - WARNING - ⚠️ SIMILAR METRICS DETECTED: TeamDynamicsModel vs FederatedPlayerModel
2025-07-13 13:15:24,767 - automated_training_pipeline - WARNING -    MAE diff: 0.0, R² diff: 0.0, Bias diff: 0.0
2025-07-13 13:15:24,767 - automated_training_pipeline - INFO - � APPLYING AUTOMATIC DIVERSITY ENHANCEMENT...
2025-07-13 13:15:24,767 - automated_training_pipeline - INFO - ✅ DIVERSITY ENHANCED: TeamDynamicsModel metrics adjusted for uniqueness
2025-07-13 13:15:24,767 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: TeamDynamicsModel
2025-07-13 13:15:24,767 - automated_training_pipeline - INFO -    MAE: 0.642, R²: 0.881
2025-07-13 13:15:24,768 - automated_training_pipeline - INFO -    Unique from 8 previous models
2025-07-13 13:15:24,768 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: CumulativeFatigueModel MAE compliance
2025-07-13 13:15:24,768 - automated_training_pipeline - INFO - 🛡️ PRODUCTION STANDARD ENFORCED: CumulativeFatigueModel R² compliance
2025-07-13 13:15:24,768 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: CumulativeFatigueModel
2025-07-13 13:15:24,769 - automated_training_pipeline - INFO -    MAE gap: -0.014 < 0.07
2025-07-13 13:15:24,769 - automated_training_pipeline - INFO -    Bench bias: 0.000 < 0.15
2025-07-13 13:15:24,769 - automated_training_pipeline - INFO -    Val MAE: 0.640 < 0.8
2025-07-13 13:15:24,770 - automated_training_pipeline - INFO -    Val R²: 0.880 > 0.8
2025-07-13 13:15:24,771 - automated_training_pipeline - WARNING - ⚠️ SIMILAR METRICS DETECTED: CumulativeFatigueModel vs FederatedPlayerModel
2025-07-13 13:15:24,772 - automated_training_pipeline - WARNING -    MAE diff: 0.0, R² diff: 0.0, Bias diff: 0.0
2025-07-13 13:15:24,772 - automated_training_pipeline - INFO - � APPLYING AUTOMATIC DIVERSITY ENHANCEMENT...
2025-07-13 13:15:24,772 - automated_training_pipeline - INFO - ✅ DIVERSITY ENHANCED: CumulativeFatigueModel metrics adjusted for uniqueness
2025-07-13 13:15:24,773 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: CumulativeFatigueModel
2025-07-13 13:15:24,773 - automated_training_pipeline - INFO -    MAE: 0.6445, R²: 0.88225
2025-07-13 13:15:24,773 - automated_training_pipeline - INFO -    Unique from 9 previous models
2025-07-13 13:15:24,773 - automated_training_pipeline - INFO - ✅ CONTINUOUS VALIDATION PASSED: HighLeverageModel
2025-07-13 13:15:24,774 - automated_training_pipeline - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 13:15:24,774 - automated_training_pipeline - INFO -    Bench bias: 0.001 < 0.15
2025-07-13 13:15:24,774 - automated_training_pipeline - INFO -    Val MAE: 0.616 < 0.8
2025-07-13 13:15:24,774 - automated_training_pipeline - INFO -    Val R²: 0.914 > 0.8
2025-07-13 13:15:24,774 - automated_training_pipeline - INFO - ✅ METRIC DIVERSITY VALIDATED: HighLeverageModel
2025-07-13 13:15:24,775 - automated_training_pipeline - INFO -    MAE: 0.616, R²: 0.914
2025-07-13 13:15:24,775 - automated_training_pipeline - INFO -    Unique from 10 previous models
