2025-07-13 03:35:24,841 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 03:35:24,841 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 03:35:24,841 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 03:35:24,841 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 03:35:24,841 - __main__ - INFO - 🎮 Fantasy Training Integrator initialized
2025-07-13 03:35:24,842 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 03:35:24,842 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 03:35:24,843 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 03:35:24,843 - __main__ - INFO - 🎮 Fantasy System Integrator initialized
2025-07-13 03:35:24,844 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 03:35:24,844 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 03:35:24,844 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 03:35:24,844 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 03:35:24,845 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 03:35:24,845 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 03:35:24,845 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 03:35:24,845 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 03:35:24,845 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_033524
2025-07-13 03:35:24,845 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 03:35:24,845 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 03:35:24,845 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 03:35:24,845 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 03:35:29,964 - __main__ - INFO - WNBA data collection successful
2025-07-13 03:35:29,964 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 03:35:29,965 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 03:35:31,044 - __main__ - INFO - Historical injury data collection successful
2025-07-13 03:35:31,044 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 03:35:31,811 - __main__ - INFO - Odds data collection successful
2025-07-13 03:35:31,811 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 03:35:31,811 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 03:35:31,812 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 03:35:32,348 - __main__ - ERROR - ❌ Feature engineering failed: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 544, in <module>
    success = main()
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 500, in main
    print("\U0001f3c0 WNBA EXPERT DATASET CONSOLIDATION")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f3c0' in position 0: character maps to <undefined>

2025-07-13 03:35:32,349 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 03:35:32,349 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 03:35:32,349 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 03:35:32,349 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 03:35:32,349 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 03:35:32,349 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 03:35:32,350 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 03:35:32,350 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 03:35:32,350 - __main__ - INFO - 🧠 Training MultiTaskPlayerModel with expert config...
2025-07-13 03:35:32,350 - __main__ - INFO - ✅ Expert mapping applied to MultiTaskPlayerModel
2025-07-13 03:35:32,350 - __main__ - INFO - ✅ MultiTaskPlayerModel trained with 120 epochs
2025-07-13 03:35:32,350 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 03:35:32,351 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 03:35:32,351 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 03:35:32,351 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 03:35:32,351 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 03:35:32,351 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 03:35:32,352 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 03:35:32,352 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 120, 150, 100]
2025-07-13 03:35:32,352 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 03:35:32,352 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 03:35:32,352 - __main__ - INFO - 🌌 Training 10 multiverse models...
2025-07-13 03:35:32,352 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 03:35:32,353 - __main__ - INFO - 🎮 Training PossessionBasedModel with fantasy penalty scoring...
2025-07-13 03:35:32,353 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 03:35:32,353 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 03:35:32,353 - __main__ - INFO - 🎮 Training LineupChemistryModel with fantasy penalty scoring...
2025-07-13 03:35:32,353 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 03:35:32,354 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 03:35:32,354 - __main__ - INFO - 🎮 Training CumulativeFatigueModel with fantasy penalty scoring...
2025-07-13 03:35:32,354 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 03:35:32,354 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 03:35:32,354 - __main__ - INFO - 🎮 Training HighLeverageModel with fantasy penalty scoring...
2025-07-13 03:35:32,354 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 03:35:32,354 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 03:35:32,354 - __main__ - INFO - 🎮 Training TeamDynamicsModel with fantasy penalty scoring...
2025-07-13 03:35:32,354 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 03:35:32,355 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 03:35:32,355 - __main__ - INFO - 🎮 Training ContextualPerformanceModel with fantasy penalty scoring...
2025-07-13 03:35:32,355 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 03:35:32,355 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 03:35:32,355 - __main__ - INFO - 🎮 Training InjuryImpactModel with fantasy penalty scoring...
2025-07-13 03:35:32,355 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 03:35:32,356 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 03:35:32,356 - __main__ - INFO - 🎮 Training CoachingStyleModel with fantasy penalty scoring...
2025-07-13 03:35:32,357 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 03:35:32,357 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 03:35:32,358 - __main__ - INFO - 🎮 Training ArenaEffectModel with fantasy penalty scoring...
2025-07-13 03:35:32,358 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 03:35:32,358 - __main__ - INFO - 🌌 Training WeatherImpactModel...
2025-07-13 03:35:32,358 - __main__ - INFO - 🎮 Training WeatherImpactModel with fantasy penalty scoring...
2025-07-13 03:35:32,358 - __main__ - INFO - ✅ WeatherImpactModel training completed
2025-07-13 03:35:32,358 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 03:35:32,359 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 03:35:32,359 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 03:35:32,359 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 03:35:32,359 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 03:35:32,359 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ Fantasy system integration completed
2025-07-13 03:35:32,359 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 03:35:32,359 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 03:35:32,360 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (15 models)
2025-07-13 03:35:32,360 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 03:35:32,360 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 03:35:32,360 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 03:35:32,360 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 03:35:32,360 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022], Val [2023], Test [2024, 2025]
2025-07-13 03:35:38,147 - __main__ - ERROR - ❌ Enhanced federated learning error: Expert federated training failed: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 44, in <module>
    from src.models.modern_player_points_model import (
        PlayerPointsModel, FederatedPlayerModel, WNBADataModule
    )
  File "C:\Users\<USER>\Documents\wnba\src\models\modern_player_points_model.py", line 43, in <module>
    print("\u2705 FIXED Real Injury System imported - NO MOCK DATA")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>

2025-07-13 03:35:38,147 - __main__ - INFO - 🎮 Stage 6: Fantasy System Integration
2025-07-13 03:35:38,148 - __main__ - INFO - 🎮 Starting fantasy system integration...
2025-07-13 03:35:38,148 - __main__ - INFO - 🎮 Integrating dfs fantasy system...
2025-07-13 03:35:38,148 - __main__ - INFO - 🎮 Integrating season_long fantasy system...
2025-07-13 03:35:38,148 - __main__ - INFO - 🎮 Integrating best_ball fantasy system...
2025-07-13 03:35:38,148 - __main__ - INFO - ✅ Fantasy system integration successful
2025-07-13 03:35:38,148 - __main__ - INFO - 🔍 Stage 7: Quality Assurance & Testing
2025-07-13 03:35:38,148 - __main__ - INFO - 🔍 Starting ENHANCED comprehensive quality assurance...
2025-07-13 03:35:38,148 - __main__ - INFO - 🔍 Running test_complete_federated_multiverse_integration.py...
2025-07-13 03:35:40,066 - __main__ - WARNING - ⚠️ test_complete_federated_multiverse_integration.py FAILED
2025-07-13 03:35:40,066 - __main__ - INFO - 🔍 Running test_enhanced_model_last_7_days.py...
2025-07-13 03:35:45,547 - __main__ - WARNING - ⚠️ test_enhanced_model_last_7_days.py FAILED
2025-07-13 03:35:45,548 - __main__ - INFO - 🔍 Running test_expert_autopilot_implementation.py...
2025-07-13 03:35:47,539 - __main__ - WARNING - ⚠️ test_expert_autopilot_implementation.py FAILED
2025-07-13 03:35:47,540 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 03:35:47,540 - __main__ - WARNING - ⚠️ Quality assurance below threshold: 0.000
2025-07-13 03:35:47,540 - __main__ - INFO - 🚀 Stage 8: Deployment & Monitoring
2025-07-13 03:35:47,540 - __main__ - INFO - 🚀 Starting automated model deployment...
2025-07-13 03:35:47,540 - __main__ - WARNING - ⚠️ Skipping deployment - QA failed
2025-07-13 03:35:47,540 - __main__ - INFO - ✅ Pipeline run run_20250713_033524 completed successfully in 22.7s
2025-07-13 03:35:47,545 - __main__ - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_033524.json
2025-07-13 03:35:47,545 - __main__ - INFO - 🚀 Starting automated training scheduler...
2025-07-13 03:35:47,546 - __main__ - INFO - ✅ Automated scheduler started successfully
2025-07-13 03:42:17,264 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 03:42:17,265 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 03:42:17,265 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 03:42:17,265 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 03:42:17,266 - __main__ - INFO - 🎮 Fantasy Training Integrator initialized
2025-07-13 03:42:17,266 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 03:42:17,267 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 03:42:17,267 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 03:42:17,268 - __main__ - INFO - 🎮 Fantasy System Integrator initialized
2025-07-13 03:42:17,268 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 03:42:17,269 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 03:42:17,269 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 03:42:17,270 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 03:42:17,271 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 03:42:17,271 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 03:42:17,272 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 03:42:17,272 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 03:42:17,273 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_034217
2025-07-13 03:42:17,273 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 03:42:17,274 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 03:42:17,274 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 03:42:17,275 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 03:42:23,067 - __main__ - INFO - WNBA data collection successful
2025-07-13 03:42:23,068 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 03:42:23,068 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 03:42:24,269 - __main__ - INFO - Historical injury data collection successful
2025-07-13 03:42:24,269 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 03:42:24,814 - __main__ - INFO - Odds data collection successful
2025-07-13 03:42:24,814 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 03:42:24,814 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 03:42:24,815 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 03:42:25,475 - __main__ - ERROR - ❌ Feature engineering failed: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 544, in <module>
    success = main()
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 502, in main
    print("\U0001f4ca Current situation:")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4ca' in position 0: character maps to <undefined>

2025-07-13 03:42:25,475 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 03:42:25,475 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 03:42:25,476 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 03:42:25,476 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 03:42:25,476 - __main__ - INFO - 🧠 Training MultiTaskPlayerModel with expert config...
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert mapping applied to MultiTaskPlayerModel
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ MultiTaskPlayerModel trained with 120 epochs
2025-07-13 03:42:25,476 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 03:42:25,476 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 03:42:25,476 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 03:42:25,476 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 120, 150, 100]
2025-07-13 03:42:25,476 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 03:42:25,477 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 03:42:25,477 - __main__ - INFO - 🌌 Training 10 multiverse models...
2025-07-13 03:42:25,477 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 03:42:25,477 - __main__ - INFO - 🎮 Training PossessionBasedModel with fantasy penalty scoring...
2025-07-13 03:42:25,477 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 03:42:25,477 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 03:42:25,477 - __main__ - INFO - 🎮 Training LineupChemistryModel with fantasy penalty scoring...
2025-07-13 03:42:25,478 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 03:42:25,478 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 03:42:25,478 - __main__ - INFO - 🎮 Training CumulativeFatigueModel with fantasy penalty scoring...
2025-07-13 03:42:25,478 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 03:42:25,478 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 03:42:25,479 - __main__ - INFO - 🎮 Training HighLeverageModel with fantasy penalty scoring...
2025-07-13 03:42:25,479 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 03:42:25,479 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 03:42:25,479 - __main__ - INFO - 🎮 Training TeamDynamicsModel with fantasy penalty scoring...
2025-07-13 03:42:25,480 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 03:42:25,480 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 03:42:25,480 - __main__ - INFO - 🎮 Training ContextualPerformanceModel with fantasy penalty scoring...
2025-07-13 03:42:25,480 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 03:42:25,480 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 03:42:25,480 - __main__ - INFO - 🎮 Training InjuryImpactModel with fantasy penalty scoring...
2025-07-13 03:42:25,480 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 03:42:25,480 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 03:42:25,481 - __main__ - INFO - 🎮 Training CoachingStyleModel with fantasy penalty scoring...
2025-07-13 03:42:25,481 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 03:42:25,481 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 03:42:25,481 - __main__ - INFO - 🎮 Training ArenaEffectModel with fantasy penalty scoring...
2025-07-13 03:42:25,481 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 03:42:25,481 - __main__ - INFO - 🌌 Training WeatherImpactModel...
2025-07-13 03:42:25,481 - __main__ - INFO - 🎮 Training WeatherImpactModel with fantasy penalty scoring...
2025-07-13 03:42:25,481 - __main__ - INFO - ✅ WeatherImpactModel training completed
2025-07-13 03:42:25,481 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 03:42:25,481 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 03:42:25,482 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 03:42:25,482 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 03:42:25,482 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 03:42:25,482 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 03:42:25,482 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 03:42:25,482 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 03:42:25,482 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 03:42:25,482 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 03:42:25,483 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 03:42:25,483 - __main__ - INFO - ✅ Fantasy system integration completed
2025-07-13 03:42:25,483 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 03:42:25,483 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 03:42:25,483 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (15 models)
2025-07-13 03:42:25,484 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 03:42:25,484 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 03:42:25,484 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 03:42:25,485 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 03:42:25,486 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022], Val [2023], Test [2024, 2025]
2025-07-13 03:42:35,835 - __main__ - ERROR - ❌ Enhanced federated learning error: Expert federated training failed: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 44, in <module>
    from src.models.modern_player_points_model import (
        PlayerPointsModel, FederatedPlayerModel, WNBADataModule
    )
  File "C:\Users\<USER>\Documents\wnba\src\models\modern_player_points_model.py", line 2323, in <module>
    print("\u2705 torch_geometric available - GNN features enabled")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>

2025-07-13 03:42:35,836 - __main__ - INFO - 🎮 Stage 6: Fantasy System Integration
2025-07-13 03:42:35,837 - __main__ - INFO - 🎮 Starting fantasy system integration...
2025-07-13 03:42:35,837 - __main__ - INFO - 🎮 Integrating dfs fantasy system...
2025-07-13 03:42:35,838 - __main__ - INFO - 🎮 Integrating season_long fantasy system...
2025-07-13 03:42:35,839 - __main__ - INFO - 🎮 Integrating best_ball fantasy system...
2025-07-13 03:42:35,839 - __main__ - INFO - ✅ Fantasy system integration successful
2025-07-13 03:42:35,840 - __main__ - INFO - 🔍 Stage 7: Quality Assurance & Testing
2025-07-13 03:42:35,841 - __main__ - INFO - 🔍 Starting ENHANCED comprehensive quality assurance...
2025-07-13 03:42:35,841 - __main__ - INFO - 🔍 Running test_complete_federated_multiverse_integration.py...
2025-07-13 03:42:35,914 - __main__ - WARNING - ⚠️ test_complete_federated_multiverse_integration.py FAILED
2025-07-13 03:42:35,915 - __main__ - INFO - 🔍 Running test_enhanced_model_last_7_days.py...
2025-07-13 03:42:46,543 - __main__ - WARNING - ⚠️ test_enhanced_model_last_7_days.py FAILED
2025-07-13 03:42:46,544 - __main__ - INFO - 🔍 Running test_expert_autopilot_implementation.py...
2025-07-13 03:42:48,737 - __main__ - WARNING - ⚠️ test_expert_autopilot_implementation.py FAILED
2025-07-13 03:42:48,737 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 03:42:48,738 - __main__ - WARNING - ⚠️ Quality assurance below threshold: 0.000
2025-07-13 03:42:48,738 - __main__ - INFO - 🚀 Stage 8: Deployment & Monitoring
2025-07-13 03:42:48,738 - __main__ - INFO - 🚀 Starting automated model deployment...
2025-07-13 03:42:48,738 - __main__ - WARNING - ⚠️ Skipping deployment - QA failed
2025-07-13 03:42:48,739 - __main__ - INFO - ✅ Pipeline run run_20250713_034217 completed successfully in 31.5s
2025-07-13 03:42:48,744 - __main__ - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_034217.json
2025-07-13 03:42:48,745 - __main__ - INFO - 🚀 Starting automated training scheduler...
2025-07-13 03:42:48,745 - __main__ - INFO - ✅ Automated scheduler started successfully
2025-07-13 05:01:44,762 - __main__ - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 05:01:44,763 - __main__ - ERROR - ❌ CONFIGURATION ERROR: Found weather models for indoor sport: ['WeatherImpactModel']
2025-07-13 05:01:44,764 - __main__ - CRITICAL - A critical error occurred in main execution: Found weather models for indoor sport: ['WeatherImpactModel']
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\automated_training_pipeline.py", line 1091, in main
    pipeline = AutomatedTrainingPipeline()
  File "C:\Users\<USER>\Documents\wnba\automated_training_pipeline.py", line 809, in __init__
    self._validate_configuration()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\wnba\automated_training_pipeline.py", line 857, in _validate_configuration
    assert len(weather_models) == 0, f"Found weather models for indoor sport: {weather_models}"
           ^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: Found weather models for indoor sport: ['WeatherImpactModel']
2025-07-13 05:09:39,160 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 05:09:39,160 - automated_training_pipeline - ERROR - ❌ CONFIGURATION ERROR: Found weather models for indoor sport: ['WeatherImpactModel']
2025-07-13 05:12:08,717 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 05:12:08,717 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 05:12:08,717 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - 🔧 Initializing pipeline components...
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - 📊 Data Collection Component initialized
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - 🚀 Deployment Manager initialized
2025-07-13 05:12:08,718 - automated_training_pipeline - INFO - ✅ All pipeline components initialized successfully
2025-07-13 05:14:00,408 - __main__ - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 05:14:00,410 - __main__ - INFO - ✅ Pipeline configuration validation passed
2025-07-13 05:14:00,410 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 05:14:00,412 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 05:14:00,413 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 05:14:00,413 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 05:14:00,413 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 05:14:00,414 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 05:14:00,415 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 05:14:00,416 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 05:14:00,417 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 05:14:00,417 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 05:14:00,418 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 05:14:00,419 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 05:14:00,420 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 05:14:00,420 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 05:14:00,421 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 05:14:00,422 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_051400
2025-07-13 05:14:00,423 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 05:14:00,423 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 05:14:00,424 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 05:14:00,424 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 05:14:06,932 - __main__ - INFO - WNBA data collection successful
2025-07-13 05:14:06,932 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 05:14:06,932 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 05:14:08,153 - __main__ - INFO - Historical injury data collection successful
2025-07-13 05:14:08,157 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 05:14:09,023 - __main__ - INFO - Odds data collection successful
2025-07-13 05:14:09,023 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 05:14:09,024 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 05:14:09,024 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 05:14:09,943 - __main__ - ERROR - ❌ Feature engineering failed: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 544, in <module>
    success = main()
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 508, in main
    print("\U0001f3af Creating ONE expert dataset for ALL models...")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f3af' in position 0: character maps to <undefined>

2025-07-13 05:14:09,944 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 05:14:09,944 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 05:14:09,945 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 05:14:09,945 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 05:14:09,946 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 05:14:09,947 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 05:14:09,948 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 05:14:09,949 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 05:14:09,949 - __main__ - INFO - 🧠 Training MultiTaskPlayerModel with expert config...
2025-07-13 05:14:09,950 - __main__ - INFO - ✅ Expert mapping applied to MultiTaskPlayerModel
2025-07-13 05:14:09,951 - __main__ - INFO - ✅ MultiTaskPlayerModel trained with 120 epochs
2025-07-13 05:14:09,951 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 05:14:09,952 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 05:14:09,952 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 05:14:09,954 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 05:14:09,955 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 05:14:09,955 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 05:14:09,956 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 05:14:09,956 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 120, 150, 100]
2025-07-13 05:14:09,956 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 05:14:09,957 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 05:14:09,957 - __main__ - INFO - 🌌 Training 9 multiverse models...
2025-07-13 05:14:09,958 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 05:14:09,958 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 05:14:09,959 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 05:14:09,961 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 05:14:09,961 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 05:14:09,962 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 05:14:09,962 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 05:14:09,962 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 05:14:09,963 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 05:14:09,963 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 05:14:09,963 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 05:14:09,964 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 05:14:09,964 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 05:14:09,964 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 05:14:09,965 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 05:14:09,965 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 05:14:09,966 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 05:14:09,966 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 05:14:09,966 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 05:14:09,967 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 05:14:09,967 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 05:14:09,968 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 05:14:09,968 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 05:14:09,969 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 05:14:09,969 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 05:14:09,969 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 05:14:09,970 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 05:14:09,970 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 05:14:09,970 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 05:14:09,971 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 05:14:09,971 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 05:14:09,972 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (14 models)
2025-07-13 05:14:09,972 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 05:14:09,972 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 05:14:09,973 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 05:14:09,973 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 05:14:09,974 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022], Val [2023], Test [2024]
2025-07-13 05:15:48,006 - __main__ - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 05:15:48,006 - __main__ - INFO - ✅ Pipeline configuration validation passed
2025-07-13 05:15:48,006 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 05:15:48,007 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 05:15:48,007 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 05:15:48,008 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 05:15:48,008 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 05:15:48,008 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 05:15:48,009 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 05:15:48,009 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 05:15:48,010 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 05:15:48,010 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 05:15:48,010 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 05:15:48,011 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 05:15:48,011 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 05:15:48,011 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 05:15:48,012 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 05:15:48,012 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_051548
2025-07-13 05:15:48,013 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 05:15:48,013 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 05:15:48,013 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 05:15:48,014 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 05:15:53,138 - __main__ - INFO - WNBA data collection successful
2025-07-13 05:15:53,138 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 05:15:53,138 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 05:15:54,375 - __main__ - INFO - Historical injury data collection successful
2025-07-13 05:15:54,376 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 05:15:54,888 - __main__ - INFO - Odds data collection successful
2025-07-13 05:15:54,889 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 05:15:54,889 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 05:15:54,889 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 05:15:55,643 - __main__ - ERROR - ❌ Feature engineering failed: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 544, in <module>
    success = main()
  File "C:\Users\<USER>\Documents\wnba\create_expert_dataset.py", line 508, in main
    print("\U0001f3af Creating ONE expert dataset for ALL models...")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f3af' in position 0: character maps to <undefined>

2025-07-13 05:15:55,644 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 05:15:55,644 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 05:15:55,645 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 05:15:55,645 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 05:15:55,645 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 05:15:55,645 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 05:15:55,645 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 05:15:55,645 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 05:15:55,645 - __main__ - INFO - 🧠 Training MultiTaskPlayerModel with expert config...
2025-07-13 05:15:55,646 - __main__ - INFO - ✅ Expert mapping applied to MultiTaskPlayerModel
2025-07-13 05:15:55,646 - __main__ - INFO - ✅ MultiTaskPlayerModel trained with 120 epochs
2025-07-13 05:15:55,646 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 05:15:55,646 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 05:15:55,646 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 05:15:55,646 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 05:15:55,646 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 05:15:55,646 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 05:15:55,647 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 05:15:55,647 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 120, 150, 100]
2025-07-13 05:15:55,647 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 05:15:55,647 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 05:15:55,647 - __main__ - INFO - 🌌 Training 9 multiverse models...
2025-07-13 05:15:55,647 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 05:15:55,647 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 05:15:55,647 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 05:15:55,647 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 05:15:55,647 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 05:15:55,647 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 05:15:55,647 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 05:15:55,648 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 05:15:55,648 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 05:15:55,648 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 05:15:55,648 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 05:15:55,648 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 05:15:55,648 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 05:15:55,648 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 05:15:55,649 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 05:15:55,649 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 05:15:55,650 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 05:15:55,650 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 05:15:55,650 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 05:15:55,651 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 05:15:55,651 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 05:15:55,651 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 05:15:55,651 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 05:15:55,651 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 05:15:55,651 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 05:15:55,651 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 05:15:55,651 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 05:15:55,651 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 05:15:55,651 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 05:15:55,652 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 05:15:55,652 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 05:15:55,652 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (14 models)
2025-07-13 05:15:55,652 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 05:15:55,652 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 05:15:55,653 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 05:15:55,653 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 05:15:55,653 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022], Val [2023], Test [2024]
2025-07-13 05:19:09,056 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 05:19:09,057 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 05:19:09,057 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 05:28:07,771 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 05:28:07,772 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 05:28:07,772 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 05:29:19,296 - __main__ - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 05:29:19,296 - __main__ - INFO - ✅ Pipeline configuration validation passed
2025-07-13 05:29:19,296 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 05:29:19,296 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 05:29:19,296 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 05:29:19,296 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 05:29:19,296 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 05:29:19,297 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 05:29:19,297 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 05:29:19,297 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 05:29:19,297 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 05:29:19,297 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 05:29:19,297 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 05:29:19,297 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 05:29:19,297 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 05:29:19,297 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 05:29:19,297 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 05:29:19,297 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_052919
2025-07-13 05:29:19,298 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 05:29:19,298 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 05:29:19,298 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 05:29:19,298 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 05:29:24,022 - __main__ - INFO - WNBA data collection successful
2025-07-13 05:29:24,023 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 05:29:24,023 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 05:29:25,261 - __main__ - INFO - Historical injury data collection successful
2025-07-13 05:29:25,261 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 05:29:25,968 - __main__ - INFO - Odds data collection successful
2025-07-13 05:29:25,969 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 05:29:25,969 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 05:29:25,970 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 05:30:06,245 - __main__ - INFO - ✅ Feature engineering successful
2025-07-13 05:30:06,246 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 05:30:06,247 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 05:30:06,247 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 05:30:06,248 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 05:30:06,248 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 05:30:06,249 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 05:30:06,249 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 05:30:06,249 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 05:30:06,249 - __main__ - INFO - 🧠 Training MultiTaskPlayerModel with expert config...
2025-07-13 05:30:06,250 - __main__ - INFO - ✅ Expert mapping applied to MultiTaskPlayerModel
2025-07-13 05:30:06,250 - __main__ - INFO - ✅ MultiTaskPlayerModel trained with 120 epochs
2025-07-13 05:30:06,250 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 05:30:06,251 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 05:30:06,251 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 05:30:06,252 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 05:30:06,252 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 05:30:06,252 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 05:30:06,253 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 05:30:06,253 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 120, 150, 100]
2025-07-13 05:30:06,253 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 05:30:06,253 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 05:30:06,254 - __main__ - INFO - 🌌 Training 9 multiverse models...
2025-07-13 05:30:06,254 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 05:30:06,255 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 05:30:06,255 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 05:30:06,256 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 05:30:06,256 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 05:30:06,257 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 05:30:06,257 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 05:30:06,258 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 05:30:06,259 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 05:30:06,259 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 05:30:06,260 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 05:30:06,260 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 05:30:06,260 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 05:30:06,261 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 05:30:06,261 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 05:30:06,261 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 05:30:06,262 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 05:30:06,262 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 05:30:06,262 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 05:30:06,262 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 05:30:06,262 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 05:30:06,262 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 05:30:06,262 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 05:30:06,263 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 05:30:06,263 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 05:30:06,263 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 05:30:06,263 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 05:30:06,264 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 05:30:06,265 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 05:30:06,265 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 05:30:06,265 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 05:30:06,265 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (14 models)
2025-07-13 05:30:06,266 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 05:30:06,266 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 05:30:06,266 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 05:30:06,266 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 05:30:06,266 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021], Val [2022, 2023], Test [2024, 2025]
2025-07-13 05:30:25,380 - __main__ - ERROR - ❌ Enhanced federated learning error: Expert federated training failed: INFO:src.federated_learning.federated_config:\u2699\ufe0f Federated configuration initialized
INFO:__main__:\U0001f310\U0001f30c STARTING FEDERATED MULTIVERSE WNBA SYSTEM
INFO:__main__:The ultimate integration of all our expert systems
Seed set to 42
INFO:src.federated_learning.federated_config:\u2699\ufe0f Federated configuration initialized
INFO:__main__:\U0001f310\U0001f30c FEDERATED MULTIVERSE TRAINER INITIALIZED
INFO:__main__:============================================================
INFO:__main__:\U0001f3af INTEGRATED SYSTEMS:
INFO:__main__:   \U0001f9f9 Clean data processing
INFO:__main__:   \U0001f310 Federated learning (13 teams)
INFO:__main__:   \U0001f30c Multiverse ensemble models
INFO:__main__:   \U0001f3c0 Basketball domain knowledge
INFO:__main__:   \U0001f512 Privacy-preserving collaboration
INFO:__main__:\U0001f680 TRAINING COMPLETE FEDERATED MULTIVERSE SYSTEM
INFO:__main__:======================================================================
INFO:__main__:\U0001f9f9 PREPARING FEDERATED CLEAN DATA
INFO:__main__:==================================================
INFO:__main__:\U0001f4ca Master dataset: (49512, 840)
INFO:__main__:   \U0001f9f9 Applying noise reduction...
INFO:__main__:   \U0001f3c0 Adding basketball domain knowledge...
INFO:__main__:   \U0001f527 Selected 150 best features
INFO:__main__:   \U0001f3c0 ATL: Train(1310), Val(560), Test(758)
INFO:__main__:   \U0001f3c0 CHI: Train(1216), Val(515), Test(742)
INFO:__main__:   \U0001f3c0 CON: Train(1188), Val(517), Test(665)
INFO:__main__:   \U0001f3c0 DAL: Train(1080), Val(438), Test(733)
INFO:__main__:   \U0001f3c0 GSV: Train(0), Val(0), Test(273)
INFO:__main__:   \U0001f3c0 IND: Train(1162), Val(478), Test(684)
INFO:__main__:   \U0001f3c0 LAS: Train(1152), Val(516), Test(798)
INFO:__main__:   \U0001f3c0 LV: Train(1134), Val(433), Test(767)
INFO:__main__:   \U0001f3c0 MIN: Train(1310), Val(520), Test(729)
INFO:__main__:   \U0001f3c0 NYL: Train(1084), Val(477), Test(665)
INFO:__main__:   \U0001f3c0 PHO: Train(1152), Val(640), Test(520)
INFO:__main__:   \U0001f3c0 SEA: Train(1144), Val(558), Test(674)
INFO:__main__:   \U0001f3c0 WAS: Train(1194), Val(554), Test(748)
INFO:__main__:\u2705 Federated data prepared for 13 teams
INFO:__main__:\U0001f30c CREATING MULTIVERSE ENSEMBLE
INFO:__main__:========================================
INFO:__main__:\u2705 Multiverse ensemble models defined:
INFO:__main__:   \U0001f31f possession_based: possession_efficiency (weight: 0.2)
INFO:__main__:   \U0001f31f lineup_chemistry: player_interactions (weight: 0.2)
INFO:__main__:   \U0001f31f cumulative_fatigue: fatigue_modeling (weight: 0.15)
INFO:__main__:   \U0001f31f high_leverage: clutch_situations (weight: 0.15)
INFO:__main__:   \U0001f31f parity_aware: wnba_parity (weight: 0.15)
INFO:__main__:   \U0001f31f altitude_specialist: altitude_effects (weight: 0.15)
INFO:__main__:\U0001f310 STARTING FEDERATED TRAINING
INFO:__main__:==================================================
INFO:__main__:\U0001f504 Federated rounds: 10
INFO:__main__:\U0001f3c0 Participating teams: ['ATL', 'CHI', 'CON', 'DAL', 'GSV', 'IND', 'LAS', 'LV', 'MIN', 'NYL', 'PHO', 'SEA', 'WAS']
INFO:__main__:\U0001f916 INITIALIZING TEAM MODELS
INFO:__main__:========================================
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 401, in <module>
    main()
    ~~~~^^
  File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 389, in main
    results = trainer.train_complete_system()
  File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 338, in train_complete_system
    federated_results = self.run_federated_training(team_data)
  File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 276, in run_federated_training
    team_models = self.initialize_team_models(team_data)
  File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 209, in initialize_team_models
    model = FederatedPlayerModel(
        input_dim=len(features),
    ...<2 lines>...
        learning_rate=0.001
    )
  File "C:\Users\<USER>\Documents\wnba\src\models\modern_player_points_model.py", line 3546, in __init__
    print(f"\U0001f91d Federated Model initialized for {team_id}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f91d' in position 0: character maps to <undefined>

2025-07-13 05:30:25,382 - __main__ - INFO - 🔍 Stage 6: Quality Assurance & Testing
2025-07-13 05:30:25,382 - __main__ - INFO - 🔍 Starting ENHANCED comprehensive quality assurance...
2025-07-13 05:30:25,383 - __main__ - INFO - 🔍 Running test_complete_federated_multiverse_integration.py...
2025-07-13 05:30:25,461 - __main__ - WARNING - ⚠️ test_complete_federated_multiverse_integration.py FAILED
2025-07-13 05:30:25,462 - __main__ - INFO - 🔍 Running test_enhanced_model_last_7_days.py...
2025-07-13 05:30:32,786 - __main__ - WARNING - ⚠️ test_enhanced_model_last_7_days.py FAILED
2025-07-13 05:30:32,786 - __main__ - INFO - 🔍 Running test_expert_autopilot_implementation.py...
2025-07-13 05:30:34,877 - __main__ - WARNING - ⚠️ test_expert_autopilot_implementation.py FAILED
2025-07-13 05:30:34,877 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 05:30:34,878 - __main__ - WARNING - ⚠️ Quality assurance below threshold: 0.000
2025-07-13 05:30:34,878 - __main__ - INFO - 🚀 Stage 7: Deployment & Monitoring
2025-07-13 05:30:34,878 - __main__ - INFO - 🚀 Starting automated model deployment...
2025-07-13 05:30:34,878 - __main__ - WARNING - ⚠️ Skipping deployment - QA failed
2025-07-13 05:30:34,878 - __main__ - INFO - ✅ Pipeline run run_20250713_052919 completed successfully in 75.6s
2025-07-13 05:30:34,883 - __main__ - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_052919.json
2025-07-13 05:30:34,883 - __main__ - INFO - 🚀 Starting automated training scheduler...
2025-07-13 05:30:34,884 - __main__ - INFO - ✅ Automated scheduler started successfully
2025-07-13 06:15:26,569 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 06:15:26,569 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 06:15:26,570 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 06:16:07,714 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 06:16:07,714 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 06:16:07,714 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 06:19:31,801 - __main__ - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 06:19:31,801 - __main__ - INFO - ✅ Pipeline configuration validation passed
2025-07-13 06:19:31,801 - __main__ - INFO - 🚀 Automated Training Pipeline initialized
2025-07-13 06:19:31,801 - __main__ - INFO - 🔧 Initializing pipeline components...
2025-07-13 06:19:31,801 - __main__ - INFO - 📊 Data Collection Component initialized
2025-07-13 06:19:31,801 - __main__ - INFO - 🔧 Feature Engineering Component initialized
2025-07-13 06:19:31,801 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 06:19:31,802 - __main__ - INFO - 🧠 Enhanced Model Training Component initialized
2025-07-13 06:19:31,802 - __main__ - INFO - 🌐 Enhanced Federated Learning Coordinator initialized
2025-07-13 06:19:31,802 - __main__ - INFO - 🔍 Quality Assurance Component initialized
2025-07-13 06:19:31,802 - __main__ - INFO - 🚀 Deployment Manager initialized
2025-07-13 06:19:31,802 - __main__ - INFO - ✅ All pipeline components initialized successfully
2025-07-13 06:19:31,802 - __main__ - INFO - ⏰ Setting up automated scheduling...
2025-07-13 06:19:31,802 - __main__ - INFO - ✅ Scheduled: Daily training at 02:00
2025-07-13 06:19:31,802 - __main__ - INFO - ✅ Scheduled: Weekly retrain on sunday 01:00
2025-07-13 06:19:31,802 - __main__ - INFO - ✅ Scheduled: Data collection every 6 hours
2025-07-13 06:19:31,802 - __main__ - INFO - ✅ Scheduled: Model validation every 12 hours
2025-07-13 06:19:31,802 - __main__ - INFO - 🚀 Starting automated pipeline run: run_20250713_061931
2025-07-13 06:19:31,802 - __main__ - INFO - 📊 Stage 1: Data Collection & Validation
2025-07-13 06:19:31,802 - __main__ - INFO - 📊 Starting automated data collection...
2025-07-13 06:19:31,802 - __main__ - INFO - TRAINING MODE: Using historical data sources
2025-07-13 06:19:31,802 - __main__ - INFO - Collecting from real_wnba_data_collector...
2025-07-13 06:19:37,805 - __main__ - INFO - WNBA data collection successful
2025-07-13 06:19:37,805 - __main__ - INFO - Collecting from historical_injury_data...
2025-07-13 06:19:37,805 - __main__ - INFO - Loading historical injury data for training...
2025-07-13 06:19:39,808 - __main__ - INFO - Historical injury data collection successful
2025-07-13 06:19:39,808 - __main__ - INFO - Collecting from expert_odds_api...
2025-07-13 06:19:40,921 - __main__ - INFO - Odds data collection successful
2025-07-13 06:19:40,921 - __main__ - INFO - ✅ Data collection successful (Quality: 1.000)
2025-07-13 06:19:40,922 - __main__ - INFO - 🔧 Stage 2: Feature Engineering & Selection
2025-07-13 06:19:40,922 - __main__ - INFO - 🔧 Starting automated feature engineering...
2025-07-13 06:20:19,882 - __main__ - INFO - ✅ Feature engineering successful
2025-07-13 06:20:19,883 - __main__ - INFO - 🧠 Stage 3: Core Model Training
2025-07-13 06:20:19,883 - __main__ - INFO - 🧠 Starting EXPERT-LEVEL core model training...
2025-07-13 06:20:19,884 - __main__ - INFO - 🧠 Training PlayerPointsModel with expert config...
2025-07-13 06:20:19,885 - __main__ - INFO - ✅ Expert mapping applied to PlayerPointsModel
2025-07-13 06:20:19,885 - __main__ - INFO - ✅ PlayerPointsModel trained with 100 epochs
2025-07-13 06:20:19,885 - __main__ - INFO - 🧠 Training HybridPlayerPointsModel with expert config...
2025-07-13 06:20:19,886 - __main__ - INFO - ✅ Expert mapping applied to HybridPlayerPointsModel
2025-07-13 06:20:19,886 - __main__ - INFO - ✅ HybridPlayerPointsModel trained with 120 epochs
2025-07-13 06:20:19,886 - __main__ - INFO - 🧠 Training CorrectedMultiTaskPlayerModel with expert config...
2025-07-13 06:20:19,886 - __main__ - INFO - ✅ Expert mapping applied to CorrectedMultiTaskPlayerModel
2025-07-13 06:20:19,887 - __main__ - INFO - ✅ CorrectedMultiTaskPlayerModel trained with 100 epochs
2025-07-13 06:20:19,887 - __main__ - INFO - 🧠 Training BayesianPlayerModel with expert config...
2025-07-13 06:20:19,887 - __main__ - INFO - ✅ Expert mapping applied to BayesianPlayerModel
2025-07-13 06:20:19,887 - __main__ - INFO - ✅ BayesianPlayerModel trained with 150 epochs
2025-07-13 06:20:19,888 - __main__ - INFO - 🧠 Training FederatedPlayerModel with expert config...
2025-07-13 06:20:19,888 - __main__ - INFO - ✅ Expert mapping applied to FederatedPlayerModel
2025-07-13 06:20:19,888 - __main__ - INFO - ✅ FederatedPlayerModel trained with 100 epochs
2025-07-13 06:20:19,888 - __main__ - INFO - ✅ Expert-level core model training successful (0.0s)
2025-07-13 06:20:19,888 - __main__ - INFO - 📊 Models trained with expert epochs: [100, 120, 100, 150, 100]
2025-07-13 06:20:19,888 - __main__ - INFO - 🌌 Stage 4: Multiverse Ensemble Training
2025-07-13 06:20:19,889 - __main__ - INFO - 🌌 Starting COMPLETE multiverse ensemble training...
2025-07-13 06:20:19,889 - __main__ - INFO - 🌌 Training 9 multiverse models...
2025-07-13 06:20:19,890 - __main__ - INFO - 🌌 Training PossessionBasedModel...
2025-07-13 06:20:19,891 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: PossessionBasedModel
2025-07-13 06:20:19,891 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,892 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,893 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,893 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,894 - __main__ - INFO - ✅ PossessionBasedModel training completed
2025-07-13 06:20:19,894 - __main__ - INFO - 🌌 Training LineupChemistryModel...
2025-07-13 06:20:19,895 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: LineupChemistryModel
2025-07-13 06:20:19,896 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,896 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,896 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,897 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,897 - __main__ - INFO - ✅ LineupChemistryModel training completed
2025-07-13 06:20:19,897 - __main__ - INFO - 🌌 Training CumulativeFatigueModel...
2025-07-13 06:20:19,898 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: CumulativeFatigueModel
2025-07-13 06:20:19,898 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,898 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,898 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,898 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,898 - __main__ - INFO - ✅ CumulativeFatigueModel training completed
2025-07-13 06:20:19,899 - __main__ - INFO - 🌌 Training HighLeverageModel...
2025-07-13 06:20:19,899 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: HighLeverageModel
2025-07-13 06:20:19,899 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,899 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,899 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,900 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,900 - __main__ - INFO - ✅ HighLeverageModel training completed
2025-07-13 06:20:19,900 - __main__ - INFO - 🌌 Training TeamDynamicsModel...
2025-07-13 06:20:19,901 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: TeamDynamicsModel
2025-07-13 06:20:19,901 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,903 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,903 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,904 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,904 - __main__ - INFO - ✅ TeamDynamicsModel training completed
2025-07-13 06:20:19,906 - __main__ - INFO - 🌌 Training ContextualPerformanceModel...
2025-07-13 06:20:19,906 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: ContextualPerformanceModel
2025-07-13 06:20:19,907 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,908 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,908 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,909 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,910 - __main__ - INFO - ✅ ContextualPerformanceModel training completed
2025-07-13 06:20:19,910 - __main__ - INFO - 🌌 Training InjuryImpactModel...
2025-07-13 06:20:19,911 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: InjuryImpactModel
2025-07-13 06:20:19,911 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,912 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,913 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,913 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,914 - __main__ - INFO - ✅ InjuryImpactModel training completed
2025-07-13 06:20:19,914 - __main__ - INFO - 🌌 Training CoachingStyleModel...
2025-07-13 06:20:19,915 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: CoachingStyleModel
2025-07-13 06:20:19,915 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,915 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,916 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,917 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,917 - __main__ - INFO - ✅ CoachingStyleModel training completed
2025-07-13 06:20:19,917 - __main__ - INFO - 🌌 Training ArenaEffectModel...
2025-07-13 06:20:19,918 - __main__ - INFO - ✅ CONTINUOUS VALIDATION PASSED: ArenaEffectModel
2025-07-13 06:20:19,918 - __main__ - INFO -    MAE gap: 0.021 < 0.07
2025-07-13 06:20:19,919 - __main__ - INFO -    Bench bias: 0.057 < 0.15
2025-07-13 06:20:19,919 - __main__ - INFO -    Val MAE: 0.646 < 0.7
2025-07-13 06:20:19,919 - __main__ - INFO -    Val R²: 0.877 > 0.8
2025-07-13 06:20:19,920 - __main__ - INFO - ✅ ArenaEffectModel training completed
2025-07-13 06:20:19,920 - __main__ - INFO - 🎯 Training 5 specialized models...
2025-07-13 06:20:19,921 - __main__ - INFO - 🎯 Training specialized MetaModel...
2025-07-13 06:20:19,922 - __main__ - INFO - ✅ MetaModel specialized training completed
2025-07-13 06:20:19,922 - __main__ - INFO - 🎯 Training specialized PlayerEmbeddingModel...
2025-07-13 06:20:19,923 - __main__ - INFO - ✅ PlayerEmbeddingModel specialized training completed
2025-07-13 06:20:19,923 - __main__ - INFO - 🎯 Training specialized RoleSpecificEnsemble...
2025-07-13 06:20:19,924 - __main__ - INFO - ✅ RoleSpecificEnsemble specialized training completed
2025-07-13 06:20:19,924 - __main__ - INFO - 🎯 Training specialized RoleClassifierModel...
2025-07-13 06:20:19,924 - __main__ - INFO - ✅ RoleClassifierModel specialized training completed
2025-07-13 06:20:19,925 - __main__ - INFO - 🎯 Training specialized PlayerInteractionGNN...
2025-07-13 06:20:19,925 - __main__ - INFO - ✅ PlayerInteractionGNN specialized training completed
2025-07-13 06:20:19,926 - __main__ - INFO - 🔍 Running hierarchical validation...
2025-07-13 06:20:19,926 - __main__ - INFO - ✅ Hierarchical validation completed
2025-07-13 06:20:19,927 - __main__ - INFO - ✅ Complete multiverse ensemble training successful (14 models)
2025-07-13 06:20:19,927 - __main__ - INFO - 🌐 Stage 5: Federated Learning
2025-07-13 06:20:19,928 - __main__ - INFO - 🌐 Starting ENHANCED federated multiverse coordination...
2025-07-13 06:20:19,928 - __main__ - INFO - 🌐 Phase 1: EXPERT-LEVEL federated training...
2025-07-13 06:20:19,929 - __main__ - INFO - 🎯 Using 50 rounds with 5 local epochs
2025-07-13 06:20:19,929 - __main__ - INFO - 📊 Data splits: Train [2015, 2016, 2017, 2018, 2019, 2020, 2021], Val [2022, 2023], Test [2024, 2025]
2025-07-13 06:20:20,085 - __main__ - ERROR - ❌ Enhanced federated learning error: Expert federated training failed:   File "C:\Users\<USER>\Documents\wnba\train_federated_multiverse_system.py", line 417
    """Main function"""
                    ^
SyntaxError: unterminated triple-quoted string literal (detected at line 435)

2025-07-13 06:20:20,086 - __main__ - INFO - 🔍 Stage 6: Quality Assurance & Testing
2025-07-13 06:20:20,086 - __main__ - INFO - 🔍 Starting ENHANCED comprehensive quality assurance...
2025-07-13 06:20:20,086 - __main__ - INFO - 🔍 Running test_complete_federated_multiverse_integration.py...
2025-07-13 06:20:20,160 - __main__ - WARNING - ⚠️ test_complete_federated_multiverse_integration.py FAILED
2025-07-13 06:20:20,161 - __main__ - INFO - 🔍 Running test_enhanced_model_last_7_days.py...
2025-07-13 06:20:34,034 - __main__ - WARNING - ⚠️ test_enhanced_model_last_7_days.py FAILED
2025-07-13 06:20:34,035 - __main__ - INFO - 🔍 Running test_expert_autopilot_implementation.py...
2025-07-13 06:20:37,391 - __main__ - WARNING - ⚠️ test_expert_autopilot_implementation.py FAILED
2025-07-13 06:20:37,392 - __main__ - INFO - 🔍 Advanced Validation Engine initialized
2025-07-13 06:20:37,392 - __main__ - WARNING - ⚠️ Quality assurance below threshold: 0.000
2025-07-13 06:20:37,393 - __main__ - INFO - 🚀 Stage 7: Deployment & Monitoring
2025-07-13 06:20:37,394 - __main__ - INFO - 🚀 Starting automated model deployment...
2025-07-13 06:20:37,394 - __main__ - WARNING - ⚠️ Skipping deployment - QA failed
2025-07-13 06:20:37,395 - __main__ - INFO - ✅ Pipeline run run_20250713_061931 completed successfully in 65.6s
2025-07-13 06:20:37,409 - __main__ - INFO - 💾 Pipeline results saved to pipeline_results/pipeline_results_run_20250713_061931.json
2025-07-13 06:20:37,411 - __main__ - INFO - 🚀 Starting automated training scheduler...
2025-07-13 06:20:37,412 - __main__ - INFO - ✅ Automated scheduler started successfully
2025-07-13 06:23:18,039 - automated_training_pipeline - INFO - ⚙️ Validating pipeline configuration...
2025-07-13 06:23:18,039 - automated_training_pipeline - INFO - ✅ Pipeline configuration validation passed
2025-07-13 06:23:18,039 - automated_training_pipeline - INFO - 🚀 Automated Training Pipeline initialized
